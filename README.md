# akey - Spring Boot多模块企业级管理系统

## 📋 项目概述

akey是一个基于Spring Boot 3.x的多模块企业级管理系统，采用现代化的框架+业务架构设计，将框架层和业务层清晰分离，便于开发、维护和扩展。项目遵循最佳实践，提供完整的用户管理、菜单管理等基础功能。

## 🏗️ 项目架构

### 架构设计思想

本项目采用**框架+业务分离**的架构模式，相比传统的分层架构具有以下优势：
- **框架层独立**: 将技术框架配置与业务逻辑分离，便于框架升级和技术栈替换
- **业务聚合**: 将相关的数据访问、业务逻辑、DTO等聚合到核心业务模块，提升内聚性
- **模块清晰**: 每个模块职责单一，依赖关系清晰，便于团队协作

### 项目结构

```
akey/                                    # 父项目根目录
├── README.md                           # 项目说明文档
├── pom.xml                             # 父项目聚合配置
├── .gitignore                          # Git忽略文件配置
├── .gitattributes                      # Git属性配置
├── .cursorignore                       # Cursor编辑器忽略文件
├── logs/                               # 日志文件目录
├── docs/                               # 项目文档目录
│
├── akey-common/                        # 🔧 公共基础模块
│   ├── pom.xml                         # 公共模块配置
│   └── src/main/java/com/akey/common/
│       ├── util/                       # 工具类包
│       ├── constant/                   # 常量定义包
│       ├── exception/                  # 异常定义包
│       └── enums/                      # 枚举类型包
│
├── akey-framework-core/                # ⚙️ 核心框架模块
│   ├── pom.xml                         # 框架核心模块配置
│   └── src/main/java/com/akey/framework/core/
│       ├── config/                     # 核心配置类
│       │   ├── MybatisPlusConfig.java  # MyBatis-Plus配置
│       │   └── DruidConfig.java        # 数据源配置
│       ├── handler/                    # 框架处理器
│       └── interceptor/                # 拦截器
│
├── akey-framework-web/                 # 🌐 Web框架模块
│   ├── pom.xml                         # Web框架模块配置
│   └── src/main/java/com/akey/framework/web/
│       ├── config/                     # Web配置类
│       ├── exception/                  # 全局异常处理
│       ├── filter/                     # Web过滤器
│       └── interceptor/                # Web拦截器
│
├── akey-core/                          # 🎯 核心业务模块
│   ├── pom.xml                         # 核心业务模块配置
│   └── src/main/java/com/akey/core/
│       ├── dao/                        # 数据访问层
│       │   ├── entity/                 # 数据实体类
│       │   └── mapper/                 # MyBatis映射器
│       ├── service/                    # 业务接口定义
│       ├── service/impl/               # 业务逻辑实现
│       ├── dto/                        # 数据传输对象
│       └── vo/                         # 视图对象
│
└── akey-web/                           # 🚀 Web应用模块
    ├── pom.xml                         # Web应用模块配置
    ├── src/main/java/com/akey/web/
    │   ├── AkeyApplication.java        # Spring Boot启动类
    │   └── controller/                 # REST控制器
    ├── src/main/resources/
    │   ├── application.yml             # 主配置文件
    │   ├── application-dev.yml         # 开发环境配置
    │   ├── application-test.yml        # 测试环境配置
    │   ├── application-prod.yml        # 生产环境配置
    │   ├── logback-spring.xml          # 日志配置
    │   └── sql/                        # SQL脚本目录
    └── src/test/                       # 测试代码
```

## 📦 模块详细说明

### 1. akey (父项目)
- **作用**: 聚合所有子模块，统一管理依赖版本和构建配置
- **类型**: POM项目，不包含业务代码
- **特性**: 
  - 继承Spring Boot父项目
  - 统一依赖版本管理
  - 统一插件配置
  - 多环境配置支持

### 2. akey-common (公共基础模块)
- **作用**: 提供项目中通用的基础组件和工具
- **包含内容**:
  - 工具类集合 (util)
  - 全局常量定义 (constant)
  - 自定义异常类 (exception)
  - 枚举类型定义 (enums)
  - 通用数据类型
- **依赖**: 仅依赖Lombok和Jackson注解
- **特点**: 被所有其他模块依赖，保持最小化依赖

### 3. akey-framework-core (核心框架模块)
- **作用**: 提供核心技术框架的配置和基础能力
- **包含内容**:
  - MyBatis-Plus配置和扩展
  - 数据库连接池配置(Druid)
  - 数据库相关的通用配置
  - 核心框架处理器和拦截器
- **依赖**: akey-common + Spring Boot + MyBatis-Plus + MySQL
- **特点**: 封装底层技术框架，为业务层提供统一的数据访问能力

### 4. akey-framework-web (Web框架模块)
- **作用**: 提供Web层的框架配置和通用功能
- **包含内容**:
  - 全局异常处理机制
  - Web通用配置
  - 请求/响应拦截器
  - 参数验证配置
  - Web安全配置
- **依赖**: akey-common + Spring Boot Web + Validation
- **特点**: 封装Web层技术框架，提供统一的Web处理能力

### 5. akey-core (核心业务模块)
- **作用**: 系统的核心业务功能载体，整合数据访问和业务逻辑
- **包含内容**:
  - 数据实体类 (entity) - 对应数据库表结构
  - 数据访问层 (mapper) - MyBatis映射器接口
  - 业务接口定义 (service) - 服务接口规范
  - 业务逻辑实现 (service/impl) - 核心业务逻辑
  - 数据传输对象 (dto) - 业务数据传输
  - 视图对象 (vo) - 前端展示数据
- **业务功能**:
  - 用户管理 (用户类型、用户信息、权限控制)
  - 菜单管理 (菜单结构、权限关联)
  - 系统基础功能
- **依赖**: akey-framework-core
- **特点**: 业务功能聚合，内聚性强，是系统核心

### 6. akey-web (Web应用模块)
- **作用**: 系统的Web入口，提供REST API和应用启动
- **包含内容**:
  - Spring Boot启动类
  - REST控制器 (controller) - 接口层
  - 配置文件管理
  - 静态资源
- **依赖**: akey-core + akey-framework-web + 测试依赖
- **特点**: 应用程序入口点，可打包为可执行JAR

## 🔗 模块依赖关系

### 依赖层次图
```
                ┌─────────────┐
                │  akey-web   │ ← Web应用入口 (Controller + 启动类)
                └─────┬───────┘
                      │ 依赖
                ┌─────▼───────┐
                │  akey-core  │ ← 核心业务层 (Entity + Service + DTO)
                └─────┬───────┘
                      │ 依赖
        ┌─────────────┼─────────────┐
        │             │             │
┌───────▼───────┐     │     ┌───────▼──────┐
│akey-framework │     │     │akey-framework│
│    -core      │     │     │    -web      │
└───────┬───────┘     │     └───────┬──────┘
        │             │             │
        │        ┌────▼────┐        │
        │        │  akey   │        │
        │        │ -common │        │
        │        └────┬────┘        │
        │             │             │
        └─────────────┼─────────────┘
                      ▼
                 基础公共组件
```

### 依赖关系详述
- **akey-web** → 依赖 akey-core、akey-framework-web
- **akey-core** → 依赖 akey-framework-core
- **akey-framework-core** → 依赖 akey-common + 数据库相关依赖
- **akey-framework-web** → 依赖 akey-common + Web相关依赖
- **akey-common** → 仅依赖Lombok等基础工具

## 🛠️ 技术栈

### 核心框架
- **Spring Boot**: 3.5.3 - 应用框架
- **MyBatis-Plus**: 3.5.7 - ORM框架，简化数据访问
- **Spring Security**: 权限控制框架(规划中)

### 数据库相关
- **MySQL**: 8.2.0 - 主数据库
- **Druid**: 1.2.25 - 数据库连接池，提供监控功能
- **Redis**: 缓存和会话存储，支持分布式锁

### 工具库
- **Lombok**: 1.18.30 - 简化实体类代码
- **Fastjson2**: 2.0.57 - JSON序列化工具
- **Jackson**: JSON注解支持

### 开发工具
- **Java**: 17 - 编程语言
- **Maven**: 构建工具
- **JUnit**: 测试框架

### 部署环境
- 支持多环境配置 (dev/test/prod)
- 日志配置 (Logback)
- 打包为可执行JAR

## 🚀 快速开始

### 环境要求
- **Java**: 17+
- **Maven**: 3.6+
- **MySQL**: 8.0+
- **IDE**: IntelliJ IDEA / Eclipse (推荐IDEA)

### 数据库准备
```sql
-- 创建数据库
CREATE DATABASE akey DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 导入初始化脚本
-- 脚本位置: akey-web/src/main/resources/sql/
```

### 配置文件
修改 `akey-web/src/main/resources/application-dev.yml` 中的数据库连接信息：
```yaml
spring:
  datasource:
    druid:
      url: *****************************************************************************************************
      username: your_username
      password: your_password
```

### 编译和运行

#### 1. 编译项目
```bash
# 进入项目根目录
cd akey

# 清理并编译所有模块
mvn clean compile

# 打包项目
mvn clean package -DskipTests
```

#### 2. 运行项目
```bash
# 方式一: 使用Maven插件运行 (开发模式)
cd akey-web
mvn spring-boot:run

# 方式二: 运行可执行JAR (生产模式)
java -jar akey-web/target/akey-web-0.0.1-SNAPSHOT.jar

# 方式三: 指定环境运行
java -jar akey-web/target/akey-web-0.0.1-SNAPSHOT.jar --spring.profiles.active=prod
```

#### 3. 验证运行
启动成功后，应用默认运行在 `http://localhost:8080`

### 开发指南

#### 新增业务功能
1. 在 `akey-core/dao/entity` 中定义实体类
2. 在 `akey-core/dao/mapper` 中创建MyBatis映射器
3. 在 `akey-core/service` 中定义业务接口
4. 在 `akey-core/service/impl` 中实现业务逻辑
5. 在 `akey-core/dto` 中定义数据传输对象
6. 在 `akey-web/controller` 中创建REST接口

#### 数据库操作
- 使用MyBatis-Plus提供的BaseMapper进行CRUD操作
- 复杂查询使用自定义SQL或QueryWrapper
- 事务管理使用@Transactional注解

## 📚 业务功能

### 用户管理模块
- **用户类型管理**: 支持多种用户类型，内置超级管理员类型
- **用户账户管理**: 用户注册、登录、密码管理、账户锁定
- **权限控制**: 基于用户类型的权限分配
- **安全特性**: 谷歌验证、登录记录、IP追踪

### 菜单管理模块
- **菜单结构管理**: 支持多级菜单结构
- **权限关联**: 菜单与用户类型权限关联
- **动态菜单**: 根据用户权限动态生成菜单

### Redis缓存模块
- **多种数据类型**: 支持String、Hash、List、Set等Redis数据类型
- **缓存工具**: 提供高级缓存操作，支持缓存穿透防护
- **分布式锁**: 基于Redis实现的分布式锁机制
- **Spring Cache**: 集成Spring Cache注解支持
- **会话管理**: 用户会话信息缓存
- **统计计数**: 支持各种计数器功能

## 🔧 配置说明

### 多环境配置
- `application.yml`: 主配置文件
- `application-dev.yml`: 开发环境配置
- `application-test.yml`: 测试环境配置
- `application-prod.yml`: 生产环境配置

### 日志配置
项目使用Logback进行日志管理，配置文件: `logback-spring.xml`
- 日志文件存储在 `logs/` 目录
- 支持按日期滚动
- 不同环境不同日志级别

## 🤝 开发规范

### 代码规范
- 遵循阿里巴巴Java开发规范
- 使用Lombok简化代码
- 详细的方法和类注释
- 统一的命名规范

### 数据库规范
- 表名、字段名使用下划线命名
- 必须有主键和基础字段(创建时间、更新时间等)
- 合理设计索引

### API规范
- RESTful API设计
- 统一的响应格式
- 完善的参数验证
- 详细的错误信息

## 📋 TODO

- [ ] 集成SaToken实现权限控制
- [x] 集成Redis实现缓存和会话管理
- [x] 实现分布式锁功能
- [ ] 添加API文档生成(Springdoc OpenAPI)
- [ ] 完善单元测试和集成测试
- [ ] 添加代码质量检查工具
- [ ] 实现分布式事务
- [ ] 性能监控和链路追踪

## 📄 许可证

本项目基于 [MIT 许可证](LICENSE) 开源。

---

**项目维护者**: Akey团队
**最后更新**: 2025年7月