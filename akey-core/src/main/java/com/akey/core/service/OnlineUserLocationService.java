package com.akey.core.service;

/**
 * 在线用户位置信息服务接口
 * 
 * <p>提供用户登录后的地理位置信息获取和存储功能</p>
 * <p>支持异步处理，避免影响登录性能</p>
 * 
 * <AUTHOR>
 * @since 2025-08-02
 */
public interface OnlineUserLocationService {

    /**
     * 异步获取并存储用户登录位置信息
     * 
     * <p>在用户登录后异步调用此方法</p>
     * <p>通过IP地址解析地理位置信息并存储在用户会话中</p>
     * 
     * @param token 用户Token值
     * @param clientIp 客户端IP地址
     */
    void fetchAndStoreLocationAsync(String token, String clientIp);

    /**
     * 从会话中获取用户的位置信息
     * 
     * @param loginId 用户登录ID
     * @return 位置信息字符串，如果不存在则返回null
     */
    String getLocationFromSession(String loginId);

    /**
     * 从会话中获取用户的IP地址
     * 
     * @param loginId 用户登录ID
     * @return IP地址字符串，如果不存在则返回null
     */
    String getClientIpFromSession(String loginId);

    /**
     * 手动更新用户会话中的位置信息
     * 
     * @param loginId 用户登录ID
     * @param clientIp 客户端IP地址
     * @param location 位置信息
     */
    void updateSessionLocation(String loginId, String clientIp, String location);
}
