package com.akey.core.service.impl;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaFoxUtil;
import com.akey.common.constant.SystemConstant;
import com.akey.common.util.Md5Util;
import com.akey.core.dao.entity.User;
import com.akey.core.dao.service.UserService;
import com.akey.core.dto.BatchKickoutDTO;
import com.akey.core.dto.OnlineUserQueryDTO;
import com.akey.core.service.OnlineUserService;
import com.akey.core.vo.OnlineUserPageVO;
import com.akey.core.vo.OnlineUserVO;
import com.akey.core.vo.UserTerminalVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 在线用户管理服务实现类
 * 
 * <p>基于Sa-Token框架实现在线用户管理功能</p>
 * <p>提供用户会话查询、管理、踢出等核心功能</p>
 * <p>支持多设备登录管理和批量操作</p>
 * 
 * <AUTHOR>
 * @since 2025-08-02
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OnlineUserServiceImpl implements OnlineUserService {

    private final UserService userService;

    @Override
    public OnlineUserPageVO getOnlineUsers(OnlineUserQueryDTO queryDTO) {
        log.info("查询在线用户列表, queryDTO: {}", queryDTO);
        
        try {
            // 获取所有已登录的会话ID
            List<String> sessionIdList = StpUtil.searchSessionId("", 0, -1, false);
            log.debug("获取到会话ID数量: {}", sessionIdList.size());
            
            // 转换为在线用户VO列表
            List<OnlineUserVO> allOnlineUsers = new ArrayList<>();
            for (String sessionId : sessionIdList) {
                try {
                    OnlineUserVO onlineUser = convertToOnlineUserVO(sessionId);
                    if (onlineUser != null) {
                        allOnlineUsers.add(onlineUser);
                    }
                } catch (Exception e) {
                    log.warn("转换会话ID为在线用户失败, sessionId: {}, error: {}", sessionId, e.getMessage());
                }
            }
            
            // 应用查询条件筛选
            List<OnlineUserVO> filteredUsers = applyQueryFilters(allOnlineUsers, queryDTO);
            
            // 排序
            sortOnlineUsers(filteredUsers, queryDTO.getSortType());
            
            // 分页处理
            OnlineUserPageVO pageResult = applyPagination(filteredUsers, queryDTO);
            
            // 设置统计信息
            pageResult.setStatistics(calculateStatistics(allOnlineUsers));
            
            log.info("查询在线用户列表成功, 总数: {}, 当前页数量: {}", 
                    pageResult.getTotal(), pageResult.getCurrentPageSize());
            
            return pageResult;
            
        } catch (Exception e) {
            log.error("查询在线用户列表失败, queryDTO: {}, error: {}", queryDTO, e.getMessage(), e);
            // 返回空结果而不是抛出异常
            return new OnlineUserPageVO(queryDTO.getCurrent(), queryDTO.getSize(), 0L, new ArrayList<>());
        }
    }



    @Override
    public boolean forceLogout(String loginId, String deviceType) {
        log.info("强制注销用户, loginId: {}, deviceType: {}", loginId, deviceType);
        
        try {
            if (StringUtils.hasText(deviceType)) {
                // 注销指定设备类型
                StpUtil.logout(loginId, deviceType);
            } else {
                // 注销所有设备
                StpUtil.logout(loginId);
            }
            
            log.info("强制注销用户成功, loginId: {}, deviceType: {}", loginId, deviceType);
            return true;
            
        } catch (Exception e) {
            log.error("强制注销用户失败, loginId: {}, deviceType: {}, error: {}", 
                    loginId, deviceType, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean kickoutUser(String loginId, String deviceType) {
        log.info("踢出用户, loginId: {}, deviceType: {}", loginId, deviceType);
        
        try {
            if (StringUtils.hasText(deviceType)) {
                // 踢出指定设备类型
                StpUtil.kickout(loginId, deviceType);
            } else {
                // 踢出所有设备
                StpUtil.kickout(loginId);
            }
            
            log.info("踢出用户成功, loginId: {}, deviceType: {}", loginId, deviceType);
            return true;
            
        } catch (Exception e) {
            log.error("踢出用户失败, loginId: {}, deviceType: {}, error: {}", 
                    loginId, deviceType, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public int batchKickout(BatchKickoutDTO batchDTO) {
        log.info("批量踢出用户, batchDTO: {}", batchDTO);
        
        int successCount = 0;
        List<String> failedUsers = new ArrayList<>();
        
        for (String loginId : batchDTO.getLoginIds()) {
            try {
                boolean success;
                if (batchDTO.getOperationType() == BatchKickoutDTO.KickoutType.LOGOUT) {
                    success = forceLogout(loginId, batchDTO.getDeviceType());
                } else {
                    success = kickoutUser(loginId, batchDTO.getDeviceType());
                }
                
                if (success) {
                    successCount++;
                } else {
                    failedUsers.add(loginId);
                }
                
            } catch (Exception e) {
                log.warn("批量踢出用户失败, loginId: {}, error: {}", loginId, e.getMessage());
                failedUsers.add(loginId);
            }
        }
        
        if (!failedUsers.isEmpty()) {
            log.warn("批量踢出部分用户失败, 失败用户: {}", failedUsers);
        }
        
        log.info("批量踢出用户完成, 总数: {}, 成功: {}, 失败: {}", 
                batchDTO.getLoginIds().size(), successCount, failedUsers.size());
        
        return successCount;
    }

    @Override
    public boolean kickoutByToken(String tokenValue) {
        log.info("根据Token踢出用户, tokenValue: {}", maskToken(tokenValue));

        try {
            StpUtil.kickoutByTokenValue(tokenValue);
            log.info("根据Token踢出用户成功, tokenValue: {}", maskToken(tokenValue));
            return true;

        } catch (Exception e) {
            log.error("根据Token踢出用户失败, tokenValue: {}, error: {}",
                    maskToken(tokenValue), e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean kickoutByTokenMd5(String tokenMd5) {
        log.info("根据Token MD5踢出用户, tokenMd5: {}", tokenMd5);

        try {
            // 获取所有已登录的会话ID
            List<String> sessionIdList = StpUtil.searchSessionId("", 0, -1, false);

            for (String sessionId : sessionIdList) {
                try {
                    // 获取会话中的终端信息
                    SaSession session = StpUtil.getSessionBySessionId(sessionId);
                    if (session == null) {
                        continue;
                    }

                    List<cn.dev33.satoken.session.SaTerminalInfo> terminalList = session.terminalListCopy();

                    // 遍历所有终端，查找匹配的Token MD5
                    for (cn.dev33.satoken.session.SaTerminalInfo terminal : terminalList) {
                        String currentTokenMd5 = Md5Util.tokenMd5(terminal.getTokenValue());

                        if (tokenMd5.equals(currentTokenMd5)) {
                            // 找到匹配的Token，执行踢出操作
                            StpUtil.kickoutByTokenValue(terminal.getTokenValue());
                            log.info("根据Token MD5踢出用户成功, tokenMd5: {}, tokenValue: {}",
                                    tokenMd5, maskToken(terminal.getTokenValue()));
                            return true;
                        }
                    }

                } catch (Exception e) {
                    log.debug("处理会话失败, sessionId: {}, error: {}", sessionId, e.getMessage());
                }
            }

            log.warn("未找到匹配的Token, tokenMd5: {}", tokenMd5);
            return false;

        } catch (Exception e) {
            log.error("根据Token MD5踢出用户失败, tokenMd5: {}, error: {}",
                    tokenMd5, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean replaceUser(String loginId, String deviceType) {
        log.info("顶替用户会话, loginId: {}, deviceType: {}", loginId, deviceType);
        
        try {
            if (StringUtils.hasText(deviceType)) {
                // 顶替指定设备类型
                StpUtil.replaced(loginId, deviceType);
            } else {
                // 顶替所有设备
                StpUtil.replaced(loginId);
            }
            
            log.info("顶替用户会话成功, loginId: {}, deviceType: {}", loginId, deviceType);
            return true;
            
        } catch (Exception e) {
            log.error("顶替用户会话失败, loginId: {}, deviceType: {}, error: {}", 
                    loginId, deviceType, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean replaceByToken(String tokenValue) {
        log.info("根据Token顶替用户会话, tokenValue: {}", maskToken(tokenValue));

        try {
            StpUtil.replacedByTokenValue(tokenValue);
            log.info("根据Token顶替用户会话成功, tokenValue: {}", maskToken(tokenValue));
            return true;

        } catch (Exception e) {
            log.error("根据Token顶替用户会话失败, tokenValue: {}, error: {}",
                    maskToken(tokenValue), e.getMessage(), e);
            return false;
        }
    }

    @Override
    public OnlineUserPageVO.OnlineUserStatistics getOnlineUserStatistics() {
        log.debug("获取在线用户统计信息");

        try {
            // 获取所有在线用户
            List<String> sessionIdList = StpUtil.searchSessionId("", 0, -1, false);
            List<OnlineUserVO> allOnlineUsers = new ArrayList<>();

            for (String sessionId : sessionIdList) {
                try {
                    OnlineUserVO onlineUser = convertToOnlineUserVO(sessionId);
                    if (onlineUser != null) {
                        allOnlineUsers.add(onlineUser);
                    }
                } catch (Exception e) {
                    log.debug("转换会话ID统计信息失败, sessionId: {}", sessionId);
                }
            }

            return calculateStatistics(allOnlineUsers);

        } catch (Exception e) {
            log.error("获取在线用户统计信息失败, error: {}", e.getMessage(), e);
            return new OnlineUserPageVO.OnlineUserStatistics();
        }
    }

    @Override
    public boolean isUserOnline(String loginId) {
        try {
            // 通过检查用户是否有活跃的终端来判断是否在线
            List<cn.dev33.satoken.session.SaTerminalInfo> terminalList = StpUtil.getTerminalListByLoginId(loginId);
            return !terminalList.isEmpty();
        } catch (Exception e) {
            log.debug("检查用户在线状态失败, loginId: {}, error: {}", loginId, e.getMessage());
            return false;
        }
    }

    @Override
    public int getUserDeviceCount(String loginId) {
        try {
            List<cn.dev33.satoken.session.SaTerminalInfo> terminalList = StpUtil.getTerminalListByLoginId(loginId);
            return terminalList.size();
        } catch (Exception e) {
            log.debug("获取用户设备数量失败, loginId: {}, error: {}", loginId, e.getMessage());
            return 0;
        }
    }

    /**
     * 将会话ID转换为在线用户VO对象
     *
     * @param sessionId 会话ID
     * @return 在线用户VO对象
     */
    private OnlineUserVO convertToOnlineUserVO(String sessionId) {
        try {
            // 根据会话ID获取SaSession对象
            SaSession session = StpUtil.getSessionBySessionId(sessionId);
            if (session == null) {
                return null;
            }

            // 从会话中提取登录ID
            String loginId = extractLoginIdFromSessionId(sessionId);
            if (!StringUtils.hasText(loginId)) {
                return null;
            }

            // 创建在线用户VO对象
            OnlineUserVO onlineUserVO = new OnlineUserVO();
            onlineUserVO.setLoginId(loginId);
            onlineUserVO.setSessionId(sessionId);

            // 获取用户基本信息
            User user = userService.getUserById(loginId);
            if (user != null) {
                onlineUserVO.setUsername(user.getUsername());
                onlineUserVO.setUserTypeId(user.getUserTypeId());
            }

            // 获取终端信息列表
            List<cn.dev33.satoken.session.SaTerminalInfo> terminalList = session.terminalListCopy();
            onlineUserVO.setDeviceCount(terminalList.size());

            if (!terminalList.isEmpty()) {
                // 获取最近登录的终端信息
                cn.dev33.satoken.session.SaTerminalInfo latestTerminal = terminalList.get(terminalList.size() - 1);
                onlineUserVO.setPrimaryDeviceType(latestTerminal.getDeviceType());
                onlineUserVO.setLoginTime(convertTimestampToLocalDateTime(latestTerminal.getCreateTime()));

                // 转换所有终端信息为VO对象
                List<UserTerminalVO> terminalVOList = new ArrayList<>();
                String currentToken = StpUtil.getTokenValue();

                for (cn.dev33.satoken.session.SaTerminalInfo terminal : terminalList) {
                    UserTerminalVO terminalVO = convertToUserTerminalVO(terminal, currentToken, loginId);
                    terminalVOList.add(terminalVO);
                }

                // 按登录时间倒序排列
                terminalVOList.sort((t1, t2) -> t2.getLoginTime().compareTo(t1.getLoginTime()));
                onlineUserVO.setTerminals(terminalVOList);

                // 设置其他信息
                onlineUserVO.setOnlineStatus(OnlineUserVO.OnlineStatus.ONLINE);

                // 计算在线时长
                onlineUserVO.calculateOnlineDuration();
            }

            // 设置IP跟位置信息
            // 登录IP
            String clientIp = session.getString(OnlineUserLocationServiceImpl.SESSION_CLIENT_IP_KEY);
            onlineUserVO.setPrimaryClientIp(clientIp);
            // 登录地点
            String location = session.getString(OnlineUserLocationServiceImpl.SESSION_LOCATION_KEY);
            onlineUserVO.setLocation(location);

            // 获取会话超时时间
            try {
                long timeout = StpUtil.getSessionTimeout();
                onlineUserVO.setSessionTimeout(timeout);
            } catch (Exception e) {
                onlineUserVO.setSessionTimeout(-1L);
            }

            return onlineUserVO;

        } catch (Exception e) {
            log.debug("转换会话ID为在线用户VO失败, sessionId: {}, error: {}", sessionId, e.getMessage());
            return null;
        }
    }

    /**
     * 将Sa-Token终端信息转换为用户终端VO对象
     *
     * @param terminal Sa-Token终端信息
     * @param currentToken 当前Token值
     * @param loginId 用户登录ID
     * @return 用户终端VO对象
     */
    private UserTerminalVO convertToUserTerminalVO(cn.dev33.satoken.session.SaTerminalInfo terminal, String currentToken, String loginId) {
        UserTerminalVO terminalVO = new UserTerminalVO();

        terminalVO.setIndex(terminal.getIndex());
        terminalVO.setDeviceType(terminal.getDeviceType());
        terminalVO.setTokenValue(terminal.getTokenValue());
        terminalVO.setLoginTime(convertTimestampToLocalDateTime(terminal.getCreateTime()));
        terminalVO.setDeviceId(terminal.getDeviceId());

        // 计算并设置Token的MD5哈希值
        String tokenMd5 = Md5Util.tokenMd5(terminal.getTokenValue());
        terminalVO.setTokenMd5(tokenMd5);

        // 判断是否为当前会话
        terminalVO.setIsCurrent(Objects.equals(terminal.getTokenValue(), currentToken));

        // 获取Token超时时间
        try {
            long timeout = StpUtil.getTokenTimeout();
            terminalVO.setTokenTimeout(timeout);
        } catch (Exception e) {
            terminalVO.setTokenTimeout(-1L);
        }

        // 获取登录IP以及地点
        SaSession session = StpUtil.getTokenSessionByToken(terminal.getTokenValue());
        if(null != session){
            // 登录IP
            String clientIp = session.getString(OnlineUserLocationServiceImpl.SESSION_CLIENT_IP_KEY);
            terminalVO.setClientIp(clientIp);
            // 登录地点
            String location = session.getString(OnlineUserLocationServiceImpl.SESSION_LOCATION_KEY);
            terminalVO.setLocation(location);
        }

        // 获取扩展信息
        try {
            Object extraInfo = terminal.getExtra("extraInfo");
            if (extraInfo != null) {
                terminalVO.setExtraInfo(extraInfo.toString());
            }
        } catch (Exception e) {
            // 忽略扩展信息获取失败
        }

        // 计算在线时长
        terminalVO.calculateOnlineDuration();

        // 对敏感信息进行脱敏处理
        terminalVO.maskTokenValue();

        return terminalVO;
    }

    /**
     * 应用查询条件筛选
     *
     * @param allUsers 所有在线用户列表
     * @param queryDTO 查询条件
     * @return 筛选后的用户列表
     */
    private List<OnlineUserVO> applyQueryFilters(List<OnlineUserVO> allUsers, OnlineUserQueryDTO queryDTO) {
        return allUsers.stream()
                .filter(user -> matchesKeyword(user, queryDTO.getKeyword()))
                .filter(user -> matchesDeviceType(user, queryDTO.getDeviceType()))
                .collect(Collectors.toList());
    }

    /**
     * 检查用户是否匹配关键词
     *
     * @param user 在线用户
     * @param keyword 关键词
     * @return 是否匹配
     */
    private boolean matchesKeyword(OnlineUserVO user, String keyword) {
        if (!StringUtils.hasText(keyword)) {
            return true;
        }

        String lowerKeyword = keyword.toLowerCase();
        return (user.getLoginId() != null && user.getLoginId().toLowerCase().contains(lowerKeyword)) ||
               (user.getUsername() != null && user.getUsername().toLowerCase().contains(lowerKeyword));
    }

    /**
     * 检查用户是否匹配设备类型
     *
     * @param user 在线用户
     * @param deviceType 设备类型
     * @return 是否匹配
     */
    private boolean matchesDeviceType(OnlineUserVO user, String deviceType) {
        if (!StringUtils.hasText(deviceType)) {
            return true;
        }

        return Objects.equals(user.getPrimaryDeviceType(), deviceType);
    }

    /**
     * 对在线用户列表进行排序
     *
     * @param users 用户列表
     * @param sortType 排序类型，true为升序，false为降序
     */
    private void sortOnlineUsers(List<OnlineUserVO> users, Boolean sortType) {
        if (sortType != null && sortType) {
            // 升序排列
            users.sort(Comparator.comparing(OnlineUserVO::getLoginTime, Comparator.nullsLast(Comparator.naturalOrder())));
        } else {
            // 降序排列（默认）
            users.sort(Comparator.comparing(OnlineUserVO::getLoginTime, Comparator.nullsLast(Comparator.reverseOrder())));
        }
    }

    /**
     * 应用分页处理
     *
     * @param filteredUsers 筛选后的用户列表
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    private OnlineUserPageVO applyPagination(List<OnlineUserVO> filteredUsers, OnlineUserQueryDTO queryDTO) {
        long total = filteredUsers.size();
        long current = queryDTO.getCurrent();
        long size = queryDTO.getSize();

        // 计算分页
        int startIndex = (int) ((current - 1) * size);
        int endIndex = (int) Math.min(startIndex + size, total);

        List<OnlineUserVO> pageData;
        if (startIndex >= total) {
            pageData = new ArrayList<>();
        } else {
            pageData = filteredUsers.subList(startIndex, endIndex);
        }

        return new OnlineUserPageVO(current, size, total, pageData);
    }

    /**
     * 计算在线用户统计信息
     *
     * @param allUsers 所有在线用户列表
     * @return 统计信息
     */
    private OnlineUserPageVO.OnlineUserStatistics calculateStatistics(List<OnlineUserVO> allUsers) {
        OnlineUserPageVO.OnlineUserStatistics statistics = new OnlineUserPageVO.OnlineUserStatistics();

        statistics.setTotalOnlineUsers((long) allUsers.size());

        // 从所有用户的终端信息中统计设备类型
        Map<String, Long> deviceTypeCount = new HashMap<>();

        for (OnlineUserVO user : allUsers) {
            try {
                // 获取用户的所有终端信息
                List<cn.dev33.satoken.session.SaTerminalInfo> terminalList = StpUtil.getTerminalListByLoginId(user.getLoginId());
                for (cn.dev33.satoken.session.SaTerminalInfo terminal : terminalList) {
                    String deviceType = terminal.getDeviceType();
                    if (deviceType != null) {
                        deviceTypeCount.merge(deviceType, 1L, Long::sum);
                    }
                }
            } catch (Exception e) {
                log.debug("获取用户终端信息失败, loginId: {}", user.getLoginId());
            }
        }

        // 设置动态设备类型统计
        statistics.setDeviceTypeStatistics(deviceTypeCount);



        // 多设备登录用户统计
        long multiDeviceUsers = allUsers.stream()
                .filter(user -> user.getDeviceCount() != null && user.getDeviceCount() > 1)
                .count();
        statistics.setMultiDeviceUsers(multiDeviceUsers);

        // 计算平均在线时长
        OptionalDouble averageDuration = allUsers.stream()
                .filter(user -> user.getOnlineDuration() != null)
                .mapToLong(OnlineUserVO::getOnlineDuration)
                .average();
        statistics.setAverageOnlineDuration(averageDuration.isPresent() ? (long) averageDuration.getAsDouble() : 0L);

        return statistics;
    }

    /**
     * 从会话ID中提取登录ID
     *
     * @param sessionId 会话ID
     * @return 登录ID
     */
    private String extractLoginIdFromSessionId(String sessionId) {
        try {
            // Sa-Token的会话ID格式通常为：satoken:login:session:loginId
            if (sessionId.contains(":")) {
                String[] parts = sessionId.split(":");
                if (parts.length >= 4) {
                    return parts[parts.length - 1];
                }
            }
            return sessionId;
        } catch (Exception e) {
            log.debug("从会话ID提取登录ID失败, sessionId: {}", sessionId);
            return sessionId;
        }
    }

    /**
     * 将时间戳转换为LocalDateTime
     *
     * @param timestamp 时间戳（毫秒）
     * @return LocalDateTime对象
     */
    private LocalDateTime convertTimestampToLocalDateTime(long timestamp) {
        try {
            return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
        } catch (Exception e) {
            return LocalDateTime.now();
        }
    }

    /**
     * 对Token值进行脱敏处理
     *
     * @param tokenValue Token值
     * @return 脱敏后的Token值
     */
    private String maskToken(String tokenValue) {
        if (tokenValue == null || tokenValue.length() <= 10) {
            return "***";
        }

        String prefix = tokenValue.substring(0, 6);
        String suffix = tokenValue.substring(tokenValue.length() - 4);
        return prefix + "***" + suffix;
    }
}
