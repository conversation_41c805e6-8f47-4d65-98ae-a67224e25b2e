package com.akey.core.service.impl;

import cn.dev33.satoken.context.mock.SaTokenContextMockUtil;
import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import com.akey.common.service.AddressResolveService;
import com.akey.core.service.OnlineUserLocationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 在线用户位置信息服务实现类
 * 
 * <p>基于Sa-Token会话管理实现用户位置信息的异步获取和存储</p>
 * <p>使用SaTokenContextMockUtil模拟登录上下文进行操作</p>
 * 
 * <AUTHOR>
 * @since 2025-08-02
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OnlineUserLocationServiceImpl implements OnlineUserLocationService {

    private final AddressResolveService addressResolveService;

    /**
     * 会话中存储客户端IP的键名
     */
    public static final String SESSION_CLIENT_IP_KEY = "clientIp";

    /**
     * 会话中存储地理位置的键名
     */
    public static final String SESSION_LOCATION_KEY = "location";

    /**
     * 会话中存储用户代理的键名
     */
    private static final String SESSION_USER_AGENT_KEY = "userAgent";

    /**
     * 会话中存储登录时间的键名
     */
    private static final String SESSION_LOGIN_TIME_KEY = "loginTime";

    @Override
    @Async("loginLogExecutor")
    public void fetchAndStoreLocationAsync(String token, String clientIp) {
        if (!StringUtils.hasText(token)) {
            log.warn("Token为空，无法获取位置信息");
            return;
        }

        if (!StringUtils.hasText(clientIp)) {
            log.warn("客户端IP为空，无法获取位置信息");
            return;
        }

        try {
            log.debug("开始异步获取用户位置信息, token: {}, clientIp: {}", maskToken(token), clientIp);

            // 使用SaTokenContextMockUtil模拟登录上下文
            SaTokenContextMockUtil.setMockContext(() -> {
                try {
                    // 设置Token到存储中
                    StpUtil.setTokenValueToStorage(token);

                    // 检查用户是否已登录
                    if (!StpUtil.isLogin()) {
                        log.warn("用户未登录或Token无效，无法存储位置信息, token: {}", maskToken(token));
                        return;
                    }

                    // 获取用户登录ID
                    String loginId = StpUtil.getLoginIdAsString();
                    log.debug("获取到用户登录ID: {}", loginId);

                    // 获取用户会话
                    SaSession session = StpUtil.getTokenSessionByToken(token);
                    if (session == null) {
                        log.warn("无法获取用户会话，loginId: {}", loginId);
                        return;
                    }

                    // 异步解析IP地址的地理位置
                    String location = resolveLocationByIp(clientIp);
                    // 存储登录信息
                    setSessionLoginInfo(session, clientIp, location);
                    setSessionLoginInfo(StpUtil.getSession(), clientIp, location);

                    log.info("用户位置信息存储成功, loginId: {}, clientIp: {}, location: {}", 
                            loginId, clientIp, location);

                } catch (Exception e) {
                    log.error("异步获取用户位置信息失败, token: {}, clientIp: {}, error: {}", 
                            maskToken(token), clientIp, e.getMessage(), e);
                }
            });

        } catch (Exception e) {
            log.error("异步获取用户位置信息异常, token: {}, clientIp: {}, error: {}", 
                    maskToken(token), clientIp, e.getMessage(), e);
        }
    }

    @Override
    public String getLocationFromSession(String loginId) {
        if (!StringUtils.hasText(loginId)) {
            return null;
        }

        try {
            // 获取指定用户的会话
            SaSession session = StpUtil.getSessionByLoginId(loginId);
            if (session != null) {
                Object location = session.get(SESSION_LOCATION_KEY);
                return location != null ? location.toString() : null;
            }
        } catch (Exception e) {
            log.debug("从会话获取位置信息失败, loginId: {}, error: {}", loginId, e.getMessage());
        }

        return null;
    }

    @Override
    public String getClientIpFromSession(String loginId) {
        if (!StringUtils.hasText(loginId)) {
            return null;
        }

        try {
            // 获取指定用户的会话
            SaSession session = StpUtil.getSessionByLoginId(loginId);
            if (session != null) {
                Object clientIp = session.get(SESSION_CLIENT_IP_KEY);
                return clientIp != null ? clientIp.toString() : null;
            }
        } catch (Exception e) {
            log.debug("从会话获取客户端IP失败, loginId: {}, error: {}", loginId, e.getMessage());
        }

        return null;
    }

    @Override
    public void updateSessionLocation(String loginId, String clientIp, String location) {
        if (!StringUtils.hasText(loginId)) {
            log.warn("用户登录ID为空，无法更新会话位置信息");
            return;
        }

        try {
            // 获取指定用户的会话
            SaSession session = StpUtil.getSessionByLoginId(loginId);
            if (session != null) {
                if (StringUtils.hasText(clientIp)) {
                    session.set(SESSION_CLIENT_IP_KEY, clientIp);
                }
                if (StringUtils.hasText(location)) {
                    session.set(SESSION_LOCATION_KEY, location);
                }

                log.debug("更新用户会话位置信息成功, loginId: {}, clientIp: {}, location: {}", 
                        loginId, clientIp, location);
            } else {
                log.warn("无法获取用户会话，无法更新位置信息, loginId: {}", loginId);
            }
        } catch (Exception e) {
            log.error("更新用户会话位置信息失败, loginId: {}, error: {}", loginId, e.getMessage(), e);
        }
    }

    /**
     * 通过IP地址解析地理位置信息
     * 
     * @param clientIp 客户端IP地址
     * @return 地理位置信息字符串
     */
    private String resolveLocationByIp(String clientIp) {
        if (!StringUtils.hasText(clientIp)) {
            return null;
        }

        try {
            long startTime = System.currentTimeMillis();
            String location = addressResolveService.getLocationByIP(clientIp);
            long endTime = System.currentTimeMillis();

            log.debug("IP地址解析完成, ip: {}, location: {}, 耗时: {}ms", 
                    clientIp, location, endTime - startTime);

            return location;
        } catch (Exception e) {
            log.warn("IP地址解析失败, ip: {}, error: {}", clientIp, e.getMessage());
            return null;
        }
    }

    /**
     * 对Token值进行脱敏处理
     * 
     * @param token Token值
     * @return 脱敏后的Token值
     */
    private String maskToken(String token) {
        if (token == null || token.length() <= 10) {
            return "***";
        }

        String prefix = token.substring(0, 6);
        String suffix = token.substring(token.length() - 4);
        return prefix + "***" + suffix;
    }

    /**
     * 设置登录IP以及位置信息
     * @param session session
     * @param clientIp IP
     * @param location 位置信息
     */
    private void setSessionLoginInfo(SaSession session, String clientIp, String location){
        try {
            // 存储IP地址和位置信息到会话中
            session.set(SESSION_CLIENT_IP_KEY, clientIp);
            if (StringUtils.hasText(location)) {
                session.set(SESSION_LOCATION_KEY, location);
            }
            // 存储登录时间
            session.set(SESSION_LOGIN_TIME_KEY, System.currentTimeMillis());
        }catch (Exception e){
            log.error("存储登录信息失败, error: {}", e.getMessage(), e);
        }
    }
}
