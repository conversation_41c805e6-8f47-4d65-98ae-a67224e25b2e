package com.akey.core.service;

import com.akey.core.dto.BatchKickoutDTO;
import com.akey.core.dto.OnlineUserQueryDTO;
import com.akey.core.vo.OnlineUserPageVO;
import com.akey.core.vo.OnlineUserVO;
import com.akey.core.vo.UserTerminalVO;

import java.util.List;

/**
 * 在线用户管理服务接口
 * 
 * <p>提供在线用户查询、管理、踢出等功能</p>
 * <p>基于Sa-Token框架实现用户会话管理</p>
 * <p>支持多设备登录管理和批量操作</p>
 * 
 * <AUTHOR>
 * @since 2025-08-02
 */
public interface OnlineUserService {

    /**
     * 分页查询在线用户
     * 
     * <p>根据查询条件获取在线用户列表</p>
     * <p>支持关键词搜索、设备类型筛选、分页查询等功能</p>
     * <p>返回结果包含用户基本信息、登录状态、设备信息等</p>
     * 
     * @param queryDTO 查询条件DTO
     * @return 分页查询结果，包含在线用户列表和统计信息
     */
    OnlineUserPageVO getOnlineUsers(OnlineUserQueryDTO queryDTO);



    /**
     * 强制注销用户
     * 
     * <p>强制指定用户下线，完全清除Token信息</p>
     * <p>用户后续访问将提示"Token无效"</p>
     * <p>支持指定设备类型进行精确注销</p>
     * 
     * @param loginId 用户登录ID
     * @param deviceType 设备类型，为空时注销所有设备
     * @return 操作是否成功
     */
    boolean forceLogout(String loginId, String deviceType);

    /**
     * 踢出用户
     * 
     * <p>将指定用户踢下线，不清除Token信息</p>
     * <p>Token被标记为"已被踢下线"状态</p>
     * <p>用户后续访问将提示"Token已被踢下线"</p>
     * 
     * @param loginId 用户登录ID
     * @param deviceType 设备类型，为空时踢出所有设备
     * @return 操作是否成功
     */
    boolean kickoutUser(String loginId, String deviceType);

    /**
     * 批量踢出用户
     * 
     * <p>根据用户ID列表批量执行踢出操作</p>
     * <p>支持强制注销和踢人下线两种操作类型</p>
     * <p>支持按设备类型进行批量操作</p>
     * 
     * @param batchDTO 批量操作参数DTO
     * @return 成功踢出的用户数量
     */
    int batchKickout(BatchKickoutDTO batchDTO);

    /**
     * 根据Token踢出用户
     *
     * <p>根据指定的Token值踢出对应的用户会话</p>
     * <p>适用于精确控制特定会话的场景</p>
     *
     * @param tokenValue Token值
     * @return 操作是否成功
     */
    boolean kickoutByToken(String tokenValue);

    /**
     * 根据Token的MD5哈希值踢出用户
     *
     * <p>根据Token的MD5哈希值匹配并踢出对应的用户会话</p>
     * <p>安全且精确的踢出方式，避免Token泄露</p>
     *
     * @param tokenMd5 Token的MD5哈希值
     * @return 操作是否成功
     */
    boolean kickoutByTokenMd5(String tokenMd5);

    /**
     * 顶替用户会话
     * 
     * <p>在多设备登录时顶替旧会话</p>
     * <p>通常由Sa-Token框架自动调用</p>
     * 
     * @param loginId 用户登录ID
     * @param deviceType 设备类型，为空时顶替所有设备
     * @return 操作是否成功
     */
    boolean replaceUser(String loginId, String deviceType);

    /**
     * 根据Token顶替用户会话
     * 
     * <p>根据指定的Token值顶替对应的用户会话</p>
     * 
     * @param tokenValue Token值
     * @return 操作是否成功
     */
    boolean replaceByToken(String tokenValue);

    /**
     * 获取在线用户统计信息
     * 
     * <p>统计当前系统中在线用户的各项数据</p>
     * <p>包括总数、设备类型分布、用户类型分布等</p>
     * 
     * @return 在线用户统计信息
     */
    OnlineUserPageVO.OnlineUserStatistics getOnlineUserStatistics();

    /**
     * 检查用户是否在线
     * 
     * <p>检查指定用户是否处于在线状态</p>
     * 
     * @param loginId 用户登录ID
     * @return 用户是否在线
     */
    boolean isUserOnline(String loginId);

    /**
     * 获取用户在线设备数量
     * 
     * <p>统计指定用户当前在线的设备数量</p>
     * 
     * @param loginId 用户登录ID
     * @return 在线设备数量
     */
    int getUserDeviceCount(String loginId);
}
