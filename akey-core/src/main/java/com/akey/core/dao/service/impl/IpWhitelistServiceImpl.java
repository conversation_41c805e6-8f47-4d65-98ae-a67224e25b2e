package com.akey.core.dao.service.impl;

import com.akey.common.enums.EnableStatusEnum;
import com.akey.common.enums.IpWhitelistTypeEnum;
import com.akey.core.dao.entity.IpWhitelist;
import com.akey.core.dao.mapper.IpWhitelistMapper;
import com.akey.core.dao.service.IpWhitelistService;
import com.akey.core.vo.IpWhitelistWithUserVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.util.CollectionUtils;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.List;
import java.util.regex.Pattern;

/**
 * IP白名单Service实现类
 *
 * <p>提供IP白名单相关的业务操作实现</p>
 * <p>继承ServiceImpl，获得基础CRUD功能</p>
 *
 * <p>主要功能实现：</p>
 * <ul>
 *   <li>IP白名单验证（支持单IP和CIDR格式）</li>
 *   <li>分页查询白名单列表</li>
 *   <li>添加、更新、删除白名单</li>
 *   <li>批量操作支持</li>
 *   <li>用户白名单管理</li>
 *   <li>IP地址格式验证</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IpWhitelistServiceImpl extends ServiceImpl<IpWhitelistMapper, IpWhitelist> implements IpWhitelistService {

    private final IpWhitelistMapper ipWhitelistMapper;

    /**
     * 分页查询IP白名单列表
     *
     * <p>通过连表查询获取IP白名单和关联的用户信息</p>
     * <p>支持多种筛选条件和模糊查询</p>
     *
     * @param page          分页参数
     * @param whitelistType 白名单类型（可选）
     * @param ipAddress     IP地址（模糊查询，可选）
     * @param userId        用户ID（可选）
     * @param username      用户名称（可选）
     * @param enableStatus  启用状态（可选）
     */
    @Override
    public IPage<IpWhitelistWithUserVO> selectListPage(Page<IpWhitelistWithUserVO> page, IpWhitelistTypeEnum whitelistType, String ipAddress, String userId, String username, EnableStatusEnum enableStatus) {
        return ipWhitelistMapper.selectIpWhitelistPageWithUser(page, whitelistType, ipAddress, userId, username, enableStatus);
    }

    /**
     * 根据IP地址和用户ID查询IP白名单
     *
     * @param whitelistType 白名单类型
     * @param ipAddress     IP地址
     * @param userId        用户ID
     * @param enableStatus  启用状态(可选)
     * @return 白名单实体类
     */
    @Override
    public IpWhitelist selectWhite(IpWhitelistTypeEnum whitelistType, String ipAddress, String userId, EnableStatusEnum enableStatus) {
        QueryWrapper<IpWhitelist> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("whitelist_type", whitelistType)
                .eq("ip_address", ipAddress)
                .eq("user_id", userId);
        if (enableStatus != null) {
            queryWrapper.eq("enable_status", enableStatus);
        }
        return getOne(queryWrapper);
    }

    /**
     * 验证IP地址格式是否有效
     *
     * @param ipAddress IP地址
     * @return 是否有效
     */
    @Override
    public boolean isValidIpAddress(String ipAddress) {
        if (!StringUtils.hasText(ipAddress)) {
            log.debug("IP地址为空或空白字符串");
            return false;
        }

        // 去除首尾空格
        ipAddress = ipAddress.trim();

        // IPv4地址正则表达式：只支持单个IP地址，不支持CIDR格式
        String ipv4Regex = "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";
        Pattern pattern = Pattern.compile(ipv4Regex);

        if (!pattern.matcher(ipAddress).matches()) {
            log.debug("IP地址格式不符合IPv4规范: {}", ipAddress);
            return false;
        }

        try {
            // 使用InetAddress进一步验证IP地址的有效性
            InetAddress.getByName(ipAddress);
            log.debug("IP地址格式验证通过: {}", ipAddress);
            return true;
        } catch (UnknownHostException e) {
            log.debug("IP地址无效: {}, 错误: {}", ipAddress, e.getMessage());
            return false;
        }
    }

    /**
     * 添加IP白名单
     *
     * @param ipWhitelist IP白名单实体类
     * @return 添加成功返回true，失败返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addIpWhitelist(IpWhitelist ipWhitelist) {
        if (ipWhitelist == null) {
            log.warn("IP白名单对象为空，无法添加");
            return false;
        }

        log.info("开始添加IP白名单, IP: {}, 类型: {}, 用户ID: {}",
                ipWhitelist.getIpAddress(), ipWhitelist.getWhitelistType(), ipWhitelist.getUserId());

        // 验证必填参数
        if (ipWhitelist.getWhitelistType() == null) {
            log.warn("白名单类型不能为空");
            return false;
        }

        if (!StringUtils.hasText(ipWhitelist.getIpAddress())) {
            log.warn("IP地址不能为空");
            return false;
        }

        // 验证IP地址格式
        if (!isValidIpAddress(ipWhitelist.getIpAddress())) {
            log.warn("IP地址格式无效: {}", ipWhitelist.getIpAddress());
            return false;
        }

        // 检查是否存在重复的IP白名单
        IpWhitelist existingWhitelist = selectWhite(
                ipWhitelist.getWhitelistType(),
                ipWhitelist.getIpAddress().trim(),
                ipWhitelist.getUserId(),
                null
        );

        if (existingWhitelist != null) {
            log.warn("IP白名单已存在, IP: {}, 类型: {}, 用户ID: {}",
                    ipWhitelist.getIpAddress(), ipWhitelist.getWhitelistType(), ipWhitelist.getUserId());
            return false;
        }

        // 设置默认值
        if (ipWhitelist.getEnableStatus() == null) {
            ipWhitelist.setEnableStatus(EnableStatusEnum.ENABLED);
        }

        // 去除IP地址首尾空格
        ipWhitelist.setIpAddress(ipWhitelist.getIpAddress().trim());

        try {
            boolean result = save(ipWhitelist);
            log.info("添加IP白名单完成, IP: {}, 类型: {}, 用户ID: {}, 结果: {}",
                    ipWhitelist.getIpAddress(), ipWhitelist.getWhitelistType(),
                    ipWhitelist.getUserId(), result);
            return result;
        } catch (Exception e) {
            log.error("添加IP白名单失败, IP: {}, 类型: {}, 用户ID: {}",
                    ipWhitelist.getIpAddress(), ipWhitelist.getWhitelistType(),
                    ipWhitelist.getUserId(), e);
            throw e;
        }
    }

    /**
     * 更新IP白名单
     *
     * @param ipWhitelist IP白名单实体类
     * @return 更新成功返回true，失败返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateIpWhitelist(IpWhitelist ipWhitelist) {
        if (ipWhitelist == null) {
            log.warn("IP白名单对象为空，无法更新");
            return false;
        }

        if (!StringUtils.hasText(ipWhitelist.getId())) {
            log.warn("IP白名单ID不能为空");
            return false;
        }

        log.info("开始更新IP白名单, ID: {}, IP: {}, 类型: {}, 用户ID: {}",
                ipWhitelist.getId(), ipWhitelist.getIpAddress(),
                ipWhitelist.getWhitelistType(), ipWhitelist.getUserId());

        // 验证记录是否存在
        IpWhitelist existingRecord = getById(ipWhitelist.getId());
        if (existingRecord == null) {
            log.warn("IP白名单记录不存在, ID: {}", ipWhitelist.getId());
            return false;
        }

        // 验证必填参数
        if (ipWhitelist.getWhitelistType() == null) {
            log.warn("白名单类型不能为空");
            return false;
        }

        if (!StringUtils.hasText(ipWhitelist.getIpAddress())) {
            log.warn("IP地址不能为空");
            return false;
        }

        // 验证IP地址格式
        if (!isValidIpAddress(ipWhitelist.getIpAddress())) {
            log.warn("IP地址格式无效: {}", ipWhitelist.getIpAddress());
            return false;
        }

        // 检查更新后是否会产生重复（排除自身）
        String trimmedIp = ipWhitelist.getIpAddress().trim();
        LambdaQueryWrapper<IpWhitelist> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IpWhitelist::getWhitelistType, ipWhitelist.getWhitelistType())
                .eq(IpWhitelist::getIpAddress, trimmedIp)
                .eq(IpWhitelist::getUserId, ipWhitelist.getUserId())
                .ne(IpWhitelist::getId, ipWhitelist.getId());

        IpWhitelist duplicateRecord = getOne(queryWrapper);
        if (duplicateRecord != null) {
            log.warn("更新后会产生重复的IP白名单, IP: {}, 类型: {}, 用户ID: {}",
                    trimmedIp, ipWhitelist.getWhitelistType(), ipWhitelist.getUserId());
            return false;
        }

        // 去除IP地址首尾空格
        ipWhitelist.setIpAddress(trimmedIp);

        try {
            boolean result = updateById(ipWhitelist);
            log.info("更新IP白名单完成, ID: {}, IP: {}, 类型: {}, 用户ID: {}, 结果: {}",
                    ipWhitelist.getId(), ipWhitelist.getIpAddress(),
                    ipWhitelist.getWhitelistType(), ipWhitelist.getUserId(), result);
            return result;
        } catch (Exception e) {
            log.error("更新IP白名单失败, ID: {}, IP: {}, 类型: {}, 用户ID: {}",
                    ipWhitelist.getId(), ipWhitelist.getIpAddress(),
                    ipWhitelist.getWhitelistType(), ipWhitelist.getUserId(), e);
            throw e;
        }
    }

    /**
     * 删除IP白名单
     *
     * @param id IP白名单ID
     * @return 删除成功返回true，失败返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteIpWhitelist(String id) {
        if (!StringUtils.hasText(id)) {
            log.warn("IP白名单ID不能为空");
            return false;
        }

        log.info("开始删除IP白名单, ID: {}", id);

        // 验证记录是否存在
        IpWhitelist existingRecord = getById(id);
        if (existingRecord == null) {
            log.warn("IP白名单记录不存在, ID: {}", id);
            return false;
        }

        try {
            boolean result = removeById(id);
            log.info("删除IP白名单完成, ID: {}, IP: {}, 结果: {}",
                    id, existingRecord.getIpAddress(), result);
            return result;
        } catch (Exception e) {
            log.error("删除IP白名单失败, ID: {}", id, e);
            throw e;
        }
    }

    /**
     * 批量删除IP白名单
     *
     * @param ids IP白名单ID列表
     * @return 实际删除成功的记录数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchDeleteIpWhitelist(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            log.warn("IP白名单ID列表为空，无法执行批量删除");
            return 0;
        }

        log.info("开始批量删除IP白名单, 数量: {}", ids.size());

        // 过滤掉空的ID
        List<String> validIds = ids.stream()
                .filter(StringUtils::hasText)
                .distinct()
                .toList();

        if (validIds.isEmpty()) {
            log.warn("没有有效的IP白名单ID，无法执行批量删除");
            return 0;
        }

        try {
            // 查询实际存在的记录数量
            long existingCount = count(new LambdaQueryWrapper<IpWhitelist>()
                    .in(IpWhitelist::getId, validIds));

            boolean result = removeByIds(validIds);
            int successCount = result ? (int) existingCount : 0;

            log.info("批量删除IP白名单完成, 请求删除数量: {}, 实际删除数量: {}",
                    validIds.size(), successCount);
            return successCount;
        } catch (Exception e) {
            log.error("批量删除IP白名单失败, ID数量: {}", validIds.size(), e);
            throw e;
        }
    }

    /**
     * 批量更新IP白名单状态
     *
     * @param ids          IP白名单ID列表
     * @param enableStatus 要设置的状态
     * @return 实际更新成功的记录数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateStatus(List<String> ids, EnableStatusEnum enableStatus) {
        if (CollectionUtils.isEmpty(ids)) {
            log.warn("IP白名单ID列表为空，无法执行批量状态更新");
            return 0;
        }

        if (enableStatus == null) {
            log.warn("启用状态不能为空");
            return 0;
        }

        log.info("开始批量更新IP白名单状态, 数量: {}, 目标状态: {}", ids.size(), enableStatus);

        // 过滤掉空的ID
        List<String> validIds = ids.stream()
                .filter(StringUtils::hasText)
                .distinct()
                .toList();

        if (validIds.isEmpty()) {
            log.warn("没有有效的IP白名单ID，无法执行批量状态更新");
            return 0;
        }

        try {
            LambdaUpdateWrapper<IpWhitelist> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(IpWhitelist::getEnableStatus, enableStatus)
                    .in(IpWhitelist::getId, validIds);

            boolean result = update(updateWrapper);

            // 查询实际更新的记录数
            int successCount = 0;
            if (result) {
                successCount = Math.toIntExact(count(new LambdaQueryWrapper<IpWhitelist>()
                        .in(IpWhitelist::getId, validIds)
                        .eq(IpWhitelist::getEnableStatus, enableStatus)));
            }

            log.info("批量更新IP白名单状态完成, 请求更新数量: {}, 实际更新数量: {}, 目标状态: {}",
                    validIds.size(), successCount, enableStatus);
            return successCount;
        } catch (Exception e) {
            log.error("批量更新IP白名单状态失败, ID数量: {}, 目标状态: {}",
                    validIds.size(), enableStatus, e);
            throw e;
        }
    }

    /**
     * 清空用户白名单
     *
     * @param userId 用户ID
     * @return 重置成功返回true，失败返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteIpWhitelistByUserId(String userId) {
        if (!StringUtils.hasText(userId)) {
            log.warn("用户ID不能为空");
            return false;
        }

        log.info("开始清空用户白名单, 用户ID: {}", userId);

        try {
            // 查询该用户的白名单记录数量
            long count = count(new LambdaQueryWrapper<IpWhitelist>()
                    .eq(IpWhitelist::getUserId, userId));

            if (count == 0) {
                log.info("用户没有白名单记录, 用户ID: {}", userId);
                return true;
            }

            // 删除该用户的所有白名单记录
            LambdaQueryWrapper<IpWhitelist> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(IpWhitelist::getUserId, userId);

            boolean result = remove(queryWrapper);
            log.info("清空用户白名单完成, 用户ID: {}, 删除记录数: {}, 结果: {}",
                    userId, count, result);
            return result;
        } catch (Exception e) {
            log.error("清空用户白名单失败, 用户ID: {}", userId, e);
            throw e;
        }
    }

    /**
     * 验证IP白名单是否通过验证
     *
     * <p>业务逻辑说明：</p>
     * <ul>
     *   <li>如果用户没有任何白名单记录，说明未开启白名单校验，直接返回true</li>
     *   <li>如果用户有白名单记录，则检查传入的IP地址是否存在于该用户的启用状态白名单中</li>
     *   <li>只有当IP地址存在且状态为启用时，才返回true</li>
     * </ul>
     *
     * @param whitelistType 白名单类型
     * @param ipAddress     IP地址
     * @param userId        用户ID
     * @return true:验证通过（未开启白名单或IP在白名单中），false:验证失败（开启了白名单但IP不在白名单中或状态为禁用）
     */
    @Override
    public boolean validateIpWhitelist(IpWhitelistTypeEnum whitelistType, String ipAddress, String userId) {
        // 参数验证
        if (whitelistType == null) {
            log.warn("IP白名单验证失败 - 白名单类型不能为空");
            return false;
        }

        if (!StringUtils.hasText(ipAddress)) {
            log.warn("IP白名单验证失败 - IP地址不能为空");
            return false;
        }

        if (!StringUtils.hasText(userId)) {
            log.warn("IP白名单验证失败 - 用户ID不能为空");
            return false;
        }

        // 验证IP地址格式
        if (!isValidIpAddress(ipAddress)) {
            log.warn("IP白名单验证失败 - IP地址格式无效: {}", ipAddress);
            return false;
        }

        String trimmedIp = ipAddress.trim();
        log.debug("开始验证IP白名单, 类型: {}, IP: {}, 用户ID: {}", whitelistType, trimmedIp, userId);

        try {
            // 查询用户的所有白名单记录
            List<IpWhitelist> whitelistRecords = list(new LambdaQueryWrapper<IpWhitelist>()
                    .eq(IpWhitelist::getWhitelistType, whitelistType)
                    .eq(IpWhitelist::getUserId, userId)
                    .eq(IpWhitelist::getEnableStatus, EnableStatusEnum.ENABLED)
            );

            if (CollectionUtils.isEmpty(whitelistRecords)) {
                // 用户没有白名单记录，说明未开启白名单校验，直接通过
                log.debug("IP白名单验证通过 - 用户未开启白名单校验, 类型: {}, IP: {}, 用户ID: {}",
                        whitelistType, trimmedIp, userId);
                return true;
            }

            // 检查IP是否在启用的白名单中
            boolean isIpInEnabledWhitelist = whitelistRecords.stream()
                    .anyMatch(record -> trimmedIp.equals(record.getIpAddress()));

            if (isIpInEnabledWhitelist) {
                // IP在启用的白名单中，验证通过
                log.debug("IP白名单验证通过 - IP在启用的白名单中, 类型: {}, IP: {}, 用户ID: {}",
                        whitelistType, trimmedIp, userId);
                return true;
            } else {
                // IP不在启用的白名单中，验证失败
                log.warn("IP白名单验证失败 - IP不在启用的白名单中, 类型: {}, IP: {}, 用户ID: {}",
                        whitelistType, trimmedIp, userId);
                return false;
            }

        } catch (Exception e) {
            log.error("IP白名单验证异常, 类型: {}, IP: {}, 用户ID: {}", whitelistType, trimmedIp, userId, e);
            // 发生异常时，为了安全起见，返回验证失败
            return false;
        }
    }


}
