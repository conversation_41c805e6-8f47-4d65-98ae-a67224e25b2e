package com.akey.core.dao.entity;

import com.akey.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户类型菜单关联实体类
 * 
 * <p>对应数据库表：sys_user_type_menu</p>
 * <p>建立用户类型和菜单的多对多关系</p>
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user_type_menu")
public class UserTypeMenu extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 用户类型ID
     * 关联用户类型表的主键
     */
    @TableField("user_type_id")
    private String userTypeId;

    /**
     * 菜单ID
     * 关联菜单表的主键
     */
    @TableField("menu_id")
    private String menuId;
} 