package com.akey.core.dao.service;

import com.akey.core.dao.entity.OperationLog;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 操作日志Service接口
 * 
 * <p>提供操作日志相关的业务操作</p>
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface OperationLogService extends IService<OperationLog> {

    /**
     * 分页查询操作日志
     *
     * @param page 分页参数
     * @param userId 用户ID（可选）
     * @param username 用户名（模糊查询，可选）
     * @param operationModule 操作模块（可选）
     * @param operationType 操作类型（可选）
     * @param operationStatus 操作状态（可选）
     * @param requestMethod 请求方法（可选）
     * @param clientIp 客户端IP（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 分页结果
     */
    IPage<OperationLog> getOperationLogPage(Page<OperationLog> page, String userId, String username,
                                           String operationModule, String operationType, Integer operationStatus,
                                           String requestMethod, String clientIp,
                                           LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据用户ID查询操作历史
     * 
     * @param userId 用户ID
     * @param limit 限制数量（可选，默认100）
     * @return 操作历史列表
     */
    List<OperationLog> getUserOperationHistory(String userId, Integer limit);

    /**
     * 根据用户ID分页查询操作历史
     * 
     * @param page 分页参数
     * @param userId 用户ID
     * @param operationType 操作类型（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 分页结果
     */
    IPage<OperationLog> getUserOperationHistoryPage(Page<OperationLog> page, String userId, String operationType,
                                                   LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计操作日志数量
     * 
     * @param operationModule 操作模块（可选）
     * @param operationType 操作类型（可选）
     * @param operationStatus 操作状态（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 统计数量
     */
    Long countOperationLogs(String operationModule, String operationType, Integer operationStatus,
                           LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据操作模块统计操作数量
     * 
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 统计结果 Map<模块名, 数量>
     */
    Map<String, Long> countByOperationModule(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据操作类型统计操作数量
     * 
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 统计结果 Map<操作类型, 数量>
     */
    Map<String, Long> countByOperationType(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据用户统计操作数量
     * 
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param limit 限制数量（可选，默认10）
     * @return 统计结果 Map<用户名, 数量>
     */
    Map<String, Long> countByUser(LocalDateTime startTime, LocalDateTime endTime, Integer limit);

    /**
     * 清理过期操作日志
     *
     * @param beforeTime 清理此时间之前的记录
     * @return 清理的记录数
     */
    Long cleanupExpiredLogs(LocalDateTime beforeTime);

    /**
     * 物理清空所有操作日志
     *
     * <p>注意：此方法执行物理删除，会真正从数据库中移除所有记录，包括已逻辑删除的记录</p>
     * <p>此操作不可逆，请谨慎使用</p>
     *
     * @return 删除的记录数
     */
    Long physicalClearAllLogs();

    /**
     * 批量插入操作日志
     * 
     * @param operationLogs 操作日志列表
     * @return 插入成功的记录数
     */
    int batchInsertOperationLogs(List<OperationLog> operationLogs);

    /**
     * 记录操作日志
     * 
     * @param operationLog 操作日志
     * @return 记录成功返回true，失败返回false
     */
    boolean recordOperationLog(OperationLog operationLog);

    /**
     * 获取用户最近的操作记录
     * 
     * @param userId 用户ID
     * @param operationType 操作类型（可选）
     * @param limit 限制数量（默认10）
     * @return 最近操作记录列表
     */
    List<OperationLog> getRecentOperations(String userId, String operationType, Integer limit);

    /**
     * 检查用户是否有指定操作权限（基于历史操作记录）
     * 
     * @param userId 用户ID
     * @param operationModule 操作模块
     * @param operationType 操作类型
     * @return 有权限返回true，无权限返回false
     */
    boolean hasOperationPermission(String userId, String operationModule, String operationType);

    /**
     * 获取操作日志统计信息
     * 
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 统计信息
     */
    Map<String, Object> getOperationStatistics(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据IP地址查询操作记录
     * 
     * @param clientIp 客户端IP
     * @param limit 限制数量（可选，默认100）
     * @return 操作记录列表
     */
    List<OperationLog> getOperationsByIp(String clientIp, Integer limit);

    /**
     * 获取失败操作记录
     * 
     * @param page 分页参数
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 失败操作记录分页结果
     */
    IPage<OperationLog> getFailedOperations(Page<OperationLog> page, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据操作模块和类型删除操作日志
     * 
     * @param operationModule 操作模块
     * @param operationType 操作类型（可选）
     * @param beforeTime 删除此时间之前的记录（可选）
     * @return 删除的记录数
     */
    Long deleteByModuleAndType(String operationModule, String operationType, LocalDateTime beforeTime);
}
