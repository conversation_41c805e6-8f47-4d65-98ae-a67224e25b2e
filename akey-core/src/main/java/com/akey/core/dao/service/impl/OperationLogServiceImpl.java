package com.akey.core.dao.service.impl;

import com.akey.common.enums.OperationStatus;
import com.akey.core.dao.entity.OperationLog;
import com.akey.core.dao.mapper.OperationLogMapper;
import com.akey.core.dao.service.OperationLogService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 操作日志Service实现类
 * 
 * <p>使用MyBatis-Plus的ServiceImpl基类和条件构造器进行操作</p>
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OperationLogServiceImpl extends ServiceImpl<OperationLogMapper, OperationLog> implements OperationLogService {

    private final OperationLogMapper operationLogMapper;

    /**
     * 分页查询操作日志
     *
     * @param page 分页参数
     * @param userId 用户ID（可选）
     * @param username 用户名（模糊查询，可选）
     * @param operationModule 操作模块（可选）
     * @param operationType 操作类型（可选）
     * @param operationStatus 操作状态（可选）
     * @param requestMethod 请求方法（可选）
     * @param clientIp 客户端IP（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 分页结果
     */
    @Override
    public IPage<OperationLog> getOperationLogPage(Page<OperationLog> page, String userId, String username,
                                                  String operationModule, String operationType, Integer operationStatus,
                                                  String requestMethod, String clientIp,
                                                  LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("分页查询操作日志, userId: {}, username: {}, module: {}, type: {}, status: {}, requestMethod: {}, clientIp: {}, startTime: {}, endTime: {}",
                userId, username, operationModule, operationType, operationStatus, requestMethod, clientIp, startTime, endTime);

        LambdaQueryWrapper<OperationLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.hasText(userId), OperationLog::getUserId, userId)
               .like(StringUtils.hasText(username), OperationLog::getUsername, username)
               .eq(StringUtils.hasText(operationModule), OperationLog::getOperationModule, operationModule)
               .eq(StringUtils.hasText(operationType), OperationLog::getOperationType, operationType)
               .eq(operationStatus != null, OperationLog::getOperationStatus, operationStatus)
               .eq(StringUtils.hasText(requestMethod), OperationLog::getRequestMethod, requestMethod)
               .eq(StringUtils.hasText(clientIp), OperationLog::getClientIp, clientIp)
               .ge(startTime != null, OperationLog::getOperationTime, startTime)
               .le(endTime != null, OperationLog::getOperationTime, endTime)
               .orderByDesc(OperationLog::getOperationTime);

        return page(page, wrapper);
    }

    /**
     * 根据用户ID查询操作历史
     * 
     * @param userId 用户ID
     * @param limit 限制数量（可选，默认100）
     * @return 操作历史列表
     */
    @Override
    public List<OperationLog> getUserOperationHistory(String userId, Integer limit) {
        if (!StringUtils.hasText(userId)) {
            log.warn("用户ID为空，无法查询操作历史");
            return List.of();
        }

        log.debug("查询用户操作历史, userId: {}, limit: {}", userId, limit);

        LambdaQueryWrapper<OperationLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OperationLog::getUserId, userId)
               .orderByDesc(OperationLog::getOperationTime);

        if (limit != null && limit > 0) {
            wrapper.last("LIMIT " + limit);
        } else {
            wrapper.last("LIMIT 100");
        }

        return list(wrapper);
    }

    /**
     * 根据用户ID分页查询操作历史
     * 
     * @param page 分页参数
     * @param userId 用户ID
     * @param operationType 操作类型（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 分页结果
     */
    @Override
    public IPage<OperationLog> getUserOperationHistoryPage(Page<OperationLog> page, String userId, String operationType,
                                                          LocalDateTime startTime, LocalDateTime endTime) {
        if (!StringUtils.hasText(userId)) {
            log.warn("用户ID为空，返回空分页结果");
            return new Page<>(page.getCurrent(), page.getSize());
        }

        log.debug("分页查询用户操作历史, userId: {}, operationType: {}, startTime: {}, endTime: {}",
                userId, operationType, startTime, endTime);

        LambdaQueryWrapper<OperationLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OperationLog::getUserId, userId)
               .eq(StringUtils.hasText(operationType), OperationLog::getOperationType, operationType)
               .ge(startTime != null, OperationLog::getOperationTime, startTime)
               .le(endTime != null, OperationLog::getOperationTime, endTime)
               .orderByDesc(OperationLog::getOperationTime);

        return page(page, wrapper);
    }

    /**
     * 统计操作日志数量
     * 
     * @param operationModule 操作模块（可选）
     * @param operationType 操作类型（可选）
     * @param operationStatus 操作状态（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 统计数量
     */
    @Override
    public Long countOperationLogs(String operationModule, String operationType, Integer operationStatus,
                                  LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("统计操作日志数量, module: {}, type: {}, status: {}, startTime: {}, endTime: {}",
                operationModule, operationType, operationStatus, startTime, endTime);

        LambdaQueryWrapper<OperationLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.hasText(operationModule), OperationLog::getOperationModule, operationModule)
               .eq(StringUtils.hasText(operationType), OperationLog::getOperationType, operationType)
               .eq(operationStatus != null, OperationLog::getOperationStatus, operationStatus)
               .ge(startTime != null, OperationLog::getOperationTime, startTime)
               .le(endTime != null, OperationLog::getOperationTime, endTime);

        return count(wrapper);
    }

    /**
     * 根据操作模块统计操作数量
     * 
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 统计结果 Map<模块名, 数量>
     */
    @Override
    public Map<String, Long> countByOperationModule(LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("根据操作模块统计操作数量, startTime: {}, endTime: {}", startTime, endTime);

        List<Map<String, Object>> results = operationLogMapper.countByOperationModule(startTime, endTime);
        return results.stream()
                .collect(Collectors.toMap(
                        map -> (String) map.get("operation_module"),
                        map -> ((Number) map.get("count")).longValue()
                ));
    }

    /**
     * 根据操作类型统计操作数量
     * 
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 统计结果 Map<操作类型, 数量>
     */
    @Override
    public Map<String, Long> countByOperationType(LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("根据操作类型统计操作数量, startTime: {}, endTime: {}", startTime, endTime);

        List<Map<String, Object>> results = operationLogMapper.countByOperationType(startTime, endTime);
        return results.stream()
                .collect(Collectors.toMap(
                        map -> (String) map.get("operation_type"),
                        map -> ((Number) map.get("count")).longValue()
                ));
    }

    /**
     * 根据用户统计操作数量
     * 
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param limit 限制数量（可选，默认10）
     * @return 统计结果 Map<用户名, 数量>
     */
    @Override
    public Map<String, Long> countByUser(LocalDateTime startTime, LocalDateTime endTime, Integer limit) {
        log.debug("根据用户统计操作数量, startTime: {}, endTime: {}, limit: {}", startTime, endTime, limit);

        Integer actualLimit = (limit != null && limit > 0) ? limit : 10;
        List<Map<String, Object>> results = operationLogMapper.countByUser(startTime, endTime, actualLimit);
        return results.stream()
                .collect(Collectors.toMap(
                        map -> (String) map.get("username"),
                        map -> ((Number) map.get("count")).longValue()
                ));
    }

    /**
     * 清理过期操作日志
     * 
     * @param beforeTime 清理此时间之前的记录
     * @return 清理的记录数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long cleanupExpiredLogs(LocalDateTime beforeTime) {
        if (beforeTime == null) {
            log.warn("清理时间为空，无法执行清理操作");
            return 0L;
        }

        log.info("开始清理过期操作日志, beforeTime: {}", beforeTime);

        try {
            Long deletedCount = operationLogMapper.cleanupExpiredLogs(beforeTime);
            log.info("清理过期操作日志完成, 删除记录数: {}", deletedCount);
            return deletedCount != null ? deletedCount : 0L;
        } catch (Exception e) {
            log.error("清理过期操作日志失败", e);
            throw e;
        }
    }

    /**
     * 批量插入操作日志
     * 
     * @param operationLogs 操作日志列表
     * @return 插入成功的记录数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInsertOperationLogs(List<OperationLog> operationLogs) {
        if (CollectionUtils.isEmpty(operationLogs)) {
            log.warn("操作日志列表为空，无法执行批量插入");
            return 0;
        }

        log.debug("批量插入操作日志, 数量: {}", operationLogs.size());

        try {
            boolean result = saveBatch(operationLogs);
            int successCount = result ? operationLogs.size() : 0;
            log.debug("批量插入操作日志完成, 成功数量: {}", successCount);
            return successCount;
        } catch (Exception e) {
            log.error("批量插入操作日志失败", e);
            throw e;
        }
    }

    /**
     * 记录操作日志
     *
     * @param operationLog 操作日志
     * @return 记录成功返回true，失败返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recordOperationLog(OperationLog operationLog) {
        if (operationLog == null) {
            log.warn("操作日志对象为空，无法记录");
            return false;
        }

        log.debug("记录操作日志, userId: {}, module: {}, type: {}",
                operationLog.getUserId(), operationLog.getOperationModule(), operationLog.getOperationType());

        try {
            // 设置默认值
            if (operationLog.getOperationTime() == null) {
                operationLog.setOperationTime(LocalDateTime.now());
            }
            if (operationLog.getOperationStatus() == null) {
                operationLog.setOperationStatusEnum(OperationStatus.SUCCESS);
            }

            boolean result = save(operationLog);
            log.debug("记录操作日志完成, 结果: {}", result);
            return result;
        } catch (Exception e) {
            log.error("记录操作日志失败", e);
            return false;
        }
    }

    /**
     * 获取用户最近的操作记录
     *
     * @param userId 用户ID
     * @param operationType 操作类型（可选）
     * @param limit 限制数量（默认10）
     * @return 最近操作记录列表
     */
    @Override
    public List<OperationLog> getRecentOperations(String userId, String operationType, Integer limit) {
        if (!StringUtils.hasText(userId)) {
            log.warn("用户ID为空，无法查询最近操作记录");
            return List.of();
        }

        log.debug("查询用户最近操作记录, userId: {}, operationType: {}, limit: {}", userId, operationType, limit);

        LambdaQueryWrapper<OperationLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OperationLog::getUserId, userId)
               .eq(StringUtils.hasText(operationType), OperationLog::getOperationType, operationType)
               .orderByDesc(OperationLog::getOperationTime);

        Integer actualLimit = (limit != null && limit > 0) ? limit : 10;
        wrapper.last("LIMIT " + actualLimit);

        return list(wrapper);
    }

    /**
     * 检查用户是否有指定操作权限（基于历史操作记录）
     *
     * @param userId 用户ID
     * @param operationModule 操作模块
     * @param operationType 操作类型
     * @return 有权限返回true，无权限返回false
     */
    @Override
    public boolean hasOperationPermission(String userId, String operationModule, String operationType) {
        if (!StringUtils.hasText(userId) || !StringUtils.hasText(operationModule) || !StringUtils.hasText(operationType)) {
            log.warn("参数不完整，无法检查操作权限");
            return false;
        }

        log.debug("检查用户操作权限, userId: {}, module: {}, type: {}", userId, operationModule, operationType);

        LambdaQueryWrapper<OperationLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OperationLog::getUserId, userId)
               .eq(OperationLog::getOperationModule, operationModule)
               .eq(OperationLog::getOperationType, operationType)
               .eq(OperationLog::getOperationStatus, OperationStatus.SUCCESS.getValue())
               .last("LIMIT 1");

        long count = count(wrapper);
        boolean hasPermission = count > 0;
        log.debug("用户操作权限检查结果: {}", hasPermission);
        return hasPermission;
    }

    /**
     * 获取操作日志统计信息
     *
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 统计信息
     */
    @Override
    public Map<String, Object> getOperationStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("获取操作日志统计信息, startTime: {}, endTime: {}", startTime, endTime);

        try {
            Map<String, Object> statistics = operationLogMapper.getOperationStatistics(startTime, endTime);

            // 计算成功率
            Long totalCount = (Long) statistics.get("totalCount");
            Long successCount = (Long) statistics.get("successCount");
            if (totalCount != null && totalCount > 0 && successCount != null) {
                double successRate = (double) successCount / totalCount * 100;
                statistics.put("successRate", Math.round(successRate * 100.0) / 100.0);
            } else {
                statistics.put("successRate", 0.0);
            }

            log.debug("获取操作日志统计信息完成: {}", statistics);
            return statistics;
        } catch (Exception e) {
            log.error("获取操作日志统计信息失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 根据IP地址查询操作记录
     *
     * @param clientIp 客户端IP
     * @param limit 限制数量（可选，默认100）
     * @return 操作记录列表
     */
    @Override
    public List<OperationLog> getOperationsByIp(String clientIp, Integer limit) {
        if (!StringUtils.hasText(clientIp)) {
            log.warn("客户端IP为空，无法查询操作记录");
            return List.of();
        }

        log.debug("根据IP查询操作记录, clientIp: {}, limit: {}", clientIp, limit);

        LambdaQueryWrapper<OperationLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OperationLog::getClientIp, clientIp)
               .orderByDesc(OperationLog::getOperationTime);

        Integer actualLimit = (limit != null && limit > 0) ? limit : 100;
        wrapper.last("LIMIT " + actualLimit);

        return list(wrapper);
    }

    /**
     * 获取失败操作记录
     *
     * @param page 分页参数
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 失败操作记录分页结果
     */
    @Override
    public IPage<OperationLog> getFailedOperations(Page<OperationLog> page, LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("查询失败操作记录, startTime: {}, endTime: {}", startTime, endTime);

        LambdaQueryWrapper<OperationLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OperationLog::getOperationStatus, OperationStatus.FAILURE.getValue())
               .ge(startTime != null, OperationLog::getOperationTime, startTime)
               .le(endTime != null, OperationLog::getOperationTime, endTime)
               .orderByDesc(OperationLog::getOperationTime);

        return page(page, wrapper);
    }

    /**
     * 根据操作模块和类型删除操作日志
     *
     * @param operationModule 操作模块
     * @param operationType 操作类型（可选）
     * @param beforeTime 删除此时间之前的记录（可选）
     * @return 删除的记录数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long deleteByModuleAndType(String operationModule, String operationType, LocalDateTime beforeTime) {
        if (!StringUtils.hasText(operationModule)) {
            log.warn("操作模块为空，无法执行删除操作");
            return 0L;
        }

        log.info("根据模块和类型删除操作日志, module: {}, type: {}, beforeTime: {}",
                operationModule, operationType, beforeTime);

        try {
            LambdaUpdateWrapper<OperationLog> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(OperationLog::getOperationModule, operationModule)
                   .eq(StringUtils.hasText(operationType), OperationLog::getOperationType, operationType)
                   .le(beforeTime != null, OperationLog::getOperationTime, beforeTime);

            // 使用逻辑删除
            boolean result = remove(wrapper);

            // 获取删除的记录数（这里简化处理，实际可能需要先查询再删除来获取准确数量）
            Long deletedCount = result ? 1L : 0L;
            log.info("删除操作日志完成, 删除记录数: {}", deletedCount);
            return deletedCount;
        } catch (Exception e) {
            log.error("删除操作日志失败", e);
            throw e;
        }
    }

    /**
     * 物理清空所有操作日志
     *
     * @return 删除的记录数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long physicalClearAllLogs() {
        log.warn("开始物理清空所有操作日志");

        try {
            Long deletedCount = operationLogMapper.physicalClearAllLogs();
            log.warn("物理清空所有操作日志完成, 删除记录数: {}", deletedCount);
            return deletedCount != null ? deletedCount : 0L;
        } catch (Exception e) {
            log.error("物理清空所有操作日志失败", e);
            throw e;
        }
    }
}
