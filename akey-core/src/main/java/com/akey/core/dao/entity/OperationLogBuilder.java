package com.akey.core.dao.entity;

import com.akey.common.enums.OperationStatus;
import com.akey.common.enums.OperationType;

import java.time.LocalDateTime;

/**
 * 操作日志构建器
 * 
 * <p>提供链式调用方式构建操作日志对象</p>
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
public class OperationLogBuilder {

    private final OperationLog operationLog;

    private OperationLogBuilder() {
        this.operationLog = new OperationLog();
        this.operationLog.setOperationTime(LocalDateTime.now());
        this.operationLog.setCreateTime(LocalDateTime.now());
        this.operationLog.setOperationStatusEnum(OperationStatus.SUCCESS);
    }

    /**
     * 创建构建器实例
     * 
     * @return 构建器实例
     */
    public static OperationLogBuilder create() {
        return new OperationLogBuilder();
    }

    /**
     * 设置用户信息
     * 
     * @param userId 用户ID
     * @param username 用户名
     * @return 构建器实例
     */
    public OperationLogBuilder user(String userId, String username) {
        operationLog.setUserId(userId);
        operationLog.setUsername(username);
        return this;
    }

    /**
     * 设置操作信息
     * 
     * @param module 操作模块
     * @param type 操作类型
     * @param description 操作描述
     * @return 构建器实例
     */
    public OperationLogBuilder operation(String module, OperationType type, String description) {
        operationLog.setOperationModule(module);
        operationLog.setOperationTypeEnum(type);
        operationLog.setOperationDesc(description);
        return this;
    }

    /**
     * 设置操作信息
     *
     * @param module 操作模块
     * @param type 操作类型字符串
     * @param description 操作描述
     * @return 构建器实例
     */
    public OperationLogBuilder operation(String module, String type, String description) {
        operationLog.setOperationModule(module);
        operationLog.setOperationType(type);
        operationLog.setOperationDesc(description);
        return this;
    }

    /**
     * 设置请求信息
     * 
     * @param method 请求方法
     * @param url 请求URL
     * @param params 请求参数
     * @return 构建器实例
     */
    public OperationLogBuilder request(String method, String url, Object params) {
        operationLog.setRequestMethod(method);
        operationLog.setRequestUrl(url);
        operationLog.setRequestParams(params);
        return this;
    }

    /**
     * 设置响应信息
     * 
     * @param status 响应状态码
     * @param message 响应消息
     * @param result 响应结果
     * @return 构建器实例
     */
    public OperationLogBuilder response(Integer status, String message, Object result) {
        operationLog.setResponseStatus(status);
        operationLog.setResponseMessage(message);
        operationLog.setResponseResult(result);
        return this;
    }

    /**
     * 设置系统信息
     * 
     * @param clientIp 客户端IP
     * @param userAgent 用户代理
     * @return 构建器实例
     */
    public OperationLogBuilder system(String clientIp, String userAgent) {
        operationLog.setClientIp(clientIp);
        operationLog.setUserAgent(userAgent);
        return this;
    }



    /**
     * 设置地理位置信息
     *
     * @param location 地理位置（格式：国家 省份 城市）
     * @return 构建器实例
     */
    public OperationLogBuilder location(String location) {
        operationLog.setLocation(location);
        return this;
    }

    /**
     * 设置操作状态
     * 
     * @param status 操作状态
     * @return 构建器实例
     */
    public OperationLogBuilder status(OperationStatus status) {
        operationLog.setOperationStatusEnum(status);
        return this;
    }

    /**
     * 设置操作成功
     * 
     * @return 构建器实例
     */
    public OperationLogBuilder success() {
        operationLog.setOperationStatusEnum(OperationStatus.SUCCESS);
        return this;
    }

    /**
     * 设置操作失败
     * 
     * @param errorMessage 错误信息
     * @return 构建器实例
     */
    public OperationLogBuilder failure(String errorMessage) {
        operationLog.setOperationStatusEnum(OperationStatus.FAILURE);
        operationLog.setErrorMessage(errorMessage);
        return this;
    }

    /**
     * 设置执行时间
     * 
     * @param executionTime 执行时间(毫秒)
     * @return 构建器实例
     */
    public OperationLogBuilder executionTime(Long executionTime) {
        operationLog.setExecutionTime(executionTime);
        return this;
    }

    /**
     * 设置操作时间
     * 
     * @param operationTime 操作时间
     * @return 构建器实例
     */
    public OperationLogBuilder operationTime(LocalDateTime operationTime) {
        operationLog.setOperationTime(operationTime);
        return this;
    }

    /**
     * 设置创建人
     * 
     * @param createBy 创建人
     * @return 构建器实例
     */
    public OperationLogBuilder createBy(String createBy) {
        operationLog.setCreateBy(createBy);
        return this;
    }

    /**
     * 构建操作日志对象
     * 
     * @return 操作日志对象
     */
    public OperationLog build() {
        return operationLog;
    }
}
