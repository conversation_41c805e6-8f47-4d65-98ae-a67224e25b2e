package com.akey.core.dao.entity;

import com.akey.common.enums.*;
import com.akey.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 菜单实体类
 * 
 * <p>对应数据库表：sys_menu</p>
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_menu")
public class Menu extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 父菜单ID
     * 顶级菜单为null
     */
    @TableField("parent_id")
    private String parentId;

    /**
     * 菜单名称
     */
    @TableField("menu_name")
    private String menuName;

    /**
     * 菜单类型
     * 1:目录 2:菜单 3:按钮
     * 
     * @see com.akey.common.enums.MenuTypeEnum
     */
    @TableField("menu_type")
    private MenuTypeEnum menuType;

    /**
     * 菜单编码
     * 唯一标识符，用于权限控制
     */
    @TableField("menu_code")
    private String menuCode;

    /**
     * 路由路径
     * 前端路由地址
     */
    @TableField("route_path")
    private String routePath;

    /**
     * 组件路径
     * 前端组件文件路径
     */
    @TableField("component_path")
    private String componentPath;

    /**
     * 图标
     * 菜单显示的图标名称
     */
    @TableField("icon")
    private String icon;

    /**
     * 排序号
     * 数值越小越靠前
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 是否可见
     * 0:隐藏 1:显示
     * 
     * @see com.akey.common.enums.ShowHideEnum
     */
    @TableField("visible")
    private ShowHideEnum visible;

    /**
     * 状态
     * 0:禁用 1:启用
     * 
     * @see com.akey.common.enums.EnableStatusEnum
     */
    @TableField("status")
    private EnableStatusEnum status;

    /**
     * 权限标识
     * 用于权限校验的标识符
     */
    @TableField("permission")
    private String permission;

    /**
     * 是否外部链接
     * 0:否 1:是
     * 
     * @see com.akey.common.enums.ExternalLinkEnum
     */
    @TableField("external_link")
    private ExternalLinkEnum externalLink;

    /**
     * 打开方式
     * _self:当前窗口 _blank:新窗口
     *
     * @see com.akey.common.enums.OpenModeEnum
     */
    @TableField("open_mode")
    private OpenModeEnum openMode;

    /**
     * 路由参数
     * JSON格式存储路由参数，用于动态路由传参
     *
     * <p>示例格式：</p>
     * <pre>
     * {
     *   "id": "123",
     *   "type": "edit",
     *   "tab": "basic"
     * }
     * </pre>
     */
    @TableField("route_params")
    private String routeParams;

    /**
     * 是否内置类型
     *
     * <p>0:是内置,不可删除、修改状态 1:否,可删除</p>
     * <p>内置菜单通常是系统核心功能菜单，不允许用户删除或修改状态</p>
     *
     * @see com.akey.common.enums.BuiltinEnum
     */
    @TableField("is_builtin")
    private BuiltinEnum isBuiltin;

    /**
     * 子菜单
     */
    @TableField(exist = false)
    private List<Menu> children;
} 