package com.akey.core.dao.service;

import com.akey.common.enums.MenuTypeEnum;
import com.akey.core.dao.entity.Menu;
import com.akey.common.enums.EnableStatusEnum;

import java.util.List;

/**
 * 菜单Service接口
 * 
 * <p>提供菜单相关的业务操作</p>
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface MenuService {

    /**
     * 获取所有菜单
     *
     * @param menuName 菜单名称（模糊查询，可为空）
     * @param menuType 菜单类型
     * @param status 菜单状态（可为空，查询全部）
     * @return 菜单列表
     */
    List<Menu> getMenuList(String menuName, MenuTypeEnum menuType, EnableStatusEnum status);

    /**
     * 2. 根据ID获取菜单信息
     * 
     * @param id 菜单ID
     * @return 菜单信息，如果不存在则返回null
     */
    Menu getMenuById(String id);

    /**
     * 3. 通过ID修改菜单信息
     * 
     * @param menu 菜单信息（必须包含id）
     * @return 修改成功返回true，失败返回false
     */
    boolean updateMenu(Menu menu);

    /**
     * 4. 通过ID删除菜单
     * 
     * <p>删除时需要判断是否被分配给用户类型</p>
     * <p>如果有子菜单，需要一并删除</p>
     * 
     * @param id 菜单ID
     * @return 删除成功返回true，失败返回false
     */
    boolean deleteMenu(String id);

    /**
     * 5. 新增菜单
     * 
     * @param menu 菜单信息
     * @return 新增成功返回true，失败返回false
     */
    boolean createMenu(Menu menu);

    /**
     * 获取指定父菜单下的子菜单列表
     * 
     * @param parentId 父菜单ID，null表示获取根菜单
     * @return 子菜单列表
     */
    List<Menu> getChildMenus(String parentId);

    /**
     * 检查菜单编码是否已存在
     * 
     * @param menuCode 菜单编码
     * @param excludeId 要排除的菜单ID（用于修改时排除自己），可为空
     * @return 存在返回true，不存在返回false
     */
    boolean existsByMenuCode(String menuCode, String excludeId);

    /**
     * 检查菜单是否被用户类型分配
     * 
     * @param menuId 菜单ID
     * @return 被分配返回true，未被分配返回false
     */
    boolean isMenuAssigned(String menuId);

    /**
     * 递归获取菜单的所有子菜单ID（包括子子菜单）
     * 
     * @param parentId 父菜单ID
     * @return 所有子菜单ID列表
     */
    List<String> getAllChildMenuIds(String parentId);

    /**
     * 检查父菜单是否有效
     * 
     * <p>验证规则：</p>
     * <ul>
     *   <li>父菜单必须存在</li>
     *   <li>父菜单必须是目录或菜单类型</li>
     *   <li>不能将自己设置为父菜单</li>
     *   <li>不能将子菜单设置为父菜单（避免循环引用）</li>
     * </ul>
     * 
     * @param parentId 父菜单ID
     * @param currentMenuId 当前菜单ID（用于避免循环引用）
     * @return 有效返回true，无效返回false
     */
    boolean isValidParent(String parentId, String currentMenuId);

    /**
     * 获取菜单的完整路径（从根菜单到当前菜单的路径）
     * 
     * @param menuId 菜单ID
     * @return 菜单路径列表，按层级排序
     */
    List<Menu> getMenuPath(String menuId);

    /**
     * 根据用户类型获取可访问的菜单树
     * 
     * @param userTypeId 用户类型ID
     * @return 用户可访问的菜单树
     */
    List<Menu> getMenuTreeByUserType(String userTypeId);

    /**
     * 批量修改菜单状态
     * 
     * @param menuIds 菜单ID列表
     * @param status 要设置的状态
     * @return 修改成功的数量
     */
    int batchUpdateMenuStatus(List<String> menuIds, EnableStatusEnum status);

    /**
     * 获取所有启用的菜单列表（扁平结构）
     * 
     * @return 启用的菜单列表
     */
    List<Menu> getAllEnabledMenus();

    /**
     * 根据权限标识获取菜单
     *
     * @param permission 权限标识
     * @return 菜单信息，如果不存在则返回null
     */
    Menu getMenuByPermission(String permission);

    /**
     * 根据用户类型ID获取菜单列表（扁平结构）
     *
     * @param userTypeId 用户类型ID
     * @return 菜单列表
     */
    List<Menu> getMenusByUserTypeId(String userTypeId);

    /**
     * 检查菜单是否为内置菜单
     *
     * @param menuId 菜单ID
     * @return 内置菜单返回true，非内置菜单返回false
     */
    boolean isBuiltinMenu(String menuId);

    /**
     * 统计所有菜单数量
     *
     * @return 菜单总数量
     */
    long countAllMenus();

    // ==================== 性能优化方法 ====================

    /**
     * 获取所有启用菜单的权限标识列表
     *
     * <p>只查询权限相关字段，用于权限验证场景</p>
     * <p>相比getAllEnabledMenus()方法，减少了不必要的字段查询</p>
     *
     * @return 权限标识列表（去重后）
     */
    List<String> getAllEnabledMenuPermissions();

    /**
     * 根据用户类型ID获取菜单权限标识列表
     *
     * <p>只查询权限相关字段，用于权限验证场景</p>
     * <p>相比getMenusByUserTypeId()方法，减少了不必要的字段查询</p>
     *
     * @param userTypeId 用户类型ID
     * @return 权限标识列表（去重后）
     */
    List<String> getMenuPermissionsByUserTypeId(String userTypeId);

    /**
     * 批量获取多个用户类型的菜单权限标识
     *
     * <p>一次查询获取多个用户类型的权限，减少数据库交互次数</p>
     *
     * @param userTypeIds 用户类型ID列表
     * @return 权限标识列表（去重后）
     */
    List<String> getMenuPermissionsByUserTypeIds(List<String> userTypeIds);
}