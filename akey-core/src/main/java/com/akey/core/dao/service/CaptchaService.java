package com.akey.core.dao.service;

import com.akey.common.entity.CaptchaResult;
import com.akey.common.enums.CaptchaTypeEnum;

/**
 * 验证码Service接口
 * 
 * <p>提供验证码生成、验证等业务操作</p>
 * 
 * <AUTHOR>
 * @since 2025-07-06
 */
public interface CaptchaService {

    /**
     * 1. 生成默认验证码
     * 
     * <p>根据配置文件中的默认设置生成验证码</p>
     * 
     * @param clientIp 客户端IP地址（用于防暴力破解）
     * @return 验证码结果，包含验证码ID和Base64图片
     */
    CaptchaResult generateCaptcha(String clientIp);

    /**
     * 2. 生成指定类型的验证码
     * 
     * @param type 验证码类型
     * @param clientIp 客户端IP地址
     * @return 验证码结果
     */
    CaptchaResult generateCaptcha(CaptchaTypeEnum type, String clientIp);

    /**
     * 3. 生成指定类型和长度的验证码
     * 
     * @param type 验证码类型
     * @param length 验证码长度（算式类型忽略此参数）
     * @param clientIp 客户端IP地址
     * @return 验证码结果
     */
    CaptchaResult generateCaptcha(CaptchaTypeEnum type, Integer length, String clientIp);

    /**
     * 4. 生成自定义尺寸的验证码
     * 
     * @param type 验证码类型
     * @param length 验证码长度
     * @param width 图片宽度
     * @param height 图片高度
     * @param clientIp 客户端IP地址
     * @return 验证码结果
     */
    CaptchaResult generateCustomCaptcha(CaptchaTypeEnum type, Integer length, Integer width, Integer height, String clientIp);

    /**
     * 5. 验证验证码
     *
     * @param captchaId 验证码ID
     * @param userInput 用户输入的验证码
     * @param clientIp 客户端IP地址（用于防暴力破解）
     * @return 验证结果
     */
    boolean verifyCaptcha(String captchaId, String userInput, String clientIp);

    /**
     * 5.1 检查验证码是否正确（不消费验证码）
     *
     * @param captchaId 验证码ID
     * @param userInput 用户输入的验证码
     * @param clientIp 客户端IP地址（用于防暴力破解）
     * @return 验证结果，true表示正确，false表示错误
     */
    boolean checkCaptcha(String captchaId, String userInput, String clientIp);

    /**
     * 5.2 消费验证码（删除缓存中的验证码）
     *
     * @param captchaId 验证码ID
     * @return 消费结果，true表示成功，false表示失败
     */
    boolean consumeCaptcha(String captchaId);

    /**
     * 6. 刷新验证码
     * 
     * <p>删除旧的验证码并生成新的验证码</p>
     * 
     * @param oldCaptchaId 旧验证码ID（可为空）
     * @param type 验证码类型（可为空，使用默认类型）
     * @param clientIp 客户端IP地址
     * @return 新的验证码结果
     */
    CaptchaResult refreshCaptcha(String oldCaptchaId, CaptchaTypeEnum type, String clientIp);

    /**
     * 7. 检查验证码是否存在
     * 
     * @param captchaId 验证码ID
     * @return 是否存在
     */
    boolean existsCaptcha(String captchaId);

    /**
     * 8. 删除验证码
     * 
     * @param captchaId 验证码ID
     * @return 删除成功返回true，失败返回false
     */
    boolean deleteCaptcha(String captchaId);
}
