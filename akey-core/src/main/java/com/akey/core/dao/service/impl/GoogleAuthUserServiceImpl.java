package com.akey.core.dao.service.impl;

import com.akey.common.service.GoogleAuthUserService;
import com.akey.core.dao.entity.User;
import com.akey.core.dao.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 谷歌验证码用户信息服务实现
 * 
 * <p>实现GoogleAuthUserService接口，提供用户谷歌验证器相关信息</p>
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GoogleAuthUserServiceImpl implements GoogleAuthUserService {

    private final UserService userService;

    @Override
    public String getUserGoogleAuthKey(String userId) {
        try {
            User user = userService.getUserById(userId);
            if (user == null) {
                log.debug("用户不存在, userId: {}", userId);
                return null; // 用户不存在
            }
            
            // 返回用户的谷歌验证器密钥，如果未设置则返回空字符串
            String googleAuthKey = user.getGoogleAuthKey();
            log.debug("获取用户谷歌验证器密钥, userId: {}, hasKey: {}", userId, googleAuthKey != null);
            
            return googleAuthKey != null ? googleAuthKey : "";
            
        } catch (Exception e) {
            log.error("获取用户谷歌验证器密钥失败, userId: {}", userId, e);
            return null; // 发生异常时返回null，表示用户不存在
        }
    }

    @Override
    public boolean userExists(String userId) {
        try {
            User user = userService.getUserById(userId);
            boolean exists = user != null;
            log.debug("检查用户是否存在, userId: {}, exists: {}", userId, exists);
            return exists;
        } catch (Exception e) {
            log.error("检查用户是否存在失败, userId: {}", userId, e);
            return false; // 发生异常时返回false
        }
    }

    @Override
    public boolean hasGoogleAuthSetup(String userId) {
        try {
            String googleAuthKey = getUserGoogleAuthKey(userId);
            if (googleAuthKey == null) {
                // 用户不存在
                return false;
            }
            
            boolean hasSetup = StringUtils.hasText(googleAuthKey);
            log.debug("检查用户是否已设置谷歌验证器, userId: {}, hasSetup: {}", userId, hasSetup);
            return hasSetup;
            
        } catch (Exception e) {
            log.error("检查用户谷歌验证器设置状态失败, userId: {}", userId, e);
            return false; // 发生异常时返回false
        }
    }
}
