package com.akey.core.dao.service.impl;

import cn.dev33.satoken.context.mock.SaTokenContextMockUtil;
import cn.dev33.satoken.stp.StpUtil;
import com.akey.common.service.AddressResolveService;
import com.akey.common.service.OperationLogRecordService;
import com.akey.core.dao.entity.OperationLog;
import com.akey.core.dao.service.OperationLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 操作日志记录服务实现类
 * 
 * <p>实现操作日志记录接口，负责将操作日志信息持久化到数据库</p>
 * <p>支持异步和同步两种记录方式</p>
 * <p>将OperationLogInfo转换为OperationLog实体进行保存</p>
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OperationLogRecordServiceImpl implements OperationLogRecordService {

    private final OperationLogService operationLogService;

    @Autowired(required = false)
    private AddressResolveService addressResolveService;

    /**
     * 异步记录操作日志
     * 
     * @param logInfo 操作日志信息
     */
    @Override
    @Async("operationLogExecutor")
    public void recordOperationLogAsync(OperationLogInfo logInfo) {
        try {
            SaTokenContextMockUtil.setMockContext(()->{
                StpUtil.setTokenValueToStorage(logInfo.getToken());
                log.debug("开始异步记录操作日志, module: {}, type: {}, description: {}",
                        logInfo.getModule(), logInfo.getType(), logInfo.getDescription());

                OperationLog operationLog = convertToEntity(logInfo);
                boolean success = operationLogService.recordOperationLog(operationLog);

                if (success) {
                    log.debug("异步记录操作日志成功, module: {}, type: {}",
                            logInfo.getModule(), logInfo.getType());
                } else {
                    log.warn("异步记录操作日志失败, module: {}, type: {}",
                            logInfo.getModule(), logInfo.getType());
                }
            });
        } catch (Exception e) {
            log.error("异步记录操作日志异常, module: {}, type: {}, description: {}", 
                    logInfo.getModule(), logInfo.getType(), logInfo.getDescription(), e);
        }
    }

    /**
     * 同步记录操作日志
     * 
     * @param logInfo 操作日志信息
     * @return 记录是否成功
     */
    @Override
    public boolean recordOperationLogSync(OperationLogInfo logInfo) {
        try {
            log.debug("开始同步记录操作日志, module: {}, type: {}, description: {}", 
                    logInfo.getModule(), logInfo.getType(), logInfo.getDescription());

            OperationLog operationLog = convertToEntity(logInfo);
            boolean success = operationLogService.recordOperationLog(operationLog);
            
            log.debug("同步记录操作日志完成, 结果: {}, module: {}, type: {}", 
                    success, logInfo.getModule(), logInfo.getType());
            return success;

        } catch (Exception e) {
            log.error("同步记录操作日志异常, module: {}, type: {}, description: {}", 
                    logInfo.getModule(), logInfo.getType(), logInfo.getDescription(), e);
            return false;
        }
    }

    /**
     * 将OperationLogInfo转换为OperationLog实体
     * 
     * @param logInfo 操作日志信息
     * @return 操作日志实体
     */
    private OperationLog convertToEntity(OperationLogInfo logInfo) {
        OperationLog operationLog = new OperationLog();
        
        // 基本信息
        operationLog.setOperationModule(logInfo.getModule());
        operationLog.setOperationTypeEnum(logInfo.getType());
        operationLog.setOperationDesc(logInfo.getDescription());
        operationLog.setOperationStatusEnum(logInfo.getStatus());
        operationLog.setErrorMessage(logInfo.getErrorMessage());
        operationLog.setExecutionTime(logInfo.getExecutionTime());
        operationLog.setOperationTime(logInfo.getOperationTime());

        // 用户信息
        operationLog.setUserId(logInfo.getUserId());
        operationLog.setUsername(logInfo.getUsername());

        // 请求信息
        operationLog.setRequestMethod(logInfo.getRequestMethod());
        operationLog.setRequestUrl(logInfo.getRequestUrl());
        operationLog.setRequestParams(logInfo.getRequestParams());

        // 响应信息
        operationLog.setResponseResult(logInfo.getResponseResult());
        operationLog.setResponseStatus(logInfo.getResponseStatus());
        operationLog.setResponseMessage(logInfo.getResponseMessage());

        // 系统信息
        operationLog.setClientIp(logInfo.getClientIp());
        operationLog.setUserAgent(logInfo.getUserAgent());

        // 地理位置信息（异步获取）
        String location = getLocationInfo(logInfo);
        operationLog.setLocation(location);

        return operationLog;
    }

    /**
     * 获取地理位置信息
     *
     * @param logInfo 操作日志信息
     * @return 地理位置字符串
     */
    private String getLocationInfo(OperationLogInfo logInfo) {
        try {
            // 获取客户端IP地址
            String clientIp = logInfo.getClientIp();

            if (StringUtils.hasText(clientIp) && addressResolveService != null) {
                // 获取地理位置信息
                String location = addressResolveService.getLocationByIP(clientIp);
                if (StringUtils.hasText(location)) {
                    log.debug("获取操作日志地理位置信息完成, ip: {}, location: {}",
                            clientIp, location);
                    return location;
                }
            }
        } catch (Exception e) {
            log.debug("获取地理位置信息失败, error: {}", e.getMessage());
        }

        return null;
    }
}
