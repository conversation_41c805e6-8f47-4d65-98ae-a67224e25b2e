package com.akey.core.dao.service;

import com.akey.core.dao.entity.User;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 用户Service接口
 * 
 * <p>提供用户相关的业务操作</p>
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface UserService {

    /**
     * 1. 创建用户
     *
     * @param user 用户信息
     * @return 创建成功返回true
     * @throws BusinessException 当参数无效、用户名已存在、用户类型不存在或数据库操作失败时抛出
     */
    boolean createUser(User user);

    /**
     * 2. 修改用户
     *
     * @param user 用户信息（必须包含id）
     * @return 修改成功返回true
     * @throws BusinessException 当参数无效、用户不存在、用户名已存在、用户类型不存在或数据库操作失败时抛出
     */
    boolean updateUser(User user);

    /**
     * 3. 根据用户名获取用户信息
     * 
     * @param username 用户名
     * @return 用户信息，如果不存在则返回null
     */
    User getUserByUsername(String username);

    /**
     * 4. 通过用户ID设置用户锁定状态
     * 
     * @param userId 用户ID
     * @param accountLocked 锁定状态（0:未锁定,1:锁定）
     * @return 设置成功返回true，失败返回false
     */
    boolean setUserLockStatus(String userId, Integer accountLocked);

    /**
     * 5. 修改登录时间+ 登录IP
     * 
     * @param userId 用户ID
     * @param loginIp 登录IP
     * @return 修改成功返回true，失败返回false
     */
    boolean updateLoginInfo(String userId, String loginIp);

    /**
     * 6. 通过用户ID修改密码
     * 
     * @param userId 用户ID
     * @param newPassword 新密码（已加密）
     * @return 修改成功返回true，失败返回false
     */
    boolean updatePassword(String userId, String newPassword);

    /**
     * 7. 通过用户ID重置密码
     * 
     * @param userId 用户ID
     * @param resetPassword 重置密码（已加密）
     * @return 重置成功返回true，失败返回false
     */
    boolean resetPassword(String userId, String resetPassword);

    /**
     * 8. 分页获取用户列表(筛选条件：用户名，用户类型，账户状态)
     * 
     * @param page 分页参数
     * @param username 用户名（模糊查询，可为空）
     * @param userTypeId 用户类型ID（可为空）
     * @param accountLocked 账户状态（可为空，查询全部）
     * @return 分页结果
     */
    IPage<User> getUserPageList(Page<User> page, String username, String userTypeId, Integer accountLocked);

    /**
     * 9. 判断用户是否已存在
     * 
     * @param username 用户名
     * @param excludeId 要排除的用户ID（用于修改时排除自己），可为空
     * @return 存在返回true，不存在返回false
     */
    boolean existsByUsername(String username, String excludeId);

    /**
     * 判断用户是否设置谷歌验证
     * 
     * @param userId 用户ID
     * @return 设置返回true，未设置返回false
     */
    boolean isUserGoogleAuth(String userId);

    /**
     * 根据ID查询用户
     * 
     * @param id 用户ID
     * @return 用户信息，如果不存在则返回null
     */
    User getUserById(String id);

    /**
     * 检查用户名和密码是否匹配（用于登录验证）
     * 
     * @param username 用户名
     * @param password 密码（已加密）
     * @return 匹配返回用户信息，不匹配返回null
     */
    User verifyUser(String username, String password);

    /**
     * 检查用户是否被锁定
     * 
     * @param userId 用户ID
     * @return 锁定返回true，未锁定返回false
     */
    boolean isUserLocked(String userId);

    /**
     * 更新用户谷歌验证KEY
     *
     * @param userId 用户ID
     * @param googleAuthKey 谷歌验证KEY
     * @return 更新成功返回true，失败返回false
     */
    boolean updateGoogleAuthKey(String userId, String googleAuthKey);

    /**
     * 重置用户谷歌验证KEY
     *
     * @param userId 用户ID
     * @return 重置成功返回true，失败返回false
     */
    boolean resetGoogleAuthKey(String userId);

    /**
     * 删除用户
     *
     * @param userId 用户ID
     * @return 删除成功返回true，失败返回false
     */
    boolean deleteUser(String userId);

    /**
     * 批量删除用户
     *
     * @param userIds 用户ID列表
     * @return 删除成功的用户数量
     */
    int batchDeleteUsers(List<String> userIds);

    /**
     * 批量设置用户锁定状态
     *
     * @param userIds 用户ID列表
     * @param accountLocked 锁定状态（0:未锁定,1:锁定）
     * @return 设置成功的用户数量
     */
    int batchSetUserLockStatus(List<String> userIds, Integer accountLocked);

    /**
     * 用户修改密码
     *
     * <p>用户自身修改密码，需要验证旧密码</p>
     *
     * @param userId 用户ID
     * @param oldPassword 旧密码（明文）
     * @param newPassword 新密码（明文）
     * @return 修改成功返回true
     * @throws BusinessException 当参数无效、用户不存在、旧密码错误、新密码与旧密码相同或数据库操作失败时抛出
     */
    boolean changePassword(String userId, String oldPassword, String newPassword);
}