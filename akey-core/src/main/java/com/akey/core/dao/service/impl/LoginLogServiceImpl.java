package com.akey.core.dao.service.impl;

import com.akey.common.entity.DeviceInfo;
import com.akey.common.enums.RiskLevelEnum;
import com.akey.core.dao.entity.LoginLog;
import com.akey.core.dao.mapper.LoginLogMapper;
import com.akey.core.dao.service.LoginLogService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 登录日志Service实现类
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LoginLogServiceImpl extends ServiceImpl<LoginLogMapper, LoginLog> implements LoginLogService {

    private final LoginLogMapper loginLogMapper;

    /**
     * 记录登录成功日志
     *
     * @param userId 用户ID
     * @param username 用户名
     * @param sessionId 会话ID
     * @param deviceInfo 设备信息
     * @return 登录日志记录
     */
    @Override
    public LoginLog recordSuccessLogin(String userId, String username, String sessionId,
                                      DeviceInfo deviceInfo) {
        LoginLog loginLog = new LoginLog();
        loginLog.setUserId(userId);
        loginLog.setUsername(username);
        loginLog.setSessionId(sessionId);
        loginLog.setLoginTime(LocalDateTime.now());
        loginLog.setLoginStatus(1); // 成功
        
        // 设置设备信息
        if (deviceInfo != null) {
            setDeviceInfoToLoginLog(loginLog, deviceInfo);
        }
        
        return saveLoginLog(loginLog);
    }

    /**
     * 记录登录失败日志
     *
     * @param username 用户名（可能为空）
     * @param failureReason 失败原因
     * @param deviceInfo 设备信息
     * @return 登录日志记录
     */
    @Override
    public LoginLog recordFailureLogin(String username, String failureReason,
                                      DeviceInfo deviceInfo) {
        LoginLog loginLog = new LoginLog();
        loginLog.setUsername(username);
        loginLog.setFailureReason(failureReason);
        loginLog.setLoginTime(LocalDateTime.now());
        loginLog.setLoginStatus(0); // 失败
        
        // 设置设备信息
        if (deviceInfo != null) {
            setDeviceInfoToLoginLog(loginLog, deviceInfo);
        }
        
        return saveLoginLog(loginLog);
    }

    /**
     * 记录登录日志（通用方法）
     *
     * @param loginLog 登录日志对象
     * @return 保存后的登录日志记录
     */
    @Override
    public LoginLog saveLoginLog(LoginLog loginLog) {
        try {
            save(loginLog);
            log.debug("保存登录日志成功, id: {}, username: {}, status: {}", 
                     loginLog.getId(), loginLog.getUsername(), loginLog.getLoginStatus());
            return loginLog;
        } catch (Exception e) {
            log.error("保存登录日志失败, username: {}, error: {}", 
                     loginLog.getUsername(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 分页查询登录日志
     *
     * @param page 分页参数
     * @param userId 用户ID（可选）
     * @param username 用户名（可选）
     * @param clientIp 客户端IP（可选）
     * @param loginStatus 登录状态（可选）
     * @param isSuspicious 是否可疑（可选）
     * @param riskLevel 风险等级（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 分页结果
     */
    @Override
    public IPage<LoginLog> getLoginLogPage(Page<LoginLog> page, String userId, String username,
                                          String clientIp, Integer loginStatus, Integer isSuspicious,
                                          String riskLevel, LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<LoginLog> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(StringUtils.hasText(userId), LoginLog::getUserId, userId)
               .like(StringUtils.hasText(username), LoginLog::getUsername, username)
               .eq(StringUtils.hasText(clientIp), LoginLog::getClientIp, clientIp)
               .eq(loginStatus != null, LoginLog::getLoginStatus, loginStatus)
               .eq(isSuspicious != null, LoginLog::getIsSuspicious, isSuspicious)
               .eq(StringUtils.hasText(riskLevel), LoginLog::getRiskLevel, riskLevel)
               .ge(startTime != null, LoginLog::getLoginTime, startTime)
               .le(endTime != null, LoginLog::getLoginTime, endTime)
               .orderByDesc(LoginLog::getLoginTime);

        return page(page, wrapper);
    }

    /**
     * 获取用户最近的登录记录
     *
     * @param userName 用户名称
     * @param limit 限制数量
     * @return 登录记录列表
     */
    @Override
    public List<LoginLog> getRecentLoginsByUser(String userName, Integer limit) {
        // 使用MyBatis-Plus条件构造器替代手写SQL
        LambdaQueryWrapper<LoginLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LoginLog::getUsername, userName)
               .orderByDesc(LoginLog::getLoginTime)
               .last("LIMIT " + limit);

        return list(wrapper);
    }

    /**
     * 统计用户在指定时间范围内的登录次数
     *
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param loginStatus 登录状态（可选）
     * @return 登录次数
     */
    @Override
    public Long countLoginsByUser(String userId, LocalDateTime startTime, LocalDateTime endTime, Integer loginStatus) {
        // 使用MyBatis-Plus条件构造器替代手写SQL
        LambdaQueryWrapper<LoginLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LoginLog::getUserId, userId)
               .between(LoginLog::getLoginTime, startTime, endTime)
               .eq(loginStatus != null, LoginLog::getLoginStatus, loginStatus);

        return count(wrapper);
    }

    /**
     * 统计IP地址在指定时间范围内的登录次数
     *
     * @param clientIp 客户端IP
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param loginStatus 登录状态（可选）
     * @return 登录次数
     */
    @Override
    public Long countLoginsByIp(String clientIp, LocalDateTime startTime, LocalDateTime endTime, Integer loginStatus) {
        // 使用MyBatis-Plus条件构造器替代手写SQL
        LambdaQueryWrapper<LoginLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LoginLog::getClientIp, clientIp)
               .between(LoginLog::getLoginTime, startTime, endTime)
               .eq(loginStatus != null, LoginLog::getLoginStatus, loginStatus);

        return count(wrapper);
    }



    /**
     * 评估登录风险等级
     *
     * @param userId 用户ID
     * @param deviceInfo 设备信息
     * @param isNewDevice 是否新设备
     * @param isNewLocation 是否新地点
     * @return 风险等级
     */
    @Override
    public String assessRiskLevel(String userId, DeviceInfo deviceInfo, boolean isNewDevice, boolean isNewLocation) {
        int riskScore = 0;
        
        // 新设备风险
        if (isNewDevice) {
            riskScore += 20;
        }
        
        // 新地点风险
        if (isNewLocation) {
            riskScore += 15;
        }
        
        // 设备信息风险评估
        if (deviceInfo != null) {
            if (Boolean.TRUE.equals(deviceInfo.getIsBot())) {
                riskScore += 30;
            }
            if (Boolean.TRUE.equals(deviceInfo.getIsProxy())) {
                riskScore += 25;
            }
            if (!StringUtils.hasText(deviceInfo.getUserAgent())) {
                riskScore += 20;
            }
        }
        
        // 根据分数确定风险等级
        if (riskScore >= 50) {
            return RiskLevelEnum.HIGH.getCode();
        } else if (riskScore >= 25) {
            return RiskLevelEnum.MEDIUM.getCode();
        } else {
            return RiskLevelEnum.LOW.getCode();
        }
    }

    /**
     * 检测可疑登录行为
     *
     * @param userId 用户ID
     * @param deviceInfo 设备信息
     * @return 可疑原因列表
     */
    @Override
    public List<String> detectSuspiciousActivity(String userId, DeviceInfo deviceInfo) {
        List<String> suspiciousReasons = new ArrayList<>();
        
        if (deviceInfo == null) {
            return suspiciousReasons;
        }
        
        // 检查是否为机器人
        if (Boolean.TRUE.equals(deviceInfo.getIsBot())) {
            suspiciousReasons.add("疑似机器人访问");
        }
        
        // 检查是否使用代理
        if (Boolean.TRUE.equals(deviceInfo.getIsProxy())) {
            suspiciousReasons.add("使用代理访问");
        }
        
        // 检查User-Agent异常
        String userAgent = deviceInfo.getUserAgent();
        if (!StringUtils.hasText(userAgent) || userAgent.length() < 20) {
            suspiciousReasons.add("User-Agent异常");
        }
        
        // 检查频繁登录
        if (StringUtils.hasText(userId)) {
            LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
            Long recentLogins = countLoginsByUser(userId, oneHourAgo, LocalDateTime.now(), null);
            if (recentLogins != null && recentLogins > 10) {
                suspiciousReasons.add("频繁登录");
            }
        }
        
        return suspiciousReasons;
    }

    /**
     * 获取登录统计信息
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计信息
     */
    @Override
    public Map<String, Object> getLoginStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> statistics = new HashMap<>();

        // 基础统计
        LambdaQueryWrapper<LoginLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.between(LoginLog::getLoginTime, startTime, endTime);

        Long totalLogins = count(wrapper);
        statistics.put("totalLogins", totalLogins);

        // 成功登录统计
        wrapper.eq(LoginLog::getLoginStatus, 1);
        Long successLogins = count(wrapper);
        statistics.put("successLogins", successLogins);

        // 失败登录统计
        Long failureLogins = totalLogins - successLogins;
        statistics.put("failureLogins", failureLogins);

        // 成功率
        Double successRate = totalLogins > 0 ? (successLogins.doubleValue() / totalLogins.doubleValue()) * 100 : 0.0;
        statistics.put("successRate", Math.round(successRate * 100.0) / 100.0);

        // 独立用户数和独立IP数通过Mapper查询
        Long uniqueUsers = loginLogMapper.countUniqueUsers(startTime, endTime);
        statistics.put("uniqueUsers", uniqueUsers);

        Long uniqueIps = loginLogMapper.countUniqueIps(startTime, endTime);
        statistics.put("uniqueIps", uniqueIps);

        // 可疑登录统计
        wrapper.clear();
        wrapper.between(LoginLog::getLoginTime, startTime, endTime);
        wrapper.eq(LoginLog::getIsSuspicious, 1);
        Long suspiciousLogins = count(wrapper);
        statistics.put("suspiciousLogins", suspiciousLogins);

        // 高风险登录统计
        wrapper.clear();
        wrapper.between(LoginLog::getLoginTime, startTime, endTime);
        wrapper.eq(LoginLog::getRiskLevel, "HIGH");
        Long highRiskLogins = count(wrapper);
        statistics.put("highRiskLogins", highRiskLogins);

        // 登录状态统计
        List<Map<String, Object>> statusStats = loginLogMapper.getLoginStatusStatistics(startTime, endTime);
        statistics.put("statusStatistics", statusStats);

        // 风险等级统计
        List<Map<String, Object>> riskStats = loginLogMapper.getRiskLevelStatistics(startTime, endTime);
        statistics.put("riskStatistics", riskStats);

        // 每小时登录统计
        List<Map<String, Object>> hourlyStats = loginLogMapper.getHourlyLoginStatistics(startTime, endTime);
        statistics.put("hourlyStatistics", hourlyStats);

        return statistics;
    }



    /**
     * 清理过期的登录日志（物理删除）
     *
     * @param beforeTime 清理此时间之前的记录
     * @return 清理的记录数
     */
    @Override
    public Long cleanupExpiredLogs(LocalDateTime beforeTime) {
        return physicalCleanupExpiredLogs(beforeTime);
    }

    /**
     * 物理删除过期的登录日志
     *
     * @param beforeTime 删除此时间之前的记录
     * @return 删除的记录数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long physicalCleanupExpiredLogs(LocalDateTime beforeTime) {
        if (beforeTime == null) {
            log.warn("删除时间为空，无法执行物理删除操作");
            return 0L;
        }

        log.warn("开始物理删除过期登录日志, beforeTime: {}", beforeTime);

        try {
            Long deletedCount = loginLogMapper.physicalCleanupExpiredLogs(beforeTime);
            log.warn("物理删除过期登录日志完成, 删除记录数: {}", deletedCount);
            return deletedCount;
        } catch (Exception e) {
            log.error("物理删除过期登录日志失败", e);
            throw e;
        }
    }

    /**
     * 物理清空所有登录日志
     *
     * @return 删除的记录数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long physicalClearAllLogs() {
        log.warn("开始物理清空所有登录日志");

        try {
            Long deletedCount = loginLogMapper.physicalClearAllLogs();
            log.warn("物理清空所有登录日志完成, 删除记录数: {}", deletedCount);
            return deletedCount;
        } catch (Exception e) {
            log.error("物理清空所有登录日志失败", e);
            throw e;
        }
    }

    /**
     * 获取热门登录IP地址统计
     *
     * @param limit 限制数量
     * @param days 统计天数
     * @return IP地址统计列表
     */
    @Override
    public List<Map<String, Object>> getTopLoginIps(Integer limit, Integer days) {
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusDays(days);

        return loginLogMapper.getTopLoginIps(startTime, endTime, limit);
    }

    /**
     * 获取设备类型统计
     *
     * @param days 统计天数
     * @return 设备类型统计
     */
    @Override
    public Map<String, Object> getDeviceStatistics(Integer days) {
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusDays(days);

        Map<String, Object> result = new HashMap<>();

        // 设备类型统计
        List<Map<String, Object>> deviceTypeStats = loginLogMapper.getDeviceTypeStatistics(startTime, endTime);
        result.put("deviceTypeStats", deviceTypeStats);

        // 操作系统统计
        List<Map<String, Object>> osStats = loginLogMapper.getOsStatistics(startTime, endTime);
        result.put("osStats", osStats);

        // 浏览器统计
        List<Map<String, Object>> browserStats = loginLogMapper.getBrowserStatistics(startTime, endTime);
        result.put("browserStats", browserStats);

        return result;
    }

    /**
     * 获取每日登录统计
     *
     * @param days 统计天数
     * @return 每日登录统计
     */
    @Override
    public List<Map<String, Object>> getDailyLoginStatistics(Integer days) {
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusDays(days);

        return loginLogMapper.getDailyLoginStatistics(startTime, endTime);
    }

    /**
     * 获取地理位置统计
     *
     * @param days 统计天数
     * @return 地理位置统计
     */
    @Override
    public Map<String, Object> getLocationStatistics(Integer days) {
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusDays(days);

        Map<String, Object> result = new HashMap<>();

        // 地理位置统计
        List<Map<String, Object>> locationStats = loginLogMapper.getLocationStatistics(startTime, endTime);
        result.put("locationStats", locationStats);

        return result;
    }

    /**
     * 设置设备信息到登录日志
     *
     * <p>将DeviceInfo中的信息映射到LoginLog对象中</p>
     * <p>包括网络信息、设备信息等（地理位置信息将在异步处理中获取）</p>
     *
     * @param loginLog 登录日志对象
     * @param deviceInfo 设备信息对象
     */
    private void setDeviceInfoToLoginLog(LoginLog loginLog, DeviceInfo deviceInfo) {
        // 网络信息
        loginLog.setClientIp(deviceInfo.getClientIp());
        loginLog.setIsProxy(deviceInfo.getIsProxy() != null && deviceInfo.getIsProxy() ? 1 : 0);
        loginLog.setProxyChain(deviceInfo.getProxyIp());

        // 注意：地理位置信息（location）将在AsyncLoginLogFactory中异步获取

        // 设备信息
        loginLog.setUserAgent(deviceInfo.getUserAgent());
        loginLog.setDeviceType(deviceInfo.getDeviceType());
        loginLog.setDeviceBrand(deviceInfo.getDeviceBrand());
        loginLog.setDeviceModel(deviceInfo.getDeviceModel());
        loginLog.setOsName(deviceInfo.getOperatingSystem());
        loginLog.setOsVersion(deviceInfo.getOsVersion());
        loginLog.setBrowserName(deviceInfo.getBrowserName());
        loginLog.setBrowserVersion(deviceInfo.getBrowserVersion());
        loginLog.setDeviceFingerprint(deviceInfo.getDeviceFingerprint());

        // 其他信息
        loginLog.setLanguage(deviceInfo.getLanguage());
        loginLog.setTimezone(deviceInfo.getTimezone());

        // 风险等级
        loginLog.setRiskLevel(deviceInfo.getRiskLevel() != null ? deviceInfo.getRiskLevel() : RiskLevelEnum.LOW.getCode());
    }
}
