package com.akey.core.dao.service;

import com.akey.common.entity.DeviceInfo;
import com.akey.core.dao.entity.LoginLog;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 登录日志Service接口
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface LoginLogService extends IService<LoginLog> {

    /**
     * 记录登录成功日志
     *
     * @param userId 用户ID
     * @param username 用户名
     * @param sessionId 会话ID
     * @param deviceInfo 设备信息
     * @return 登录日志记录
     */
    LoginLog recordSuccessLogin(String userId, String username, String sessionId,
                               DeviceInfo deviceInfo);

    /**
     * 记录登录失败日志
     *
     * @param username 用户名（可能为空）
     * @param failureReason 失败原因
     * @param deviceInfo 设备信息
     * @return 登录日志记录
     */
    LoginLog recordFailureLogin(String username, String failureReason,
                               DeviceInfo deviceInfo);

    /**
     * 记录登录日志（通用方法）
     * 
     * @param loginLog 登录日志对象
     * @return 保存后的登录日志记录
     */
    LoginLog saveLoginLog(LoginLog loginLog);

    /**
     * 分页查询登录日志
     *
     * @param page 分页参数
     * @param userId 用户ID（可选）
     * @param username 用户名（可选）
     * @param clientIp 客户端IP（可选）
     * @param loginStatus 登录状态（可选）
     * @param isSuspicious 是否可疑（可选）
     * @param riskLevel 风险等级（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 分页结果
     */
    IPage<LoginLog> getLoginLogPage(Page<LoginLog> page, String userId, String username,
                                   String clientIp, Integer loginStatus, Integer isSuspicious,
                                   String riskLevel, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取用户最近的登录记录
     * 
     * @param userName 用户名称
     * @param limit 限制数量
     * @return 登录记录列表
     */
    List<LoginLog> getRecentLoginsByUser(String userName, Integer limit);

    /**
     * 统计用户在指定时间范围内的登录次数
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param loginStatus 登录状态（可选）
     * @return 登录次数
     */
    Long countLoginsByUser(String userId, LocalDateTime startTime, LocalDateTime endTime, Integer loginStatus);

    /**
     * 统计IP地址在指定时间范围内的登录次数
     * 
     * @param clientIp 客户端IP
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param loginStatus 登录状态（可选）
     * @return 登录次数
     */
    Long countLoginsByIp(String clientIp, LocalDateTime startTime, LocalDateTime endTime, Integer loginStatus);



    /**
     * 评估登录风险等级
     * 
     * @param userId 用户ID
     * @param deviceInfo 设备信息
     * @param isNewDevice 是否新设备
     * @param isNewLocation 是否新地点
     * @return 风险等级
     */
    String assessRiskLevel(String userId, DeviceInfo deviceInfo, boolean isNewDevice, boolean isNewLocation);

    /**
     * 检测可疑登录行为
     * 
     * @param userId 用户ID
     * @param deviceInfo 设备信息
     * @return 可疑原因列表
     */
    List<String> detectSuspiciousActivity(String userId, DeviceInfo deviceInfo);

    /**
     * 获取登录统计信息
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计信息
     */
    Map<String, Object> getLoginStatistics(LocalDateTime startTime, LocalDateTime endTime);



    /**
     * 清理过期的登录日志
     *
     * @param beforeTime 清理此时间之前的记录
     * @return 清理的记录数
     */
    Long cleanupExpiredLogs(LocalDateTime beforeTime);

    /**
     * 物理删除过期的登录日志
     *
     * <p>注意：此方法执行物理删除，会真正从数据库中移除记录，包括已逻辑删除的记录</p>
     * <p>与cleanupExpiredLogs不同，此方法不使用MyBatis-Plus的逻辑删除机制</p>
     *
     * @param beforeTime 删除此时间之前的记录
     * @return 删除的记录数
     */
    Long physicalCleanupExpiredLogs(LocalDateTime beforeTime);

    /**
     * 物理清空所有登录日志
     *
     * <p>注意：此方法执行物理删除，会真正从数据库中移除所有记录，包括已逻辑删除的记录</p>
     * <p>此操作不可逆，请谨慎使用</p>
     *
     * @return 删除的记录数
     */
    Long physicalClearAllLogs();

    /**
     * 获取热门登录IP地址统计
     *
     * @param limit 限制数量
     * @param days 统计天数
     * @return IP地址统计列表
     */
    List<Map<String, Object>> getTopLoginIps(Integer limit, Integer days);

    /**
     * 获取设备类型统计
     *
     * @param days 统计天数
     * @return 设备类型统计
     */
    Map<String, Object> getDeviceStatistics(Integer days);

    /**
     * 获取每日登录统计
     *
     * @param days 统计天数
     * @return 每日登录统计
     */
    List<Map<String, Object>> getDailyLoginStatistics(Integer days);

    /**
     * 获取地理位置统计
     *
     * @param days 统计天数
     * @return 地理位置统计
     */
    Map<String, Object> getLocationStatistics(Integer days);

}
