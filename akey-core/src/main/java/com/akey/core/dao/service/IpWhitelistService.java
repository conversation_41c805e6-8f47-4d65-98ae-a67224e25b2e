package com.akey.core.dao.service;

import com.akey.common.enums.EnableStatusEnum;
import com.akey.common.enums.IpWhitelistTypeEnum;
import com.akey.core.dao.entity.IpWhitelist;
import com.akey.core.vo.IpWhitelistWithUserVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * IP白名单Service接口
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
public interface IpWhitelistService extends IService<IpWhitelist> {

    /**
     * 分页查询IP白名单列表
     *
     * <p>通过连表查询获取IP白名单和关联的用户信息</p>
     * <p>支持多种筛选条件和模糊查询</p>
     *
     * @param page 分页参数
     * @param whitelistType 白名单类型（可选）
     * @param ipAddress IP地址（模糊查询，可选）
     * @param userId 用户ID（可选）
     * @param username 用户名称（可选）
     * @param enableStatus 启用状态（可选）
     * */
    IPage<IpWhitelistWithUserVO> selectListPage(
            Page<IpWhitelistWithUserVO> page,
            IpWhitelistTypeEnum whitelistType,
            String ipAddress,
            String userId,
            String username,
            EnableStatusEnum enableStatus
    );


    /**
     * 根据IP地址和用户ID查询IP白名单
     *
     * @param whitelistType 白名单类型
     * @param ipAddress IP地址
     * @param userId 用户ID
     * @param enableStatus 启用状态(可选)
     * @return 白名单实体类
     */
    IpWhitelist selectWhite(IpWhitelistTypeEnum whitelistType, String ipAddress, String userId, EnableStatusEnum enableStatus);

    /**
     * 验证IP地址格式是否有效
     *
     * @param ipAddress IP地址
     * @return 是否有效
     */
    boolean isValidIpAddress(String ipAddress);

    /**
     * 添加IP白名单
     *
     * @param ipWhitelist IP白名单实体类
     * @return 添加成功返回true，失败返回false
     */
    boolean addIpWhitelist(IpWhitelist ipWhitelist);

    /**
     * 更新IP白名单
     *
     * @param ipWhitelist IP白名单实体类
     * @return 更新成功返回true，失败返回false
     */
    boolean updateIpWhitelist(IpWhitelist ipWhitelist);

    /**
     * 删除IP白名单
     *
     * @param id IP白名单ID
     * @return 删除成功返回true，失败返回false
     */
    boolean deleteIpWhitelist(String id);

    /**
     * 批量删除IP白名单
     *
     * @param ids IP白名单ID列表
     * @return 实际删除成功的记录数
     */
    int batchDeleteIpWhitelist(List<String> ids);

    /**
     * 批量更新IP白名单状态
     *
     * @param ids IP白名单ID列表
     * @param enableStatus 要设置的状态
     * @return 实际更新成功的记录数
     */
    int batchUpdateStatus(List<String> ids, EnableStatusEnum enableStatus);

    /**
     * 清空用户白名单
     *
     * @param userId 用户ID
     * @return 重置成功返回true，失败返回false
     */
    boolean deleteIpWhitelistByUserId(String userId);

    /**
     * 验证IP白名单是否通过验证
     *
     * <p>业务逻辑说明：</p>
     * <ul>
     *   <li>如果用户没有任何白名单记录，说明未开启白名单校验，直接返回true</li>
     *   <li>如果用户有白名单记录，则检查传入的IP地址是否存在于该用户的启用状态白名单中</li>
     *   <li>只有当IP地址存在且状态为启用时，才返回true</li>
     * </ul>
     *
     * @param whitelistType 白名单类型
     * @param ipAddress IP地址
     * @param userId 用户ID
     * @return true:验证通过（未开启白名单或IP在白名单中），false:验证失败（开启了白名单但IP不在白名单中或状态为禁用）
     */
    boolean validateIpWhitelist(IpWhitelistTypeEnum whitelistType, String ipAddress, String userId);

}
