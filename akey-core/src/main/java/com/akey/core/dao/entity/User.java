package com.akey.core.dao.entity;

import com.akey.common.enums.AccountLockStatusEnum;
import com.akey.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户实体类
 * 
 * <p>对应数据库表：sys_user</p>
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user")
public class User extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 用户名
     * 用于登录的唯一标识
     */
    @TableField("username")
    private String username;

    /**
     * 用户密码
     * 存储加密后的密码
     */
    @TableField("password")
    private String password;

    /**
     * 用户类型ID
     * 关联用户类型表的主键
     */
    @TableField("user_type_id")
    private String userTypeId;

    /**
     * 谷歌验证KEY
     * 用于谷歌验证器的密钥
     */
    @TableField("google_auth_key")
    private String googleAuthKey;

    /**
     * 账户是否锁定
     * 0: 未锁定，可以正常登录
     * 1: 已锁定，无法登录
     * 
     * @see com.akey.common.enums.AccountLockStatusEnum
     */
    @TableField("account_locked")
    private AccountLockStatusEnum accountLocked;

    /**
     * 密码最后修改时间
     * 记录用户最后一次修改密码的时间
     */
    @TableField("password_update_time")
    private LocalDateTime passwordUpdateTime;

    /**
     * 最后登录时间
     * 记录用户最后一次成功登录的时间
     */
    @TableField("last_login_time")
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     * 记录用户最后一次成功登录的IP地址
     */
    @TableField("last_login_ip")
    private String lastLoginIp;
} 