package com.akey.core.dao.mapper;

import com.akey.common.entity.BaseEntity;

/**
 * 基础Mapper接口
 * 
 * <p>功能说明：</p>
 * <ul>
 *   <li>继承MyBatis-Plus的BaseMapper，提供基础CRUD操作</li>
 *   <li>所有业务Mapper接口都应该继承此接口</li>
 *   <li>自动获得insert、delete、update、select等基础方法</li>
 * </ul>
 * 
 * <p>提供的基础方法包括：</p>
 * <ul>
 *   <li>insert(T entity) - 插入一条记录</li>
 *   <li>deleteById(Serializable id) - 根据ID删除</li>
 *   <li>updateById(T entity) - 根据ID更新</li>
 *   <li>selectById(Serializable id) - 根据ID查询</li>
 *   <li>selectList(Wrapper&lt;T&gt; queryWrapper) - 条件查询列表</li>
 *   <li>selectPage(Page&lt;T&gt; page, Wrapper&lt;T&gt; queryWrapper) - 分页查询</li>
 * </ul>
 *
 * @param <T> 实体类型，必须继承BaseEntity
 * <AUTHOR>
 * @since 1.0.0
 */
public interface BaseMapper<T extends BaseEntity> extends com.baomidou.mybatisplus.core.mapper.BaseMapper<T> {
    
    // 继承MyBatis-Plus的BaseMapper，无需额外定义方法
    // 所有基础的CRUD操作都已由MyBatis-Plus提供
    
    /*
     * 常用方法说明：
     * 
     * 1. 插入操作：
     *    - insert(T entity): 插入一条记录
     * 
     * 2. 删除操作：
     *    - deleteById(Serializable id): 根据ID删除（逻辑删除）
     *    - delete(Wrapper<T> queryWrapper): 根据条件删除
     * 
     * 3. 更新操作：
     *    - updateById(T entity): 根据ID更新
     *    - update(T entity, Wrapper<T> updateWrapper): 根据条件更新
     * 
     * 4. 查询操作：
     *    - selectById(Serializable id): 根据ID查询
     *    - selectList(Wrapper<T> queryWrapper): 条件查询列表
     *    - selectPage(Page<T> page, Wrapper<T> queryWrapper): 分页查询
     *    - selectCount(Wrapper<T> queryWrapper): 查询总数
     */
} 