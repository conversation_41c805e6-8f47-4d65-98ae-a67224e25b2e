package com.akey.core.dao.service.impl;


import com.akey.core.dao.service.MenuService;
import com.akey.core.dao.service.UserTypeMenuService;
import com.akey.core.dao.entity.Menu;
import com.akey.core.dao.mapper.MenuMapper;
import com.akey.core.dao.mapper.UserTypeMenuMapper;
import com.akey.common.enums.BuiltinEnum;
import com.akey.common.enums.EnableStatusEnum;
import com.akey.common.enums.MenuTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import org.springframework.util.CollectionUtils;


/**
 * 菜单Service实现类
 * 
 * <p>使用MyBatis-Plus的QueryWrapper进行单表操作</p>
 * <p>实现菜单的树形结构构建和层级管理</p>
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MenuServiceImpl implements MenuService {

    private final MenuMapper menuMapper;
    private final UserTypeMenuMapper userTypeMenuMapper;
    private final UserTypeMenuService userTypeMenuService;

    /**
     * 获取所有菜单
     * 
     * @param menuName 菜单名称（模糊查询，可为空）
     * @param menuType 菜单类型
     * @param status 菜单状态（可为空，查询全部）
     * @return 菜单列表
     */
    @Override
    public List<Menu> getMenuList(String menuName, MenuTypeEnum menuType, EnableStatusEnum status) {
        log.info("开始获取菜单树形结构, menuName: {}, status: {}", menuName, status);
        
        // 构建查询条件
        LambdaQueryWrapper<Menu> queryWrapper = new LambdaQueryWrapper<>();
        
        // 菜单名称模糊查询
        if (StringUtils.hasText(menuName)) {
            queryWrapper.like(Menu::getMenuName, menuName);
        }
        
        // 菜单状态筛选
        if (status != null) {
            queryWrapper.eq(Menu::getStatus, status);
        }

        // 菜单类型筛选
        if (menuType != null) {
            queryWrapper.eq(Menu::getMenuType, menuType);
        }
        
        // 按排序号和创建时间排序
        queryWrapper.orderByAsc(Menu::getSortOrder, Menu::getCreateTime);
        
        // 查询所有符合条件的菜单
        return menuMapper.selectList(queryWrapper);
    }

    /**
     * 根据ID获取菜单信息
     * 
     * @param id 菜单ID
     * @return 菜单信息，如果不存在则返回null
     */
    @Override
    public Menu getMenuById(String id) {
        if (!StringUtils.hasText(id)) {
            return null;
        }
        
        return menuMapper.selectById(id);
    }

    /**
     * 通过ID修改菜单信息
     * 
     * @param menu 菜单信息（必须包含id）
     * @return 修改成功返回true，失败返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMenu(Menu menu) {
        log.info("开始修改菜单, id: {}, menuName: {}", menu.getId(), menu.getMenuName());
        
        // 校验参数
        if (!StringUtils.hasText(menu.getId())) {
            log.warn("菜单ID不能为空");
            return false;
        }
        
        if (!StringUtils.hasText(menu.getMenuName())) {
            log.warn("菜单名称不能为空");
            return false;
        }
        
        if (!StringUtils.hasText(menu.getMenuCode())) {
            log.warn("菜单编码不能为空");
            return false;
        }
        
        // 检查菜单是否存在
        Menu existingMenu = getMenuById(menu.getId());
        if (existingMenu == null) {
            log.warn("菜单不存在, id: {}", menu.getId());
            return false;
        }

        // 检查是否为内置菜单，内置菜单不允许修改状态
        if (existingMenu.getIsBuiltin() == BuiltinEnum.BUILTIN &&
            menu.getStatus() != null &&
            !menu.getStatus().equals(existingMenu.getStatus())) {
            log.warn("内置菜单不允许修改状态, id: {}, menuName: {}", menu.getId(), existingMenu.getMenuName());
            return false;
        }

        // 检查菜单编码是否已被其他菜单使用
        if (existsByMenuCode(menu.getMenuCode(), menu.getId())) {
            log.warn("菜单编码已存在: {}", menu.getMenuCode());
            return false;
        }
        
        // 验证父菜单是否有效（如果有父菜单）
        if (StringUtils.hasText(menu.getParentId())) {
            if (!isValidParent(menu.getParentId(), menu.getId())) {
                log.warn("无效的父菜单ID: {}", menu.getParentId());
                return false;
            }
        }
        
        // 验证菜单类型
        if (menu.getMenuType() != null && MenuTypeEnum.fromValue(menu.getMenuType().getValue()) == null) {
            log.warn("无效的菜单类型: {}", menu.getMenuType());
            return false;
        }
        
        // 验证状态
        if (menu.getStatus() != null && EnableStatusEnum.fromValue(menu.getStatus().getValue()) == null) {
            log.warn("无效的菜单状态: {}", menu.getStatus());
            return false;
        }
        
        try {
            int result = menuMapper.updateById(menu);
            log.info("修改菜单完成, id: {}, menuName: {}, result: {}", 
                    menu.getId(), menu.getMenuName(), result);
            return result > 0;
        } catch (Exception e) {
            log.error("修改菜单失败, id: {}, menuName: {}", menu.getId(), menu.getMenuName(), e);
            throw e;
        }
    }

    /**
     * 通过ID删除菜单
     * 
     * @param id 菜单ID
     * @return 删除成功返回true，失败返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMenu(String id) {
        log.info("开始删除菜单, id: {}", id);
        
        // 校验参数
        if (!StringUtils.hasText(id)) {
            log.warn("菜单ID不能为空");
            return false;
        }
        
        // 检查菜单是否存在
        Menu menu = getMenuById(id);
        if (menu == null) {
            log.warn("菜单不存在, id: {}", id);
            return false;
        }

        // 检查是否为内置菜单
        if (menu.getIsBuiltin() == BuiltinEnum.BUILTIN) {
            log.warn("内置菜单不允许删除, id: {}, menuName: {}", id, menu.getMenuName());
            return false;
        }

        // 检查菜单是否被用户类型分配
        if (isMenuAssigned(id)) {
            log.warn("菜单已被分配给用户类型，无法删除, id: {}, menuName: {}", id, menu.getMenuName());
            return false;
        }
        
        // 获取所有子菜单ID（包括子子菜单）
        List<String> allChildMenuIds = getAllChildMenuIds(id);
        
        // 检查子菜单是否被分配
        for (String childMenuId : allChildMenuIds) {
            if (isMenuAssigned(childMenuId)) {
                Menu childMenu = getMenuById(childMenuId);
                log.warn("子菜单已被分配给用户类型，无法删除, childMenuId: {}, childMenuName: {}", 
                        childMenuId, childMenu != null ? childMenu.getMenuName() : "未知");
                return false;
            }
        }
        
        try {
            // 删除所有子菜单
            if (!allChildMenuIds.isEmpty()) {
                int childDeleteCount = menuMapper.deleteByIds(allChildMenuIds);
                log.info("删除子菜单完成, 删除数量: {}", childDeleteCount);
            }
            
            // 删除当前菜单
            int result = menuMapper.deleteById(id);
            
            log.info("删除菜单完成, id: {}, menuName: {}, 删除子菜单数: {}, result: {}", 
                    id, menu.getMenuName(), allChildMenuIds.size(), result);
            return result > 0;
        } catch (Exception e) {
            log.error("删除菜单失败, id: {}, menuName: {}", id, menu.getMenuName(), e);
            throw e;
        }
    }

    /**
     * 新增菜单
     * 
     * @param menu 菜单信息
     * @return 新增成功返回true，失败返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createMenu(Menu menu) {
        log.info("开始新增菜单, menuName: {}, menuCode: {}", menu.getMenuName(), menu.getMenuCode());
        
        // 校验参数
        if (!StringUtils.hasText(menu.getMenuName())) {
            log.warn("菜单名称不能为空");
            return false;
        }
        
        if (!StringUtils.hasText(menu.getMenuCode())) {
            log.warn("菜单编码不能为空");
            return false;
        }
        
        if (menu.getMenuType() == null) {
            log.warn("菜单类型不能为空");
            return false;
        }
        
        // 检查菜单编码是否已存在
        if (existsByMenuCode(menu.getMenuCode(), null)) {
            log.warn("菜单编码已存在: {}", menu.getMenuCode());
            return false;
        }
        
        // 验证父菜单是否有效（如果有父菜单）
        if (StringUtils.hasText(menu.getParentId())) {
            if (!isValidParent(menu.getParentId(), null)) {
                log.warn("无效的父菜单ID: {}", menu.getParentId());
                return false;
            }
        }
        
        // 验证菜单类型
        if (MenuTypeEnum.fromValue(menu.getMenuType().getValue()) == null) {
            log.warn("无效的菜单类型: {}", menu.getMenuType());
            return false;
        }
        
        // 设置默认值
        if (menu.getStatus() == null) {
            menu.setStatus(EnableStatusEnum.ENABLED);
        }
        
        if (menu.getSortOrder() == null) {
            menu.setSortOrder(0);
        }
        
        try {
            int result = menuMapper.insert(menu);
            log.info("新增菜单完成, menuName: {}, menuCode: {}, result: {}", 
                    menu.getMenuName(), menu.getMenuCode(), result);
            return result > 0;
        } catch (Exception e) {
            log.error("新增菜单失败, menuName: {}, menuCode: {}", menu.getMenuName(), menu.getMenuCode(), e);
            throw e;
        }
    }

    /**
     * 获取指定父菜单下的子菜单列表
     * 
     * @param parentId 父菜单ID，null表示获取根菜单
     * @return 子菜单列表
     */
    @Override
    public List<Menu> getChildMenus(String parentId) {
        LambdaQueryWrapper<Menu> queryWrapper = new LambdaQueryWrapper<>();
        
        if (parentId == null) {
            queryWrapper.isNull(Menu::getParentId);
        } else {
            queryWrapper.eq(Menu::getParentId, parentId);
        }
        
        queryWrapper.orderByAsc(Menu::getSortOrder, Menu::getCreateTime);
        
        return menuMapper.selectList(queryWrapper);
    }

    /**
     * 检查菜单编码是否已存在
     * 
     * @param menuCode 菜单编码
     * @param excludeId 要排除的菜单ID（用于修改时排除自己），可为空
     * @return 存在返回true，不存在返回false
     */
    @Override
    public boolean existsByMenuCode(String menuCode, String excludeId) {
        if (!StringUtils.hasText(menuCode)) {
            return false;
        }
        
        LambdaQueryWrapper<Menu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Menu::getMenuCode, menuCode);
        
        if (StringUtils.hasText(excludeId)) {
            queryWrapper.ne(Menu::getId, excludeId);
        }
        
        Long count = menuMapper.selectCount(queryWrapper);
        return count != null && count > 0;
    }

    /**
     * 检查菜单是否被用户类型分配
     * 
     * @param menuId 菜单ID
     * @return 被分配返回true，未被分配返回false
     */
    @Override
    public boolean isMenuAssigned(String menuId) {
        // 使用UserTypeMenuService检查菜单是否被分配
        return userTypeMenuService.isMenuAssignedToAnyUserType(menuId);
    }

    /**
     * 递归获取菜单的所有子菜单ID（包括子子菜单）
     * 
     * @param parentId 父菜单ID
     * @return 所有子菜单ID列表
     */
    @Override
    public List<String> getAllChildMenuIds(String parentId) {
        List<String> childMenuIds = new ArrayList<>();
        
        if (!StringUtils.hasText(parentId)) {
            return childMenuIds;
        }
        
        // 获取直接子菜单
        List<Menu> directChildren = getChildMenus(parentId);
        
        for (Menu child : directChildren) {
            // 添加子菜单ID
            childMenuIds.add(child.getId());
            
            // 递归获取子菜单的子菜单
            List<String> grandChildrenIds = getAllChildMenuIds(child.getId());
            childMenuIds.addAll(grandChildrenIds);
        }
        
        return childMenuIds;
    }

    /**
     * 检查父菜单是否有效
     * 
     * @param parentId 父菜单ID
     * @param currentMenuId 当前菜单ID（用于避免循环引用）
     * @return 有效返回true，无效返回false
     */
    @Override
    public boolean isValidParent(String parentId, String currentMenuId) {
        if (!StringUtils.hasText(parentId)) {
            return true; // 没有父菜单时认为有效
        }
        
        // 不能将自己设置为父菜单
        if (Objects.equals(parentId, currentMenuId)) {
            log.warn("不能将自己设置为父菜单, menuId: {}", currentMenuId);
            return false;
        }
        
        // 父菜单必须存在
        Menu parentMenu = getMenuById(parentId);
        if (parentMenu == null) {
            log.warn("父菜单不存在, parentId: {}", parentId);
            return false;
        }
        
        // 父菜单必须是目录或菜单类型（不能是按钮）
        if (parentMenu.getMenuType() == MenuTypeEnum.BUTTON) {
            log.warn("父菜单不能是按钮类型, parentId: {}, menuType: {}", parentId, parentMenu.getMenuType());
            return false;
        }
        
        // 检查是否会造成循环引用（不能将子菜单设置为父菜单）
        if (StringUtils.hasText(currentMenuId)) {
            List<String> allChildIds = getAllChildMenuIds(currentMenuId);
            if (allChildIds.contains(parentId)) {
                log.warn("不能将子菜单设置为父菜单, currentMenuId: {}, parentId: {}", currentMenuId, parentId);
                return false;
            }
        }
        
        return true;
    }

    /**
     * 获取菜单的完整路径（从根菜单到当前菜单的路径）
     * 
     * @param menuId 菜单ID
     * @return 菜单路径列表，按层级排序
     */
    @Override
    public List<Menu> getMenuPath(String menuId) {
        List<Menu> path = new ArrayList<>();
        
        if (!StringUtils.hasText(menuId)) {
            return path;
        }
        
        Menu currentMenu = getMenuById(menuId);
        while (currentMenu != null) {
            path.add(0, currentMenu); // 插入到列表开头，保持层级顺序
            
            if (StringUtils.hasText(currentMenu.getParentId())) {
                currentMenu = getMenuById(currentMenu.getParentId());
            } else {
                break;
            }
        }
        
        return path;
    }

    /**
     * 根据用户类型获取可访问的菜单树
     * 
     * @param userTypeId 用户类型ID
     * @return 用户可访问的菜单树
     */
    @Override
    public List<Menu> getMenuTreeByUserType(String userTypeId) {
        log.info("开始获取用户类型菜单树, userTypeId: {}", userTypeId);
        
        if (!StringUtils.hasText(userTypeId)) {
            return new ArrayList<>();
        }
        
        // 通过UserTypeMenuService获取菜单ID列表
        List<String> menuIds = userTypeMenuService.getMenuIdsByUserType(userTypeId);
        
        if (menuIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 查询菜单信息（只查询启用的菜单）
        LambdaQueryWrapper<Menu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Menu::getId, menuIds)
                   .eq(Menu::getStatus, EnableStatusEnum.ENABLED)
                   .orderByAsc(Menu::getSortOrder, Menu::getCreateTime);
        
        List<Menu> userMenus = menuMapper.selectList(queryWrapper);
        
        // 构建树形结构
        List<Menu> menuTree = buildMenuTree(userMenus, null);
        
        log.info("获取用户类型菜单树完成, userTypeId: {}, 菜单数: {}, 根菜单数: {}", 
                userTypeId, userMenus.size(), menuTree.size());
        return menuTree;
    }

    /**
     * 批量修改菜单状态
     * 
     * @param menuIds 菜单ID列表
     * @param status 要设置的状态
     * @return 修改成功的数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateMenuStatus(List<String> menuIds, EnableStatusEnum status) {
        log.info("开始批量修改菜单状态, menuIds: {}, status: {}", menuIds, status);

        if (menuIds == null || menuIds.isEmpty() || status == null) {
            return 0;
        }

        // 过滤掉内置菜单，内置菜单不允许修改状态
        LambdaUpdateWrapper<Menu> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(Menu::getId, menuIds)
                    .ne(Menu::getIsBuiltin, BuiltinEnum.BUILTIN)  // 排除内置菜单
                    .set(Menu::getStatus, status);

        int result = menuMapper.update(null, updateWrapper);

        log.info("批量修改菜单状态完成, 修改数量: {} (已自动排除内置菜单)", result);
        return result;
    }

    /**
     * 获取所有启用的菜单列表（扁平结构）
     * 
     * @return 启用的菜单列表
     */
    @Override
    public List<Menu> getAllEnabledMenus() {
        LambdaQueryWrapper<Menu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Menu::getStatus, EnableStatusEnum.ENABLED)
                   .orderByAsc(Menu::getSortOrder, Menu::getCreateTime);
        
        return menuMapper.selectList(queryWrapper);
    }

    /**
     * 根据权限标识获取菜单
     *
     * @param permission 权限标识
     * @return 菜单信息，如果不存在则返回null
     */
    @Override
    public Menu getMenuByPermission(String permission) {
        if (!StringUtils.hasText(permission)) {
            return null;
        }

        LambdaQueryWrapper<Menu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Menu::getPermission, permission);

        return menuMapper.selectOne(queryWrapper);
    }

    /**
     * 根据用户类型ID获取菜单列表（扁平结构）
     *
     * @param userTypeId 用户类型ID
     * @return 菜单列表
     */
    @Override
    public List<Menu> getMenusByUserTypeId(String userTypeId) {
        if (!StringUtils.hasText(userTypeId)) {
            return new ArrayList<>();
        }

        // 获取用户类型关联的菜单ID列表
        List<String> menuIds = userTypeMenuService.getMenuIdsByUserType(userTypeId);
        if (menuIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 根据菜单ID列表查询菜单信息
        LambdaQueryWrapper<Menu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Menu::getId, menuIds)
                   .eq(Menu::getStatus, EnableStatusEnum.ENABLED)
                   .orderByAsc(Menu::getSortOrder, Menu::getCreateTime);

        return menuMapper.selectList(queryWrapper);
    }

    /**
     * 构建菜单树形结构
     * 
     * @param allMenus 所有菜单列表
     * @param parentId 父菜单ID，null表示根节点
     * @return 树形结构的菜单列表
     */
    private List<Menu> buildMenuTree(List<Menu> allMenus, String parentId) {
        List<Menu> children = new ArrayList<>();
        
        for (Menu menu : allMenus) {
            // 判断是否为当前父节点的子节点
            boolean isChild = (parentId == null && menu.getParentId() == null) ||
                             (parentId != null && Objects.equals(menu.getParentId(), parentId));
            
            if (isChild) {
                // 递归构建子菜单
                List<Menu> subChildren = buildMenuTree(allMenus, menu.getId());
                // 设置子菜单列表到children字段
                menu.setChildren(subChildren);
                
                children.add(menu);
            }
        }

        return children;
    }

    /**
     * 清理菜单树的children字段
     * 
     * <p>在某些情况下需要清理children字段，比如数据库保存时</p>
     * 
     * @param menus 菜单列表
     */
    private void clearMenuChildren(List<Menu> menus) {
        if (menus == null || menus.isEmpty()) {
            return;
        }
        
        for (Menu menu : menus) {
            if (menu.getChildren() != null) {
                clearMenuChildren(menu.getChildren());
                menu.setChildren(null);
            }
        }
    }

    /**
     * 深度复制菜单列表（包含children）
     * 
     * @param sourceMenus 源菜单列表
     * @return 复制后的菜单列表
     */
    private List<Menu> deepCopyMenus(List<Menu> sourceMenus) {
        if (sourceMenus == null || sourceMenus.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<Menu> copiedMenus = new ArrayList<>();
        for (Menu sourceMenu : sourceMenus) {
            Menu copiedMenu = new Menu();
            // 复制基本属性
            copyMenuProperties(copiedMenu, sourceMenu);
            
            // 递归复制子菜单
            if (sourceMenu.getChildren() != null && !sourceMenu.getChildren().isEmpty()) {
                copiedMenu.setChildren(deepCopyMenus(sourceMenu.getChildren()));
            }
            
            copiedMenus.add(copiedMenu);
        }
        
        return copiedMenus;
    }

    /**
     * 复制菜单属性
     *
     * <p>使用Spring的BeanUtils进行属性复制，避免手动设置每个属性</p>
     * <p>注意：BeanUtils.copyProperties会忽略children字段，因为它是List类型且可能为null</p>
     *
     * @param target 目标菜单
     * @param source 源菜单
     */
    private void copyMenuProperties(Menu target, Menu source) {
        // 使用Spring BeanUtils进行属性复制，忽略children字段避免浅拷贝问题
        BeanUtils.copyProperties(source, target, "children");
    }

    /**
     * 获取菜单树的所有叶子节点
     * 
     * @param menuTree 菜单树
     * @return 叶子节点列表
     */
    private List<Menu> getLeafMenus(List<Menu> menuTree) {
        List<Menu> leafMenus = new ArrayList<>();
        
        if (menuTree == null || menuTree.isEmpty()) {
            return leafMenus;
        }
        
        for (Menu menu : menuTree) {
            if (menu.getChildren() == null || menu.getChildren().isEmpty()) {
                // 这是叶子节点
                leafMenus.add(menu);
            } else {
                // 递归处理子菜单
                leafMenus.addAll(getLeafMenus(menu.getChildren()));
            }
        }
        
        return leafMenus;
    }

    /**
     * 统计菜单树的总节点数
     *
     * @param menuTree 菜单树
     * @return 总节点数
     */
    private int countMenuNodes(List<Menu> menuTree) {
        if (menuTree == null || menuTree.isEmpty()) {
            return 0;
        }

        int count = menuTree.size();
        for (Menu menu : menuTree) {
            if (menu.getChildren() != null) {
                count += countMenuNodes(menu.getChildren());
            }
        }

        return count;
    }

    /**
     * 统计所有菜单数量
     *
     * @return 菜单总数量
     */
    @Override
    public long countAllMenus() {
        return menuMapper.selectCount(null);
    }

    /**
     * 在菜单树中查找指定ID的菜单
     * 
     * @param menuTree 菜单树
     * @param menuId 要查找的菜单ID
     * @return 找到的菜单，如果不存在则返回null
     */
    private Menu findMenuInTree(List<Menu> menuTree, String menuId) {
        if (menuTree == null || menuTree.isEmpty() || !StringUtils.hasText(menuId)) {
            return null;
        }
        
        for (Menu menu : menuTree) {
            if (Objects.equals(menu.getId(), menuId)) {
                return menu;
            }
            
            if (menu.getChildren() != null) {
                Menu found = findMenuInTree(menu.getChildren(), menuId);
                if (found != null) {
                    return found;
                }
            }
        }
        
        return null;
    }

    /**
     * 检查菜单是否为内置菜单
     *
     * @param menuId 菜单ID
     * @return 内置菜单返回true，非内置菜单返回false
     */
    @Override
    public boolean isBuiltinMenu(String menuId) {
        if (!StringUtils.hasText(menuId)) {
            return false;
        }

        Menu menu = getMenuById(menuId);
        return menu != null && menu.getIsBuiltin() == BuiltinEnum.BUILTIN;
    }

    // ==================== 性能优化方法 ====================

    /**
     * 获取所有启用菜单的权限标识列表
     *
     * <p>只查询权限相关字段，用于权限验证场景</p>
     * <p>相比getAllEnabledMenus()方法，减少了不必要的字段查询</p>
     *
     * @return 权限标识列表（去重后）
     */
    @Override
    public List<String> getAllEnabledMenuPermissions() {
        log.debug("开始获取所有启用菜单的权限标识列表");

        LambdaQueryWrapper<Menu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Menu::getStatus, EnableStatusEnum.ENABLED)
                   .isNotNull(Menu::getPermission)
                   .ne(Menu::getPermission, "")
                   .select(Menu::getPermission);

        List<Menu> menus = menuMapper.selectList(queryWrapper);

        List<String> permissions = menus.stream()
                .map(Menu::getPermission)
                .filter(StringUtils::hasText)
                .distinct()
                .collect(Collectors.toList());

        log.debug("获取所有启用菜单权限标识完成，数量: {}", permissions.size());
        return permissions;
    }

    /**
     * 根据用户类型ID获取菜单权限标识列表
     *
     * <p>只查询权限相关字段，用于权限验证场景</p>
     * <p>相比getMenusByUserTypeId()方法，减少了不必要的字段查询</p>
     *
     * @param userTypeId 用户类型ID
     * @return 权限标识列表（去重后）
     */
    @Override
    public List<String> getMenuPermissionsByUserTypeId(String userTypeId) {
        log.debug("开始获取用户类型菜单权限标识列表，userTypeId: {}", userTypeId);

        if (!StringUtils.hasText(userTypeId)) {
            return new ArrayList<>();
        }

        // 获取用户类型关联的菜单ID列表
        List<String> menuIds = userTypeMenuService.getMenuIdsByUserType(userTypeId);
        if (menuIds.isEmpty()) {
            log.debug("用户类型没有关联任何菜单，userTypeId: {}", userTypeId);
            return new ArrayList<>();
        }

        // 只查询权限字段
        LambdaQueryWrapper<Menu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Menu::getId, menuIds)
                   .eq(Menu::getStatus, EnableStatusEnum.ENABLED)
                   .isNotNull(Menu::getPermission)
                   .ne(Menu::getPermission, "")
                   .select(Menu::getPermission);

        List<Menu> menus = menuMapper.selectList(queryWrapper);

        List<String> permissions = menus.stream()
                .map(Menu::getPermission)
                .filter(StringUtils::hasText)
                .distinct()
                .collect(Collectors.toList());

        log.debug("获取用户类型菜单权限标识完成，userTypeId: {}, 权限数量: {}", userTypeId, permissions.size());
        return permissions;
    }

    /**
     * 批量获取多个用户类型的菜单权限标识
     *
     * <p>一次查询获取多个用户类型的权限，减少数据库交互次数</p>
     *
     * @param userTypeIds 用户类型ID列表
     * @return 权限标识列表（去重后）
     */
    @Override
    public List<String> getMenuPermissionsByUserTypeIds(List<String> userTypeIds) {
        log.debug("开始批量获取用户类型菜单权限标识列表，userTypeIds: {}", userTypeIds);

        if (CollectionUtils.isEmpty(userTypeIds)) {
            return new ArrayList<>();
        }

        // 批量获取所有用户类型关联的菜单ID
        Set<String> allMenuIds = new HashSet<>();
        for (String userTypeId : userTypeIds) {
            if (StringUtils.hasText(userTypeId)) {
                List<String> menuIds = userTypeMenuService.getMenuIdsByUserType(userTypeId);
                allMenuIds.addAll(menuIds);
            }
        }

        if (allMenuIds.isEmpty()) {
            log.debug("所有用户类型都没有关联任何菜单，userTypeIds: {}", userTypeIds);
            return new ArrayList<>();
        }

        // 只查询权限字段
        LambdaQueryWrapper<Menu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Menu::getId, allMenuIds)
                   .eq(Menu::getStatus, EnableStatusEnum.ENABLED)
                   .isNotNull(Menu::getPermission)
                   .ne(Menu::getPermission, "")
                   .select(Menu::getPermission);

        List<Menu> menus = menuMapper.selectList(queryWrapper);

        List<String> permissions = menus.stream()
                .map(Menu::getPermission)
                .filter(StringUtils::hasText)
                .distinct()
                .collect(Collectors.toList());

        log.debug("批量获取用户类型菜单权限标识完成，userTypeIds: {}, 权限数量: {}", userTypeIds, permissions.size());
        return permissions;
    }
}