package com.akey.core.dao.entity;

import com.akey.common.entity.BaseEntity;
import com.akey.common.enums.OperationStatus;
import com.akey.common.enums.OperationType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 操作日志实体类
 *
 * <p>对应数据库表：operation_log</p>
 * <p>记录系统中所有用户的操作行为，支持审计和追踪</p>
 * <p>继承BaseEntity，获得公共字段：id、createTime、updateTime、createBy、updateBy、deleted、version等</p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "operation_log", autoResultMap = true)
public class OperationLog extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 操作用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 操作用户名
     */
    @TableField("username")
    private String username;

    /**
     * 操作时间
     */
    @TableField("operation_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime operationTime;

    /**
     * 操作模块(用户自定义，如：用户管理、角色管理、系统管理等)
     */
    @TableField("operation_module")
    private String operationModule;

    /**
     * 操作类型
     */
    @TableField("operation_type")
    private String operationType;

    /**
     * 操作描述
     */
    @TableField("operation_desc")
    private String operationDesc;

    /**
     * 请求方法(GET,POST,PUT,DELETE等)
     */
    @TableField("request_method")
    private String requestMethod;

    /**
     * 请求URL
     */
    @TableField("request_url")
    private String requestUrl;

    /**
     * 请求参数(JSON格式)
     */
    @TableField(value = "request_params", typeHandler = JacksonTypeHandler.class)
    private Object requestParams;

    /**
     * 响应结果(JSON格式)
     */
    @TableField(value = "response_result", typeHandler = JacksonTypeHandler.class)
    private Object responseResult;

    /**
     * 响应状态码
     */
    @TableField("response_status")
    private Integer responseStatus;

    /**
     * 响应消息
     */
    @TableField("response_message")
    private String responseMessage;

    /**
     * 客户端IP地址(支持IPv6)
     */
    @TableField("client_ip")
    private String clientIp;

    /**
     * 用户代理信息
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 操作耗时(毫秒)
     */
    @TableField("execution_time")
    private Long executionTime;

    /**
     * 地理位置
     *
     * <p>格式：国家 省份 城市</p>
     */
    @TableField("location")
    private String location;

    /**
     * 操作状态(0-失败,1-成功)
     */
    @TableField("operation_status")
    private Integer operationStatus;

    /**
     * 错误信息(操作失败时记录)
     */
    @TableField("error_message")
    private String errorMessage;

    // ==================== 便捷方法 ====================

    /**
     * 获取操作类型枚举
     *
     * @return 操作类型枚举
     */
    public OperationType getOperationTypeEnum() {
        return OperationType.fromCode(this.operationType);
    }

    /**
     * 设置操作类型枚举
     *
     * @param operationType 操作类型枚举
     */
    public void setOperationTypeEnum(OperationType operationType) {
        this.operationType = operationType != null ? operationType.getCode() : null;
    }

    /**
     * 获取操作状态枚举
     *
     * @return 操作状态枚举
     */
    public OperationStatus getOperationStatusEnum() {
        return OperationStatus.fromValue(this.operationStatus);
    }

    /**
     * 设置操作状态枚举
     *
     * @param operationStatus 操作状态枚举
     */
    public void setOperationStatusEnum(OperationStatus operationStatus) {
        this.operationStatus = operationStatus != null ? operationStatus.getValue() : null;
    }

    /**
     * 判断操作是否成功
     *
     * @return true-成功，false-失败
     */
    public boolean isSuccess() {
        return OperationStatus.isSuccess(this.operationStatus);
    }

    /**
     * 判断操作是否失败
     *
     * @return true-失败，false-成功
     */
    public boolean isFailure() {
        return OperationStatus.isFailure(this.operationStatus);
    }

    /**
     * 判断是否为写操作
     *
     * @return true-写操作，false-读操作
     */
    public boolean isWriteOperation() {
        return OperationType.isWriteOperation(this.operationType);
    }

    /**
     * 判断是否为读操作
     *
     * @return true-读操作，false-写操作
     */
    public boolean isReadOperation() {
        return OperationType.isReadOperation(this.operationType);
    }

    /**
     * 判断是否为认证操作
     *
     * @return true-认证操作，false-其他操作
     */
    public boolean isAuthOperation() {
        return OperationType.isAuthOperation(this.operationType);
    }
}
