package com.akey.core.dao.entity;

import com.akey.common.enums.BuiltinEnum;
import com.akey.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户类型实体类
 * 
 * <p>对应数据库表：sys_user_type</p>
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user_type")
public class UserType extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 类型名称
     * 如：超级管理员、普通管理员、普通用户等
     */
    @TableField("type_name")
    private String typeName;

    /**
     * 是否内置类型
     * 0: 是内置类型，不可删除
     * 1: 否，非内置类型，可删除
     * 
     * @see BuiltinEnum
     */
    @TableField("is_builtin")
    private BuiltinEnum isBuiltin;
} 