package com.akey.core.dao.service;

import com.akey.core.vo.MenuRouterTreeVO;

import java.util.List;

/**
 * 用户路由Service接口
 *
 * <p>提供用户路由相关的业务操作</p>
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface UserRouteService {

    /**
     * 根据用户ID获取用户菜单树 (getUserRouters)
     *
     * <p>只返回菜单和目录，排除按钮级权限</p>
     * <p>超级管理员可获取全部数据，其他用户按用户类型过滤</p>
     * <p>返回树形结构，符合前端组件要求</p>
     *
     * @param userId 用户ID
     * @return 用户可访问的菜单树
     */
    List<MenuRouterTreeVO> getUserRouters(String userId);

    /**
     * 根据用户ID获取用户权限字符串列表 (getUserPermissions)
     *
     * <p>返回权限字符串数组</p>
     * <p>超级管理员可获取全部数据，其他用户按用户类型过滤</p>
     *
     * @param userId 用户ID
     * @return 权限字符串列表
     */
    List<String> getUserPermissions(String userId);
}
