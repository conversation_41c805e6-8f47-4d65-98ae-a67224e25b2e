package com.akey.core.dao.entity;

import com.akey.common.entity.BaseEntity;
import com.akey.common.enums.EnableStatusEnum;
import com.akey.common.enums.IpWhitelistTypeEnum;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * IP白名单实体类
 * 
 * <p>对应数据库表：sys_ip_whitelist</p>
 * 
 * <p>功能说明：</p>
 * <ul>
 *   <li>支持系统白名单和接口白名单两种类型</li>
 *   <li>提供启用/禁用状态控制</li>
 *   <li>支持有效期设置，过期自动失效</li>
 *   <li>记录详细的管理信息和备注说明</li>
 * </ul>
 * 
 * <AUTHOR>
 * @since 2025-07-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_ip_whitelist")
public class IpWhitelist extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 白名单类型
     * 
     * <p>1-系统白名单：用于后台管理系统的访问控制</p>
     * <p>2-接口白名单：用于对外开放接口的访问控制</p>
     * 
     * @see IpWhitelistTypeEnum
     */
    @TableField("whitelist_type")
    private IpWhitelistTypeEnum whitelistType;

    /**
     * IP地址
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 关联用户ID
     *
     * <p>用于区分是哪个用户的白名单，为空表示系统级白名单</p>
     */
    @TableField("user_id")
    private String userId;

    /**
     * 启用状态
     *
     * <p>0-禁用：不生效，不进行IP验证</p>
     * <p>1-启用：生效，进行IP验证</p>
     *
     * @see EnableStatusEnum
     */
    @TableField("enable_status")
    private EnableStatusEnum enableStatus;

    /**
     * 备注说明
     *
     * <p>记录IP的用途等信息</p>
     */
    @TableField("remark")
    private String remark;

    /**
     * 判断白名单是否当前有效
     *
     * <p>检查启用状态</p>
     *
     * @return true:有效，false:无效
     */
    public boolean isCurrentlyValid() {
        return enableStatus == EnableStatusEnum.ENABLED;
    }

    /**
     * 判断是否为系统白名单
     * 
     * @return true:系统白名单，false:非系统白名单
     */
    public boolean isSystemWhitelist() {
        return whitelistType == IpWhitelistTypeEnum.SYSTEM;
    }

    /**
     * 判断是否为接口白名单
     * 
     * @return true:接口白名单，false:非接口白名单
     */
    public boolean isApiWhitelist() {
        return whitelistType == IpWhitelistTypeEnum.API;
    }

    /**
     * 判断是否为系统级白名单（无关联用户）
     *
     * @return true:系统级白名单，false:用户级白名单
     */
    public boolean isSystemLevel() {
        return userId == null || userId.trim().isEmpty();
    }

    /**
     * 判断是否为用户级白名单（有关联用户）
     *
     * @return true:用户级白名单，false:系统级白名单
     */
    public boolean isUserLevel() {
        return userId != null && !userId.trim().isEmpty();
    }
}
