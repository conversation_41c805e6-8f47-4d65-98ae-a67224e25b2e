package com.akey.core.dao.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.akey.common.constant.SystemConstant;
import com.akey.common.exception.AuthException;
import com.akey.common.exception.BusinessException;
import com.akey.common.util.PasswordUtil;
import com.akey.core.dao.service.UserService;
import com.akey.core.dao.service.UserTypeService;
import com.akey.common.enums.AccountLockStatusEnum;
import com.akey.core.dao.entity.User;
import com.akey.core.dao.entity.UserType;
import com.akey.core.dao.mapper.UserMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户Service实现类
 * 
 * <p>使用MyBatis-Plus的QueryWrapper进行单表操作</p>
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserMapper userMapper;
    private final UserTypeService userTypeService;

    /**
     * 创建用户
     * 
     * @param user 用户信息
     * @return 创建成功返回true，失败返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createUser(User user) {
        log.info("开始创建用户, username: {}", user.getUsername());
        
        // 校验参数
        if (!StringUtils.hasText(user.getUsername())) {
            log.warn("用户名不能为空");
            throw new BusinessException("用户名不能为空");
        }

        if (!StringUtils.hasText(user.getPassword())) {
            log.warn("用户密码不能为空");
            throw new BusinessException("用户密码不能为空");
        }

        if (!StringUtils.hasText(user.getUserTypeId())) {
            log.warn("用户类型ID不能为空");
            throw new BusinessException("用户类型ID不能为空");
        }

        // 检查用户名是否已存在
        if (existsByUsername(user.getUsername(), null)) {
            log.warn("用户名已存在: {}", user.getUsername());
            throw new BusinessException("用户名已存在");
        }

        // 验证用户类型是否存在
        UserType userType = userTypeService.getUserTypeById(user.getUserTypeId());
        if (userType == null) {
            log.warn("用户类型不存在: {}", user.getUserTypeId());
            throw new BusinessException("用户类型不存在");
        }
        
        // 加密密码
        String encryptedPassword = PasswordUtil.encryptPassword(user.getPassword());
        user.setPassword(encryptedPassword);

        // 设置默认值
        LocalDateTime now = LocalDateTime.now();
        if (user.getAccountLocked() == null) {
            user.setAccountLocked(AccountLockStatusEnum.UNLOCKED);
        }
        user.setPasswordUpdateTime(now);

        try {
            int result = userMapper.insert(user);
            log.info("创建用户完成, username: {}, userType: {}, result: {}",
                    user.getUsername(), userType.getTypeName(), result);

            if (result <= 0) {
                log.warn("创建用户失败，数据库插入失败, username: {}", user.getUsername());
                throw new BusinessException("创建用户失败，请稍后重试");
            }

            return true;
        } catch (BusinessException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("创建用户失败, username: {}", user.getUsername(), e);
            throw new BusinessException("创建用户失败，系统异常：" + e.getMessage());
        }
    }

    /**
     * 修改用户
     * 
     * @param user 用户信息（必须包含id）
     * @return 修改成功返回true，失败返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUser(User user) {
        log.info("开始修改用户, id: {}, username: {}, userTypeId: {}, accountLocked: {}",
                user.getId(), user.getUsername(), user.getUserTypeId(), user.getAccountLocked());

        // 校验参数
        if (!StringUtils.hasText(user.getId())) {
            log.warn("用户ID不能为空");
            throw new BusinessException("用户ID不能为空");
        }

        // 检查用户是否存在
        User existingUser = getUserById(user.getId());
        if (existingUser == null) {
            log.warn("用户不存在, id: {}", user.getId());
            throw new BusinessException("用户不存在");
        }

        // 检查用户名是否已被其他用户使用（如果有修改用户名）
        if (StringUtils.hasText(user.getUsername()) &&
            !user.getUsername().equals(existingUser.getUsername())) {
            if (existsByUsername(user.getUsername(), user.getId())) {
                log.warn("用户名已存在: {}", user.getUsername());
                throw new BusinessException("用户名已存在");
            }
        }

        // 验证用户类型是否存在（如果有修改）
        if (StringUtils.hasText(user.getUserTypeId())) {
            UserType userType = userTypeService.getUserTypeById(user.getUserTypeId());
            if (userType == null) {
                log.warn("用户类型不存在: {}", user.getUserTypeId());
                throw new BusinessException("用户类型不存在");
            }
        }

        try {
            // 使用LambdaUpdateWrapper进行选择性更新
            LambdaUpdateWrapper<User> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(User::getId, user.getId());

            // 只更新非空字段
            if (StringUtils.hasText(user.getUsername())) {
                updateWrapper.set(User::getUsername, user.getUsername());
            }

            if (StringUtils.hasText(user.getUserTypeId())) {
                updateWrapper.set(User::getUserTypeId, user.getUserTypeId());
            }

            if (user.getAccountLocked() != null) {
                updateWrapper.set(User::getAccountLocked, user.getAccountLocked());
            }

            int result = userMapper.update(null, updateWrapper);
            log.info("修改用户完成, id: {}, username: {}, userTypeId: {}, accountLocked: {}, result: {}",
                    user.getId(), user.getUsername(), user.getUserTypeId(), user.getAccountLocked(), result);
            return result > 0;
        } catch (Exception e) {
            log.error("修改用户失败, id: {}, username: {}, userTypeId: {}, accountLocked: {}",
                    user.getId(), user.getUsername(), user.getUserTypeId(), user.getAccountLocked(), e);
            throw e;
        }
    }

    /**
     * 根据用户名获取用户信息
     * 
     * @param username 用户名
     * @return 用户信息，如果不存在则返回null
     */
    @Override
    public User getUserByUsername(String username) {
        if (!StringUtils.hasText(username)) {
            return null;
        }
        
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getUsername, username);
        
        return userMapper.selectOne(queryWrapper);
    }

    /**
     * 通过用户ID设置用户锁定状态
     * 
     * @param userId 用户ID
     * @param accountLocked 锁定状态（0:未锁定,1:锁定）
     * @return 设置成功返回true，失败返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setUserLockStatus(String userId, Integer accountLocked) {
        log.info("开始设置用户锁定状态, userId: {}, accountLocked: {}", userId, accountLocked);
        
        // 校验参数
        if (!StringUtils.hasText(userId)) {
            log.warn("用户ID不能为空");
            throw new BusinessException("用户ID不能为空");
        }

        if (accountLocked == null) {
            log.warn("锁定状态不能为空");
            throw new BusinessException("锁定状态不能为空");
        }

        // 验证锁定状态值
        AccountLockStatusEnum lockStatus = AccountLockStatusEnum.fromValue(accountLocked);
        if (lockStatus == null) {
            log.warn("无效的锁定状态值: {}", accountLocked);
            throw new BusinessException("无效的锁定状态值");
        }
        
        // 检查用户是否存在
        User user = getUserById(userId);
        if (user == null) {
            log.warn("用户不存在, userId: {}", userId);
            return false;
        }
        
        try {
            LambdaUpdateWrapper<User> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(User::getId, userId)
                        .set(User::getAccountLocked, lockStatus);
            
            int result = userMapper.update(null, updateWrapper);
            log.info("设置用户锁定状态完成, userId: {}, username: {}, accountLocked: {}, result: {}", 
                    userId, user.getUsername(), lockStatus.getDescription(), result);
            return result > 0;
        } catch (Exception e) {
            log.error("设置用户锁定状态失败, userId: {}, accountLocked: {}", userId, accountLocked, e);
            throw e;
        }
    }

    /**
     * 修改登录时间+ 登录IP
     * 
     * @param userId 用户ID
     * @param loginIp 登录IP
     * @return 修改成功返回true，失败返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLoginInfo(String userId, String loginIp) {
        log.info("开始更新登录信息, userId: {}, loginIp: {}", userId, loginIp);
        
        // 校验参数
        if (!StringUtils.hasText(userId)) {
            log.warn("用户ID不能为空");
            return false;
        }
        
        if (!StringUtils.hasText(loginIp)) {
            log.warn("登录IP不能为空");
            return false;
        }
        
        // 检查用户是否存在
        User user = getUserById(userId);
        if (user == null) {
            log.warn("用户不存在, userId: {}", userId);
            return false;
        }
        
        try {
            LocalDateTime now = LocalDateTime.now();
            LambdaUpdateWrapper<User> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(User::getId, userId)
                        .set(User::getLastLoginTime, now)
                        .set(User::getLastLoginIp, loginIp);
            
            int result = userMapper.update(null, updateWrapper);
            log.info("更新登录信息完成, userId: {}, username: {}, loginTime: {}, loginIp: {}, result: {}", 
                    userId, user.getUsername(), now, loginIp, result);
            return result > 0;
        } catch (Exception e) {
            log.error("更新登录信息失败, userId: {}, loginIp: {}", userId, loginIp, e);
            throw e;
        }
    }

    /**
     * 通过用户ID修改密码
     * 
     * @param userId 用户ID
     * @param newPassword 新密码（已加密）
     * @return 修改成功返回true，失败返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePassword(String userId, String newPassword) {
        log.info("开始修改用户密码, userId: {}", userId);
        
        // 校验参数
        if (!StringUtils.hasText(userId)) {
            log.warn("用户ID不能为空");
            return false;
        }
        
        if (!StringUtils.hasText(newPassword)) {
            log.warn("新密码不能为空");
            return false;
        }
        
        // 检查用户是否存在
        User user = getUserById(userId);
        if (user == null) {
            log.warn("用户不存在, userId: {}", userId);
            return false;
        }

        // 加密新密码
        String encryptedPassword = PasswordUtil.encryptPassword(newPassword);

        try {
            LocalDateTime now = LocalDateTime.now();
            LambdaUpdateWrapper<User> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(User::getId, userId)
                        .set(User::getPassword, encryptedPassword)
                        .set(User::getPasswordUpdateTime, now);

            int result = userMapper.update(null, updateWrapper);
            log.info("修改用户密码完成, userId: {}, username: {}, passwordUpdateTime: {}, result: {}",
                    userId, user.getUsername(), now, result);
            return result > 0;
        } catch (Exception e) {
            log.error("修改用户密码失败, userId: {}", userId, e);
            throw e;
        }
    }

    /**
     * 通过用户ID重置密码
     * 
     * @param userId 用户ID
     * @param resetPassword 重置密码（已加密）
     * @return 重置成功返回true，失败返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetPassword(String userId, String resetPassword) {
        log.info("开始重置用户密码, userId: {}", userId);
        
        // 校验参数
        if (!StringUtils.hasText(userId)) {
            log.warn("用户ID不能为空");
            return false;
        }
        
        if (!StringUtils.hasText(resetPassword)) {
            log.warn("重置密码不能为空");
            return false;
        }
        
        // 检查用户是否存在
        User user = getUserById(userId);
        if (user == null) {
            log.warn("用户不存在, userId: {}", userId);
            return false;
        }

        // 加密新密码
        String encryptedPassword = PasswordUtil.encryptPassword(resetPassword);

        try {
            LocalDateTime now = LocalDateTime.now();
            LambdaUpdateWrapper<User> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(User::getId, userId)
                        .set(User::getPassword, encryptedPassword)
                        .set(User::getPasswordUpdateTime, now);

            int result = userMapper.update(null, updateWrapper);
            log.info("重置用户密码完成, userId: {}, username: {}, passwordUpdateTime: {}, result: {}",
                    userId, user.getUsername(), now, result);
            return result > 0;
        } catch (Exception e) {
            log.error("重置用户密码失败, userId: {}", userId, e);
            throw e;
        }
    }

    /**
     * 分页获取用户列表(筛选条件：用户名，用户类型，账户状态)
     * 
     * @param page 分页参数
     * @param username 用户名（模糊查询，可为空）
     * @param userTypeId 用户类型ID（可为空）
     * @param accountLocked 账户状态（可为空，查询全部）
     * @return 分页结果
     */
    @Override
    public IPage<User> getUserPageList(Page<User> page, String username, String userTypeId, Integer accountLocked) {
        log.debug("开始分页查询用户列表, page: {}, size: {}, username: {}, userTypeId: {}, accountLocked: {}", 
                page.getCurrent(), page.getSize(), username, userTypeId, accountLocked);
        
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        
        // 用户名模糊查询
        queryWrapper.like(StringUtils.hasText(username), User::getUsername, username);
        
        // 用户类型筛选
        queryWrapper.eq(StringUtils.hasText(userTypeId), User::getUserTypeId, userTypeId);

        // 如果查询的用户不是超级管理员，则不显示超级管理员账号
        String userId = StpUtil.getLoginIdAsString();
        User user = getUserById(userId);
        if (user != null && !userTypeService.isSuperAdminType(user.getUserTypeId())) {
            queryWrapper.ne(User::getUserTypeId, SystemConstant.ADMIN_USER_TYPE_ID);
        }

        // 账户状态筛选
        if (accountLocked != null) {
            AccountLockStatusEnum lockStatus = AccountLockStatusEnum.fromValue(accountLocked);
            if (lockStatus != null) {
                queryWrapper.eq(User::getAccountLocked, lockStatus);
            }
        }
        
        // 排序：按创建时间倒序
        queryWrapper.orderByDesc(User::getCreateTime);
        
        IPage<User> result = userMapper.selectPage(page, queryWrapper);
        
        log.debug("分页查询用户列表完成, total: {}, pages: {}", 
                result.getTotal(), result.getPages());
        
        return result;
    }

    /**
     * 检查用户名是否已存在
     * 
     * @param username 用户名
     * @param excludeId 要排除的ID（用于修改时排除自己），可为空
     * @return 存在返回true，不存在返回false
     */
    @Override
    public boolean existsByUsername(String username, String excludeId) {
        if (!StringUtils.hasText(username)) {
            return false;
        }
        
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getUsername, username);
        
        // 排除指定ID
        queryWrapper.ne(StringUtils.hasText(excludeId), User::getId, excludeId);
        
        Long count = userMapper.selectCount(queryWrapper);
        return count != null && count > 0;
    }

    /**
     * 根据ID查询用户
     * 
     * @param id 用户ID
     * @return 用户，如果不存在则返回null
     */
    @Override
    public User getUserById(String id) {
        if (!StringUtils.hasText(id)) {
            return null;
        }
        
        return userMapper.selectById(id);
    }

    /**
     * 验证用户名和密码
     * 
     * @param username 用户名
     * @param password 密码
     * @return 用户，如果不存在则返回null
     */
    @Override
    public User verifyUser(String username, String password) {
        if (!StringUtils.hasText(username) || !StringUtils.hasText(password)) {
            return null;
        }

        // 获取用户信息
        User userByUsername = getUserByUsername(username);
        // 判断账号密码是否正确
        if (userByUsername == null || !PasswordUtil.verifyPassword(password, userByUsername.getPassword())) {
            log.warn("用户登录失败 - 用户名或密码错误, username: {}", username);
            throw AuthException.loginFailed();
        }
        // 判断账号状态
        if (userByUsername.getAccountLocked() == AccountLockStatusEnum.LOCKED) {
            log.warn("用户登录失败 - 账户已被锁定, username: {}", username);
            throw AuthException.accountLocked();
        }
        return userByUsername;
    }

    /**
     * 检查用户是否锁定
     * 
     * @param userId 用户ID
     * @return 锁定返回true，未锁定返回false
     */
    @Override
    public boolean isUserLocked(String userId) {
        User user = getUserById(userId);
        if (user == null) {
            return true; // 用户不存在，视为锁定
        }
        
        return user.getAccountLocked() == AccountLockStatusEnum.LOCKED;
    }


    /**
     * 判断用户是否设置谷歌验证
     * 
     * @param userId 用户ID
     * @return 设置返回true，未设置返回false
     */
    @Override
    public boolean isUserGoogleAuth(String userId) {
        User user = getUserById(userId);
        if (user == null) {
            return false;
        }
        return user.getGoogleAuthKey() != null && !"".equals(user.getGoogleAuthKey());
    }

    /**
     * 更新用户谷歌验证KEY
     *
     * @param userId 用户ID
     * @param googleAuthKey 谷歌验证KEY
     * @return 更新成功返回true，失败返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateGoogleAuthKey(String userId, String googleAuthKey) {
        log.info("开始更新用户谷歌验证KEY, userId: {}", userId);

        // 校验参数
        if (!StringUtils.hasText(userId)) {
            log.warn("用户ID不能为空");
            return false;
        }

        // 检查用户是否存在
        User user = getUserById(userId);
        if (user == null) {
            log.warn("用户不存在, userId: {}", userId);
            return false;
        }

        try {
            LambdaUpdateWrapper<User> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(User::getId, userId)
                        .set(User::getGoogleAuthKey, googleAuthKey);

            int result = userMapper.update(null, updateWrapper);
            log.info("更新用户谷歌验证KEY完成, userId: {}, username: {}, result: {}",
                    userId, user.getUsername(), result);
            return result > 0;
        } catch (Exception e) {
            log.error("更新用户谷歌验证KEY失败, userId: {}", userId, e);
            throw e;
        }
    }

    /**
     * 重置用户谷歌验证KEY
     *
     * @param userId 用户ID
     * @return 重置成功返回true，失败返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetGoogleAuthKey(String userId) {
        log.info("开始重置用户谷歌验证KEY, userId: {}", userId);

        // 校验参数
        if (!StringUtils.hasText(userId)) {
            log.warn("用户ID不能为空");
            return false;
        }

        // 检查用户是否存在
        User user = getUserById(userId);
        if (user == null) {
            log.warn("用户不存在, userId: {}", userId);
            return false;
        }

        try {
            LambdaUpdateWrapper<User> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(User::getId, userId)
                        .set(User::getGoogleAuthKey, null);

            int result = userMapper.update(null, updateWrapper);
            log.info("重置用户谷歌验证KEY完成, userId: {}, username: {}, result: {}",
                    userId, user.getUsername(), result);
            return result > 0;
        } catch (Exception e) {
            log.error("重置用户谷歌验证KEY失败, userId: {}", userId, e);
            throw e;
        }
    }

    /**
     * 删除用户
     *
     * @param userId 用户ID
     * @return 删除成功返回true，失败返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUser(String userId) {
        log.info("开始删除用户, userId: {}", userId);

        // 校验参数
        if (!StringUtils.hasText(userId)) {
            log.warn("用户ID不能为空");
            return false;
        }

        // 检查用户是否存在
        User user = getUserById(userId);
        if (user == null) {
            log.warn("用户不存在, userId: {}", userId);
            return false;
        }

        // 超级管理员不能删除
        if (userTypeService.isSuperAdminType(user.getUserTypeId())) {
            log.warn("超级管理员用户不能删除, userId: {}", userId);
            return false;
        }

        try {
            int result = userMapper.deleteById(userId);
            log.info("删除用户完成, userId: {}, username: {}, result: {}",
                    userId, user.getUsername(), result);
            return result > 0;
        } catch (Exception e) {
            log.error("删除用户失败, userId: {}", userId, e);
            throw e;
        }
    }

    /**
     * 批量删除用户
     *
     * @param userIds 用户ID列表
     * @return 删除成功的用户数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchDeleteUsers(List<String> userIds) {
        log.info("开始批量删除用户, userIds: {}", userIds);

        // 校验参数
        if (userIds == null || userIds.isEmpty()) {
            log.warn("用户ID列表不能为空");
            return 0;
        }

        int successCount = 0;
        for (String userId : userIds) {
            try {
                if (deleteUser(userId)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("批量删除用户失败, userId: {}, error: {}", userId, e.getMessage());
            }
        }

        log.info("批量删除用户完成, total: {}, success: {}", userIds.size(), successCount);
        return successCount;
    }

    /**
     * 批量设置用户锁定状态
     *
     * @param userIds 用户ID列表
     * @param accountLocked 锁定状态（0:未锁定,1:锁定）
     * @return 设置成功的用户数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchSetUserLockStatus(List<String> userIds, Integer accountLocked) {
        log.info("开始批量设置用户锁定状态, userIds: {}, accountLocked: {}", userIds, accountLocked);

        // 校验参数
        if (userIds == null || userIds.isEmpty()) {
            log.warn("用户ID列表不能为空");
            return 0;
        }

        if (accountLocked == null) {
            log.warn("锁定状态不能为空");
            return 0;
        }

        int successCount = 0;
        for (String userId : userIds) {
            try {
                if (setUserLockStatus(userId, accountLocked)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("批量设置用户锁定状态失败, userId: {}, error: {}", userId, e.getMessage());
            }
        }

        log.info("批量设置用户锁定状态完成, total: {}, success: {}", userIds.size(), successCount);
        return successCount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changePassword(String userId, String oldPassword, String newPassword) {
        log.info("开始修改用户密码, userId: {}", userId);

        // 校验参数
        if (!StringUtils.hasText(userId)) {
            log.warn("用户ID不能为空");
            throw new BusinessException("用户ID不能为空");
        }

        if (!StringUtils.hasText(oldPassword)) {
            log.warn("旧密码不能为空");
            throw new BusinessException("旧密码不能为空");
        }

        if (!StringUtils.hasText(newPassword)) {
            log.warn("新密码不能为空");
            throw new BusinessException("新密码不能为空");
        }


        // 检查用户是否存在
        User user = getUserById(userId);
        if (user == null) {
            log.warn("用户不存在, userId: {}", userId);
            throw new BusinessException("用户不存在");
        }

        // 验证旧密码是否正确
        if (!PasswordUtil.verifyPassword(oldPassword, user.getPassword())) {
            log.warn("旧密码验证失败, userId: {}", userId);
            throw new BusinessException("旧密码错误，请重新输入");
        }

        // 检查新密码是否与旧密码相同
        if (oldPassword.equals(newPassword)) {
            log.warn("新密码不能与旧密码相同, userId: {}", userId);
            throw new BusinessException("新密码不能与旧密码相同");
        }

        // 加密新密码
        String encryptedPassword = PasswordUtil.encryptPassword(newPassword);

        try {
            LocalDateTime now = LocalDateTime.now();
            LambdaUpdateWrapper<User> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(User::getId, userId)
                        .set(User::getPassword, encryptedPassword)
                        .set(User::getPasswordUpdateTime, now);

            int result = userMapper.update(null, updateWrapper);
            log.info("修改用户密码完成, userId: {}, username: {}, passwordUpdateTime: {}, result: {}",
                    userId, user.getUsername(), now, result);

            if (result <= 0) {
                log.warn("修改用户密码失败，数据库更新失败, userId: {}", userId);
                throw new BusinessException("密码修改失败，请稍后重试");
            }

            return true;
        } catch (BusinessException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("修改用户密码失败, userId: {}", userId, e);
            throw new BusinessException("密码修改失败，系统异常：" + e.getMessage());
        }
    }
}