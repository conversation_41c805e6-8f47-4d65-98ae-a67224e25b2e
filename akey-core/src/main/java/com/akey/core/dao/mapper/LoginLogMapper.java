package com.akey.core.dao.mapper;

import com.akey.core.dao.entity.LoginLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 登录日志Mapper接口
 *
 * <p>优化说明：</p>
 * <ul>
 *   <li>简单查询方法已移至Service层，使用MyBatis-Plus条件构造器实现</li>
 *   <li>保留复杂的聚合查询和统计方法</li>
 *   <li>减少手写SQL，提高代码可维护性</li>
 * </ul>
 *
 * <p>已移至Service层的方法：</p>
 * <ul>
 *   <li>countLoginsByUserAndTime - 使用LambdaQueryWrapper + count()</li>
 *   <li>countLoginsByIpAndTime - 使用LambdaQueryWrapper + count()</li>
 *   <li>getRecentLoginsByUser - 使用LambdaQueryWrapper + selectList()</li>
 *   <li>getSuspiciousLogins - 使用LambdaQueryWrapper + selectPage()</li>
 *   <li>getHighRiskLogins - 使用LambdaQueryWrapper + selectPage()</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Mapper
public interface LoginLogMapper extends BaseMapper<LoginLog> {

    /**
     * 统计登录状态分布
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 状态分布统计
     */
    @Select({
        "SELECT login_status, COUNT(*) as count",
        "FROM login_log",
        "WHERE login_time BETWEEN #{startTime} AND #{endTime}",
        "GROUP BY login_status"
    })
    List<Map<String, Object>> getLoginStatusStatistics(@Param("startTime") LocalDateTime startTime,
                                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 统计风险等级分布
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 风险等级分布统计
     */
    @Select({
        "SELECT risk_level, COUNT(*) as count",
        "FROM login_log",
        "WHERE login_time BETWEEN #{startTime} AND #{endTime}",
        "GROUP BY risk_level"
    })
    List<Map<String, Object>> getRiskLevelStatistics(@Param("startTime") LocalDateTime startTime,
                                                      @Param("endTime") LocalDateTime endTime);

    /**
     * 统计每小时登录次数
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 每小时登录统计
     */
    @Select({
        "SELECT hour_group as hour,",
        "COUNT(*) as total_count,",
        "SUM(CASE WHEN login_status = 1 THEN 1 ELSE 0 END) as success_count,",
        "SUM(CASE WHEN login_status = 0 THEN 1 ELSE 0 END) as failure_count",
        "FROM (",
        "  SELECT DATE_FORMAT(login_time, '%Y-%m-%d %H') as hour_group, login_status",
        "  FROM login_log",
        "  WHERE login_time BETWEEN #{startTime} AND #{endTime}",
        ") t",
        "GROUP BY hour_group",
        "ORDER BY hour_group"
    })
    List<Map<String, Object>> getHourlyLoginStatistics(@Param("startTime") LocalDateTime startTime,
                                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 统计独立用户数
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 独立用户数
     */
    @Select({
        "SELECT COUNT(DISTINCT user_id) as count",
        "FROM login_log",
        "WHERE login_time BETWEEN #{startTime} AND #{endTime}",
        "AND user_id IS NOT NULL"
    })
    Long countUniqueUsers(@Param("startTime") LocalDateTime startTime,
                          @Param("endTime") LocalDateTime endTime);

    /**
     * 统计独立IP数
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 独立IP数
     */
    @Select({
        "SELECT COUNT(DISTINCT client_ip) as count",
        "FROM login_log",
        "WHERE login_time BETWEEN #{startTime} AND #{endTime}",
        "AND client_ip IS NOT NULL"
    })
    Long countUniqueIps(@Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

    /**
     * 获取热门登录IP地址统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return IP地址统计
     */
    @Select({
        "SELECT client_ip,",
        "COUNT(*) as total_count,",
        "SUM(CASE WHEN login_status = 1 THEN 1 ELSE 0 END) as success_count,",
        "SUM(CASE WHEN login_status = 0 THEN 1 ELSE 0 END) as failure_count,",
        "MAX(location) as location,",
        "MAX(CASE WHEN is_suspicious = 1 THEN 1 ELSE 0 END) as has_suspicious",
        "FROM login_log",
        "WHERE login_time BETWEEN #{startTime} AND #{endTime}",
        "GROUP BY client_ip",
        "ORDER BY total_count DESC",
        "LIMIT #{limit}"
    })
    List<Map<String, Object>> getTopLoginIps(@Param("startTime") LocalDateTime startTime,
                                             @Param("endTime") LocalDateTime endTime,
                                             @Param("limit") Integer limit);

    /**
     * 获取设备类型统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 设备类型统计
     */
    @Select({
        "SELECT device_type,",
        "COUNT(*) as count,",
        "SUM(CASE WHEN login_status = 1 THEN 1 ELSE 0 END) as success_count,",
        "SUM(CASE WHEN login_status = 0 THEN 1 ELSE 0 END) as failure_count",
        "FROM login_log",
        "WHERE login_time BETWEEN #{startTime} AND #{endTime}",
        "AND device_type IS NOT NULL",
        "GROUP BY device_type",
        "ORDER BY count DESC"
    })
    List<Map<String, Object>> getDeviceTypeStatistics(@Param("startTime") LocalDateTime startTime,
                                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 获取操作系统统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作系统统计
     */
    @Select({
        "SELECT os_name,",
        "COUNT(*) as count",
        "FROM login_log",
        "WHERE login_time BETWEEN #{startTime} AND #{endTime}",
        "AND os_name IS NOT NULL",
        "GROUP BY os_name",
        "ORDER BY count DESC",
        "LIMIT 10"
    })
    List<Map<String, Object>> getOsStatistics(@Param("startTime") LocalDateTime startTime,
                                              @Param("endTime") LocalDateTime endTime);

    /**
     * 获取浏览器统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 浏览器统计
     */
    @Select({
        "SELECT browser_name,",
        "COUNT(*) as count",
        "FROM login_log",
        "WHERE login_time BETWEEN #{startTime} AND #{endTime}",
        "AND browser_name IS NOT NULL",
        "GROUP BY browser_name",
        "ORDER BY count DESC",
        "LIMIT 10"
    })
    List<Map<String, Object>> getBrowserStatistics(@Param("startTime") LocalDateTime startTime,
                                                    @Param("endTime") LocalDateTime endTime);

    /**
     * 获取每日登录统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 每日登录统计
     */
    @Select({
        "SELECT date_group as date,",
        "COUNT(*) as total_count,",
        "SUM(CASE WHEN login_status = 1 THEN 1 ELSE 0 END) as success_count,",
        "SUM(CASE WHEN login_status = 0 THEN 1 ELSE 0 END) as failure_count,",
        "COUNT(DISTINCT user_id) as unique_users,",
        "COUNT(DISTINCT client_ip) as unique_ips",
        "FROM (",
        "  SELECT DATE_FORMAT(login_time, '%Y-%m-%d') as date_group, login_status, user_id, client_ip",
        "  FROM login_log",
        "  WHERE login_time BETWEEN #{startTime} AND #{endTime}",
        ") t",
        "GROUP BY date_group",
        "ORDER BY date_group"
    })
    List<Map<String, Object>> getDailyLoginStatistics(@Param("startTime") LocalDateTime startTime,
                                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 获取地理位置统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 地理位置统计
     */
    @Select({
        "SELECT location,",
        "COUNT(*) as count",
        "FROM login_log",
        "WHERE login_time BETWEEN #{startTime} AND #{endTime}",
        "AND location IS NOT NULL",
        "GROUP BY location",
        "ORDER BY count DESC",
        "LIMIT 20"
    })
    List<Map<String, Object>> getLocationStatistics(@Param("startTime") LocalDateTime startTime,
                                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 物理删除过期的登录日志
     *
     * <p>注意：此方法执行物理删除，会真正从数据库中移除记录，包括已逻辑删除的记录</p>
     *
     * @param beforeTime 删除此时间之前的记录
     * @return 删除的记录数
     */
    @Select("DELETE FROM login_log WHERE create_time < #{beforeTime}")
    Long physicalCleanupExpiredLogs(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 物理清空所有登录日志
     *
     * <p>注意：此方法执行物理删除，会真正从数据库中移除所有记录，包括已逻辑删除的记录</p>
     *
     * @return 删除的记录数
     */
    @Select("DELETE FROM login_log")
    Long physicalClearAllLogs();
}
