package com.akey.core.dao.service.impl;

import com.akey.common.enums.IpWhitelistTypeEnum;
import com.akey.common.security.IpWhitelistValidator;
import com.akey.core.dao.service.IpWhitelistService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * IP白名单验证器实现类
 *
 * <p>提供IP白名单验证的具体实现，集成缓存和性能优化</p>
 * <p>支持系统白名单和接口白名单验证</p>
 * <p>异常时默认拒绝访问，确保安全</p>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class IpWhitelistValidatorImpl implements IpWhitelistValidator {

    private final IpWhitelistService ipWhitelistService;

    /**
     * 验证IP是否在白名单中
     *
     * <p>验证逻辑：</p>
     * <ul>
     *   <li>1. 参数有效性检查</li>
     *   <li>2. 调用白名单服务进行验证</li>
     *   <li>3. 支持用户级和系统级白名单</li>
     *   <li>4. 异常时默认拒绝访问</li>
     * </ul>
     *
     * @param type     白名单类型
     * @param userId   用户ID
     * @param clientIp 客户端IP
     * @return true:验证通过，false:验证失败
     */
    @Override
    public boolean isIpAllowed(IpWhitelistTypeEnum type, String userId, String clientIp) {
        try {
            log.debug("开始IP白名单验证, type={}, userId={}, clientIp={}", type, userId, clientIp);
            // 调用白名单服务进行验证
            return ipWhitelistService.validateIpWhitelist(type, clientIp, userId);
        } catch (Exception e) {
            log.error("IP白名单验证异常, type={}, userId={}, clientIp={}, error={}",
                    type, userId, clientIp, e.getMessage(), e);
            // 异常时默认拒绝访问，确保安全
            return false;
        }
    }
}
