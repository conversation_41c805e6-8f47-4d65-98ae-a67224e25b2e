package com.akey.core.dao.mapper;

import com.akey.core.dao.entity.OperationLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 操作日志Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Mapper
public interface OperationLogMapper extends BaseMapper<OperationLog> {

    /**
     * 根据操作模块统计操作数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    @Select({
        "<script>",
        "SELECT operation_module, COUNT(*) as count",
        "FROM operation_log",
        "WHERE deleted = 0",
        "<if test='startTime != null'>",
        "AND operation_time >= #{startTime}",
        "</if>",
        "<if test='endTime != null'>",
        "AND operation_time &lt;= #{endTime}",
        "</if>",
        "GROUP BY operation_module",
        "ORDER BY count DESC",
        "</script>"
    })
    List<Map<String, Object>> countByOperationModule(@Param("startTime") LocalDateTime startTime,
                                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 根据操作类型统计操作数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    @Select({
        "<script>",
        "SELECT operation_type, COUNT(*) as count",
        "FROM operation_log",
        "WHERE deleted = 0",
        "<if test='startTime != null'>",
        "AND operation_time >= #{startTime}",
        "</if>",
        "<if test='endTime != null'>",
        "AND operation_time &lt;= #{endTime}",
        "</if>",
        "GROUP BY operation_type",
        "ORDER BY count DESC",
        "</script>"
    })
    List<Map<String, Object>> countByOperationType(@Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 根据用户统计操作数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 统计结果
     */
    @Select({
        "<script>",
        "SELECT username, COUNT(*) as count",
        "FROM operation_log",
        "WHERE deleted = 0 AND username IS NOT NULL",
        "<if test='startTime != null'>",
        "AND operation_time >= #{startTime}",
        "</if>",
        "<if test='endTime != null'>",
        "AND operation_time &lt;= #{endTime}",
        "</if>",
        "GROUP BY username",
        "ORDER BY count DESC",
        "<if test='limit != null and limit > 0'>",
        "LIMIT #{limit}",
        "</if>",
        "</script>"
    })
    List<Map<String, Object>> countByUser(@Param("startTime") LocalDateTime startTime,
                                         @Param("endTime") LocalDateTime endTime,
                                         @Param("limit") Integer limit);

    /**
     * 获取操作统计信息
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计信息
     */
    @Select({
        "<script>",
        "SELECT",
        "COUNT(*) as totalCount,",
        "COUNT(CASE WHEN operation_status = 1 THEN 1 END) as successCount,",
        "COUNT(CASE WHEN operation_status = 0 THEN 1 END) as failureCount,",
        "COUNT(DISTINCT user_id) as uniqueUsers,",
        "COUNT(DISTINCT client_ip) as uniqueIps",
        "FROM operation_log",
        "WHERE deleted = 0",
        "<if test='startTime != null'>",
        "AND operation_time >= #{startTime}",
        "</if>",
        "<if test='endTime != null'>",
        "AND operation_time &lt;= #{endTime}",
        "</if>",
        "</script>"
    })
    Map<String, Object> getOperationStatistics(@Param("startTime") LocalDateTime startTime,
                                              @Param("endTime") LocalDateTime endTime);

    /**
     * 清理过期操作日志
     *
     * @param beforeTime 清理此时间之前的记录
     * @return 清理的记录数
     */
    @Select("DELETE FROM operation_log WHERE create_time < #{beforeTime}")
    Long cleanupExpiredLogs(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 物理清空所有操作日志
     *
     * <p>注意：此方法执行物理删除，会真正从数据库中移除所有记录，包括已逻辑删除的记录</p>
     *
     * @return 删除的记录数
     */
    @Select("DELETE FROM operation_log")
    Long physicalClearAllLogs();
}
