package com.akey.core.dao.service.impl;

import com.akey.common.entity.CaptchaResult;
import com.akey.common.enums.CaptchaTypeEnum;
import com.akey.common.exception.BusinessException;
import com.akey.common.util.CaptchaUtil;
import com.akey.core.dao.service.CaptchaService;
import com.akey.framework.core.config.CaptchaProperties;
import com.akey.framework.core.redis.util.RedisUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.UUID;

/**
 * 验证码Service实现类
 * 
 * <p>基于配置文件和Redis缓存的验证码服务实现</p>
 * 
 * <AUTHOR>
 * @since 2025-07-06
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CaptchaServiceImpl implements CaptchaService {

    private final CaptchaProperties captchaProperties;
    private final RedisUtil redisUtil;

    /**
     * 生成默认验证码
     * 
     * @param clientIp 客户端IP地址
     * @return 验证码结果
     */
    @Override
    public CaptchaResult generateCaptcha(String clientIp) {
        log.info("开始生成默认验证码, clientIp: {}", clientIp);
        
        // 检查是否启用验证码功能
        if (!captchaProperties.getEnabled()) {
            log.warn("验证码功能已禁用");
            throw new BusinessException("验证码功能已禁用");
        }
        
        // 检查IP频率限制
        if (!checkGenerateLimit(clientIp)) {
            log.warn("IP生成验证码频率超限, clientIp: {}", clientIp);
            throw new BusinessException("验证码生成过于频繁，请稍后再试");
        }
        
        // 获取默认验证码类型
        CaptchaTypeEnum type = CaptchaTypeEnum.fromCode(captchaProperties.getDefaultType());
        if (type == null) {
            type = CaptchaTypeEnum.NUMBER; // 降级到默认类型
        }

        // 如果配置的是随机类型，则随机选择一种验证码类型
        if (type.isRandom()) {
            type = CaptchaTypeEnum.getRandomType();
            log.info("随机选择验证码类型: {}, clientIp: {}", type.getCode(), clientIp);
        }
        
        return generateCaptchaInternal(type, captchaProperties.getDefaultLength(), 
            captchaProperties.getImage().getWidth(), captchaProperties.getImage().getHeight(), clientIp);
    }

    /**
     * 生成指定类型的验证码
     * 
     * @param type 验证码类型
     * @param clientIp 客户端IP地址
     * @return 验证码结果
     */
    @Override
    public CaptchaResult generateCaptcha(CaptchaTypeEnum type, String clientIp) {
        log.info("开始生成指定类型验证码, type: {}, clientIp: {}", type.getCode(), clientIp);
        
        if (!captchaProperties.getEnabled()) {
            throw new BusinessException("验证码功能已禁用");
        }
        
        if (!checkGenerateLimit(clientIp)) {
            throw new BusinessException("验证码生成过于频繁，请稍后再试");
        }
        
        return generateCaptchaInternal(type, captchaProperties.getDefaultLength(),
            captchaProperties.getImage().getWidth(), captchaProperties.getImage().getHeight(), clientIp);
    }

    /**
     * 生成指定类型和长度的验证码
     * 
     * @param type 验证码类型
     * @param length 验证码长度
     * @param clientIp 客户端IP地址
     * @return 验证码结果
     */
    @Override
    public CaptchaResult generateCaptcha(CaptchaTypeEnum type, Integer length, String clientIp) {
        log.info("开始生成指定类型和长度验证码, type: {}, length: {}, clientIp: {}", 
            type.getCode(), length, clientIp);
        
        if (!captchaProperties.getEnabled()) {
            throw new BusinessException("验证码功能已禁用");
        }
        
        if (!checkGenerateLimit(clientIp)) {
            throw new BusinessException("验证码生成过于频繁，请稍后再试");
        }
        
        return generateCaptchaInternal(type, length,
            captchaProperties.getImage().getWidth(), captchaProperties.getImage().getHeight(), clientIp);
    }

    /**
     * 生成自定义尺寸的验证码
     * 
     * @param type 验证码类型
     * @param length 验证码长度
     * @param width 图片宽度
     * @param height 图片高度
     * @param clientIp 客户端IP地址
     * @return 验证码结果
     */
    @Override
    public CaptchaResult generateCustomCaptcha(CaptchaTypeEnum type, Integer length, Integer width, Integer height, String clientIp) {
        log.info("开始生成自定义尺寸验证码, type: {}, length: {}, size: {}x{}, clientIp: {}", 
            type.getCode(), length, width, height, clientIp);
        
        if (!captchaProperties.getEnabled()) {
            throw new BusinessException("验证码功能已禁用");
        }
        
        if (!checkGenerateLimit(clientIp)) {
            throw new BusinessException("验证码生成过于频繁，请稍后再试");
        }
        
        return generateCaptchaInternal(type, length, width, height, clientIp);
    }

    /**
     * 验证验证码
     *
     * @param captchaId 验证码ID
     * @param userInput 用户输入的验证码
     * @param clientIp 客户端IP地址
     * @return 验证结果
     */
    @Override
    public boolean verifyCaptcha(String captchaId, String userInput, String clientIp) {
        log.info("开始验证验证码, captchaId: {}, clientIp: {}", captchaId, clientIp);

        // 先检查验证码是否正确
        boolean isValid = checkCaptcha(captchaId, userInput, clientIp);

        if (isValid) {
            // 验证成功，消费验证码
            consumeCaptcha(captchaId);
            log.info("验证码验证成功并已消费, captchaId: {}", captchaId);
        }

        return isValid;
    }

    /**
     * 检查验证码是否正确（不消费验证码）
     *
     * @param captchaId 验证码ID
     * @param userInput 用户输入的验证码
     * @param clientIp 客户端IP地址
     * @return 验证结果
     */
    @Override
    public boolean checkCaptcha(String captchaId, String userInput, String clientIp) {
        log.debug("开始检查验证码, captchaId: {}, clientIp: {}", captchaId, clientIp);

        if (!captchaProperties.getEnabled()) {
            log.warn("验证码功能已禁用");
            return false;
        }

        // 检查IP验证频率限制
        if (!checkVerifyLimit(clientIp)) {
            log.warn("IP验证验证码频率超限, clientIp: {}", clientIp);
            throw new BusinessException("验证码验证过于频繁，请稍后再试");
        }

        // 参数校验
        if (!StringUtils.hasText(captchaId) || !StringUtils.hasText(userInput)) {
            log.warn("验证码ID或用户输入为空");
            return false;
        }

        try {
            // 从缓存获取验证码答案
            String cacheKey = captchaProperties.getCacheKey(captchaId);
            Object cachedAnswer = redisUtil.get(cacheKey);

            if (cachedAnswer == null) {
                log.warn("验证码不存在或已过期, captchaId: {}", captchaId);
                throw new BusinessException("验证码不存在或已过期");
            }

            String correctAnswer = cachedAnswer.toString();

            // 验证答案
            boolean isValid;
            if (captchaProperties.getSecurity().getIgnoreCase()) {
                isValid = correctAnswer.equalsIgnoreCase(userInput.trim());
            } else {
                isValid = correctAnswer.equals(userInput.trim());
            }

            if (isValid) {
                log.debug("验证码检查成功, captchaId: {}", captchaId);
            } else {
                log.warn("验证码检查失败, captchaId: {}, userInput: {}, correctAnswer: {}",
                    captchaId, userInput, correctAnswer);
            }

            return isValid;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("检查验证码异常, captchaId: {}", captchaId, e);
            return false;
        }
    }

    /**
     * 消费验证码（删除缓存中的验证码）
     *
     * @param captchaId 验证码ID
     * @return 消费结果
     */
    @Override
    public boolean consumeCaptcha(String captchaId) {
        if (!StringUtils.hasText(captchaId)) {
            return false;
        }

        try {
            String cacheKey = captchaProperties.getCacheKey(captchaId);
            redisUtil.del(cacheKey);
            log.debug("验证码消费成功, captchaId: {}", captchaId);
            return true;
        } catch (Exception e) {
            log.error("消费验证码失败, captchaId: {}", captchaId, e);
            return false;
        }
    }

    /**
     * 刷新验证码
     * 
     * @param oldCaptchaId 旧验证码ID
     * @param type 验证码类型
     * @param clientIp 客户端IP地址
     * @return 新的验证码结果
     */
    @Override
    public CaptchaResult refreshCaptcha(String oldCaptchaId, CaptchaTypeEnum type, String clientIp) {
        log.info("开始刷新验证码, oldCaptchaId: {}, type: {}, clientIp: {}", 
            oldCaptchaId, type != null ? type.getCode() : "default", clientIp);
        
        // 删除旧的验证码
        if (StringUtils.hasText(oldCaptchaId)) {
            deleteCaptcha(oldCaptchaId);
        }
        
        // 生成新的验证码
        if (type != null) {
            return generateCaptcha(type, clientIp);
        } else {
            return generateCaptcha(clientIp);
        }
    }

    /**
     * 检查验证码是否存在
     * 
     * @param captchaId 验证码ID
     * @return 是否存在
     */
    @Override
    public boolean existsCaptcha(String captchaId) {
        if (!StringUtils.hasText(captchaId)) {
            return false;
        }
        
        String cacheKey = captchaProperties.getCacheKey(captchaId);
        return redisUtil.hasKey(cacheKey);
    }

    /**
     * 删除验证码
     *
     * @param captchaId 验证码ID
     * @return 删除成功返回true，失败返回false
     */
    @Override
    public boolean deleteCaptcha(String captchaId) {
        if (!StringUtils.hasText(captchaId)) {
            return false;
        }

        try {
            String cacheKey = captchaProperties.getCacheKey(captchaId);
            redisUtil.del(cacheKey);
            log.debug("删除验证码成功, captchaId: {}", captchaId);
            return true;
        } catch (Exception e) {
            log.error("删除验证码失败, captchaId: {}", captchaId, e);
            return false;
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 内部验证码生成方法
     *
     * @param type 验证码类型
     * @param length 验证码长度
     * @param width 图片宽度
     * @param height 图片高度
     * @param clientIp 客户端IP地址
     * @return 验证码结果
     */
    private CaptchaResult generateCaptchaInternal(CaptchaTypeEnum type, Integer length, Integer width, Integer height, String clientIp) {
        try {
            // 生成验证码
            CaptchaResult captchaResult;
            if (type.isArithmetic()) {
                captchaResult = CaptchaUtil.generateCustomCaptcha(type, 0, width, height);
            } else {
                captchaResult = CaptchaUtil.generateCustomCaptcha(type, length, width, height);
            }

            // 生成验证码ID
            String captchaId = UUID.randomUUID().toString();

            // 将验证码答案存储到Redis
            String cacheKey = captchaProperties.getCacheKey(captchaId);
            long expireMinutes = captchaProperties.getCache().getExpireMinutes();
            boolean set = redisUtil.set(cacheKey, captchaResult.getAnswer(), expireMinutes * 60);
            if(!set){
                log.error("生成验证码失败, type: {}, clientIp: {}, msg: redis存储失败", type.getCode(), clientIp);
                throw new BusinessException("Redis ERROR");
            }

            // 设置验证码ID到结果中
            captchaResult.setTimestamp(System.currentTimeMillis());

            // 记录生成日志
            if (captchaProperties.getSecurity().getEnableLogging()) {
                log.info("验证码生成成功, captchaId: {}, type: {}, clientIp: {}",
                    captchaId, type.getCode(), clientIp);
            }

            // 创建返回结果（不包含答案，但包含captchaId用于后续验证）
            return CaptchaResult.builder()
                    .captchaId(captchaId)
                    .imageBase64(captchaResult.getImageBase64())
                    .type(captchaResult.getType())
                    .expression(captchaResult.getExpression())
                    .length(captchaResult.getLength())
                    .timestamp(captchaResult.getTimestamp())
                    .build();

        } catch (Exception e) {
            log.error("生成验证码失败, type: {}, clientIp: {}", type.getCode(), clientIp, e);
            throw new BusinessException("生成验证码失败: " + e.getMessage());
        }
    }

    /**
     * 检查生成验证码的频率限制
     *
     * @param clientIp 客户端IP地址
     * @return 是否允许生成
     */
    private boolean checkGenerateLimit(String clientIp) {
        if (!captchaProperties.getSecurity().getEnableAntibrute()) {
            return true; // 未启用防暴力破解
        }

        if (!StringUtils.hasText(clientIp)) {
            return true; // IP为空，允许生成
        }

        try {
            String limitKey = captchaProperties.getAntibruteKey(clientIp, "generate");
            Object count = redisUtil.get(limitKey);

            int currentCount = (count != null) ? Integer.parseInt(count.toString()) : 0;
            int maxCount = captchaProperties.getSecurity().getMaxGeneratePerMinute();

            if (currentCount >= maxCount) {
                return false;
            }

            // 增加计数
            if (count == null) {
                redisUtil.set(limitKey, 1, 60);
            } else {
                redisUtil.incr(limitKey, 1);
            }

            return true;

        } catch (Exception e) {
            log.error("检查生成频率限制异常, clientIp: {}", clientIp, e);
            return true; // 异常时允许生成
        }
    }

    /**
     * 检查验证验证码的频率限制
     *
     * @param clientIp 客户端IP地址
     * @return 是否允许验证
     */
    private boolean checkVerifyLimit(String clientIp) {
        if (!captchaProperties.getSecurity().getEnableAntibrute()) {
            return true; // 未启用防暴力破解
        }

        if (!StringUtils.hasText(clientIp)) {
            return true; // IP为空，允许验证
        }

        try {
            String limitKey = captchaProperties.getAntibruteKey(clientIp, "verify");
            Object count = redisUtil.get(limitKey);

            int currentCount = (count != null) ? Integer.parseInt(count.toString()) : 0;
            int maxCount = captchaProperties.getSecurity().getMaxVerifyPerMinute();

            if (currentCount >= maxCount) {
                return false;
            }

            // 增加计数
            if (count == null) {
                redisUtil.set(limitKey, 1, 60);
            } else {
                redisUtil.incr(limitKey, 1);
            }

            return true;

        } catch (Exception e) {
            log.error("检查验证频率限制异常, clientIp: {}", clientIp, e);
            return true; // 异常时允许验证
        }
    }
}
