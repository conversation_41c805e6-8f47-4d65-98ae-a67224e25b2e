package com.akey.core.dao.service.impl;

import com.akey.common.constant.SystemConstant;
import com.akey.common.enums.EnableStatusEnum;
import com.akey.common.enums.MenuTypeEnum;
import com.akey.core.dao.entity.Menu;
import com.akey.core.dao.entity.User;
import com.akey.core.dao.service.MenuService;
import com.akey.core.dao.service.UserRouteService;
import com.akey.core.dao.service.UserService;
import com.akey.core.vo.MenuRouterTreeVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户路由Service实现类
 *
 * <p>提供用户路由相关的业务操作实现</p>
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserRouteServiceImpl implements UserRouteService {

    private final UserService userService;
    private final MenuService menuService;

    /**
     * 根据用户ID获取用户菜单树 (getUserRouters)
     *
     * @param userId 用户ID
     * @return 用户可访问的菜单树
     */
    @Override
    public List<MenuRouterTreeVO> getUserRouters(String userId) {
        log.info("开始获取用户菜单树, userId: {}", userId);

        if (!StringUtils.hasText(userId)) {
            return new ArrayList<>();
        }

        // 获取用户信息
        User user = userService.getUserById(userId);
        if (user == null) {
            return new ArrayList<>();
        }

        List<Menu> userMenus;

        // 超级管理员获取全部菜单
        if (isSuperAdmin(userId)) {
            userMenus = menuService.getAllEnabledMenus();
        } else {
            // 普通用户根据用户类型获取菜单
            userMenus = menuService.getMenusByUserTypeId(user.getUserTypeId());
        }

        // 过滤掉按钮类型的菜单
        List<Menu> filteredMenus = userMenus.stream()
                .filter(menu -> menu.getMenuType() != MenuTypeEnum.BUTTON)
                .filter(menu -> menu.getStatus() == EnableStatusEnum.ENABLED)
                .collect(Collectors.toList());

        // 构建菜单树
        List<Menu> menuTree = buildMenuTree(filteredMenus, null);

        // 转换为VO对象
        List<MenuRouterTreeVO> menuRouterTreeVOS = convertToMenuTreeVOs(menuTree);

        log.info("获取用户菜单树完成, userId: {}, 菜单数: {}", userId, menuRouterTreeVOS.size());
        return menuRouterTreeVOS;
    }

    /**
     * 根据用户ID获取用户权限字符串列表 (getUserPermissions)
     *
     * @param userId 用户ID
     * @return 权限字符串列表
     */
    @Override
    public List<String> getUserPermissions(String userId) {
        log.info("开始获取用户权限列表, userId: {}", userId);

        if (!StringUtils.hasText(userId)) {
            return new ArrayList<>();
        }

        // 获取用户信息
        User user = userService.getUserById(userId);
        if (user == null) {
            return new ArrayList<>();
        }

        List<Menu> userMenus;

        // 超级管理员获取全部菜单
        if (isSuperAdmin(userId)) {
            userMenus = menuService.getAllEnabledMenus();
        } else {
            // 普通用户根据用户类型获取菜单
            userMenus = menuService.getMenusByUserTypeId(user.getUserTypeId());
        }

        // 提取权限字符串
        List<String> permissions = new ArrayList<>();
        for (Menu menu : userMenus) {
            if (StringUtils.hasText(menu.getPermission())) {
                permissions.add(menu.getPermission());
            }
        }

        log.info("获取用户权限列表完成, userId: {}, 权限数: {}", userId, permissions.size());
        return permissions;
    }

    /**
     * 检查用户是否为超级管理员
     *
     * @param userId 用户ID
     * @return 是超级管理员返回true，否则返回false
     */
    private boolean isSuperAdmin(String userId) {
        if (!StringUtils.hasText(userId)) {
            return false;
        }

        User user = userService.getUserById(userId);
        if (user == null) {
            return false;
        }

        // 检查用户类型是否为超级管理员
        return Objects.equals(user.getUserTypeId(), SystemConstant.ADMIN_USER_TYPE_ID);
    }



    /**
     * 构建菜单树
     *
     * @param menus 菜单列表
     * @param parentId 父菜单ID
     * @return 菜单树
     */
    private List<Menu> buildMenuTree(List<Menu> menus, String parentId) {
        List<Menu> tree = new ArrayList<>();

        for (Menu menu : menus) {
            if (Objects.equals(menu.getParentId(), parentId)) {
                List<Menu> children = buildMenuTree(menus, menu.getId());
                menu.setChildren(children);
                tree.add(menu);
            }
        }

        // 按排序号排序
        tree.sort((m1, m2) -> {
            Integer sort1 = m1.getSortOrder() != null ? m1.getSortOrder() : 0;
            Integer sort2 = m2.getSortOrder() != null ? m2.getSortOrder() : 0;
            return sort1.compareTo(sort2);
        });

        return tree;
    }

    /**
     * 转换菜单树为VO对象
     *
     * @param menuTree 菜单树
     * @return 菜单树VO列表
     */
    private List<MenuRouterTreeVO> convertToMenuTreeVOs(List<Menu> menuTree) {
        List<MenuRouterTreeVO> voList = new ArrayList<>();

        for (Menu menu : menuTree) {
            MenuRouterTreeVO vo = convertToMenuTreeVO(menu);
            voList.add(vo);
        }

        return voList;
    }

    /**
     * 转换单个菜单为VO对象
     *
     * @param menu 菜单实体
     * @return 菜单VO
     */
    private MenuRouterTreeVO convertToMenuTreeVO(Menu menu) {
        MenuRouterTreeVO vo = new MenuRouterTreeVO();

        // 复制基本属性
        BeanUtils.copyProperties(menu, vo);

        // 递归处理子菜单
        if (menu.getChildren() != null && !menu.getChildren().isEmpty()) {
            List<MenuRouterTreeVO> children = convertToMenuTreeVOs(menu.getChildren());
            vo.setChildren(children);
        }

        return vo;
    }
}
