package com.akey.core.dao.mapper;

import com.akey.common.enums.EnableStatusEnum;
import com.akey.common.enums.IpWhitelistTypeEnum;
import com.akey.core.dao.entity.IpWhitelist;
import com.akey.core.vo.IpWhitelistWithUserVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * IP白名单Mapper接口
 * 
 * <p>继承BaseMapper，利用MyBatis-Plus提供的单表CRUD操作</p>
 * <p>复杂查询通过Service层使用QueryWrapper构建</p>
 * 
 * <p>功能说明：</p>
 * <ul>
 *   <li>提供IP白名单的基础数据访问操作</li>
 *   <li>支持分页查询、条件查询、批量操作等</li>
 *   <li>利用MyBatis-Plus的条件构造器进行复杂查询</li>
 *   <li>支持IP地址精确匹配和模糊匹配</li>
 * </ul>
 * 
 * <p>主要查询场景：</p>
 * <ul>
 *   <li>根据白名单类型和IP地址查询</li>
 *   <li>根据用户ID查询用户的白名单</li>
 *   <li>查询有效的白名单（enable_status=1且deleted=0）</li>
 *   <li>支持IP地址模糊匹配和CIDR格式匹配</li>
 *   <li>分页查询白名单列表</li>
 *   <li>批量操作支持</li>
 * </ul>
 * 
 * <AUTHOR>
 * @since 2025-07-31
 */
@Mapper
public interface IpWhitelistMapper extends BaseMapper<IpWhitelist> {

    // MyBatis-Plus已提供完整的单表CRUD操作
    // 复杂查询在Service层使用QueryWrapper/LambdaQueryWrapper构建

    /**
     * 分页查询IP白名单列表（带用户信息）
     *
     * <p>通过连表查询获取IP白名单和关联的用户信息</p>
     * <p>支持多种筛选条件和模糊查询</p>
     *
     * @param page 分页参数
     * @param whitelistType 白名单类型（可选）
     * @param ipAddress IP地址（模糊查询，可选）
     * @param userId 用户ID（可选）
     * @param username 用户名（模糊查询，可选）
     * @param enableStatus 启用状态（可选）
     * @return 分页结果（包含用户信息）
     */
    @Select("<script>" +
            "SELECT " +
            "    w.id, " +
            "    w.whitelist_type, " +
            "    w.ip_address, " +
            "    w.user_id, " +
            "    u.username, " +
            "    w.enable_status, " +
            "    w.remark, " +
            "    w.create_time, " +
            "    w.create_by, " +
            "    w.update_time, " +
            "    w.update_by " +
            "FROM sys_ip_whitelist w " +
            "LEFT JOIN sys_user u ON w.user_id = u.id AND u.deleted = 0 " +
            "WHERE w.deleted = 0 " +
            "<if test='whitelistType != null'>" +
            "    AND w.whitelist_type = #{whitelistType} " +
            "</if>" +
            "<if test='ipAddress != null and ipAddress != \"\"'>" +
            "    AND w.ip_address LIKE CONCAT('%', #{ipAddress}, '%') " +
            "</if>" +
            "<if test='userId != null and userId != \"\"'>" +
            "    AND w.user_id = #{userId} " +
            "</if>" +
            "<if test='username != null and username != \"\"'>" +
            "    AND u.username LIKE CONCAT('%', #{username}, '%') " +
            "</if>" +
            "<if test='enableStatus != null'>" +
            "    AND w.enable_status = #{enableStatus} " +
            "</if>" +
            "ORDER BY w.create_time DESC" +
            "</script>")
    IPage<IpWhitelistWithUserVO> selectIpWhitelistPageWithUser(
            Page<IpWhitelistWithUserVO> page,
            @Param("whitelistType") IpWhitelistTypeEnum whitelistType,
            @Param("ipAddress") String ipAddress,
            @Param("userId") String userId,
            @Param("username") String username,
            @Param("enableStatus") EnableStatusEnum enableStatus
    );

}
