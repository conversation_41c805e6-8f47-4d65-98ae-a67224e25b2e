package com.akey.core.dao.service;

import com.akey.core.dao.entity.UserTypeMenu;

import java.util.List;

/**
 * 用户类型菜单关联Service接口
 * 
 * <p>提供用户类型与菜单关联关系的业务操作</p>
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface UserTypeMenuService {

    /**
     * 为用户类型分配单个菜单
     * 
     * @param userTypeId 用户类型ID
     * @param menuId 菜单ID
     * @return 分配成功返回true，失败或已存在返回false
     */
    boolean assignMenuToUserType(String userTypeId, String menuId);

    /**
     * 为用户类型批量分配菜单
     * 
     * <p>先清空该用户类型的所有菜单关联，再添加新的关联</p>
     * 
     * @param userTypeId 用户类型ID
     * @param menuIds 菜单ID列表
     * @return 分配成功返回true，失败返回false
     */
    boolean assignMenusToUserType(String userTypeId, List<String> menuIds);

    /**
     * 移除用户类型的单个菜单
     * 
     * @param userTypeId 用户类型ID
     * @param menuId 菜单ID
     * @return 移除成功返回true，失败或关联不存在返回false
     */
    boolean removeMenuFromUserType(String userTypeId, String menuId);

    /**
     * 移除用户类型的所有菜单
     * 
     * @param userTypeId 用户类型ID
     * @return 移除的菜单数量
     */
    int removeAllMenusFromUserType(String userTypeId);

    /**
     * 移除菜单的所有用户类型关联
     * 
     * @param menuId 菜单ID
     * @return 移除的用户类型数量
     */
    int removeAllUserTypesFromMenu(String menuId);

    /**
     * 获取用户类型关联的所有菜单ID
     * 
     * @param userTypeId 用户类型ID
     * @return 菜单ID列表
     */
    List<String> getMenuIdsByUserType(String userTypeId);

    /**
     * 获取菜单关联的所有用户类型ID
     * 
     * @param menuId 菜单ID
     * @return 用户类型ID列表
     */
    List<String> getUserTypeIdsByMenu(String menuId);

    /**
     * 检查用户类型是否分配了指定菜单
     * 
     * @param userTypeId 用户类型ID
     * @param menuId 菜单ID
     * @return 已分配返回true，未分配返回false
     */
    boolean isMenuAssignedToUserType(String userTypeId, String menuId);

    /**
     * 检查菜单是否分配给了任何用户类型
     * 
     * @param menuId 菜单ID
     * @return 已分配返回true，未分配返回false
     */
    boolean isMenuAssignedToAnyUserType(String menuId);

    /**
     * 检查用户类型是否分配了任何菜单
     * 
     * @param userTypeId 用户类型ID
     * @return 已分配返回true，未分配返回false
     */
    boolean hasUserTypeAssignedAnyMenu(String userTypeId);

    /**
     * 获取用户类型的菜单关联记录
     * 
     * @param userTypeId 用户类型ID
     * @return 关联记录列表
     */
    List<UserTypeMenu> getUserTypeMenusByUserType(String userTypeId);

    /**
     * 获取菜单的用户类型关联记录
     * 
     * @param menuId 菜单ID
     * @return 关联记录列表
     */
    List<UserTypeMenu> getUserTypeMenusByMenu(String menuId);

    /**
     * 获取所有用户类型菜单关联记录
     * 
     * @return 所有关联记录列表
     */
    List<UserTypeMenu> getAllUserTypeMenus();

    /**
     * 批量删除用户类型菜单关联
     * 
     * @param userTypeMenuIds 关联记录ID列表
     * @return 删除的记录数量
     */
    int batchDeleteUserTypeMenus(List<String> userTypeMenuIds);

    /**
     * 复制用户类型的菜单权限到另一个用户类型
     * 
     * @param sourceUserTypeId 源用户类型ID
     * @param targetUserTypeId 目标用户类型ID
     * @return 复制成功返回true，失败返回false
     */
    boolean copyMenusFromUserType(String sourceUserTypeId, String targetUserTypeId);

    /**
     * 统计用户类型的菜单数量
     * 
     * @param userTypeId 用户类型ID
     * @return 菜单数量
     */
    long countMenusByUserType(String userTypeId);

    /**
     * 统计菜单的用户类型数量
     * 
     * @param menuId 菜单ID
     * @return 用户类型数量
     */
    long countUserTypesByMenu(String menuId);
} 