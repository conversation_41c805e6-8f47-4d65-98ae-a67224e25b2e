package com.akey.core.dao.entity;

import com.akey.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 登录日志实体类
 *
 * <p>记录用户登录行为和安全审计信息</p>
 * <p>支持登录成功和失败两种情况的详细记录</p>
 * <p>继承BaseEntity，获得公共字段：id、createTime、updateTime、deleted、version等</p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("login_log")
public class LoginLog extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     * 
     * <p>登录失败时可能为空</p>
     */
    @TableField("user_id")
    private String userId;

    /**
     * 登录用户名
     * 
     * <p>即使登录失败也记录，用于安全审计</p>
     */
    @TableField("username")
    private String username;

    /**
     * 登录时间
     */
    @TableField("login_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime loginTime;

    /**
     * 登录状态
     * 
     * <p>0-失败，1-成功</p>
     */
    @TableField("login_status")
    private Integer loginStatus;

    /**
     * 登录失败原因
     */
    @TableField("failure_reason")
    private String failureReason;

    /**
     * 会话ID
     * 
     * <p>登录成功时记录</p>
     */
    @TableField("session_id")
    private String sessionId;



    /**
     * 客户端IP地址
     * 
     * <p>支持IPv6</p>
     */
    @TableField("client_ip")
    private String clientIp;



    /**
     * 是否通过代理
     * 
     * <p>0-否，1-是</p>
     */
    @TableField("is_proxy")
    private Integer isProxy;

    /**
     * 代理链信息
     * 
     * <p>记录X-Forwarded-For等代理信息</p>
     */
    @TableField("proxy_chain")
    private String proxyChain;

    /**
     * 地理位置
     *
     * <p>格式：国家 省份 城市</p>
     */
    @TableField("location")
    private String location;



    /**
     * 完整的User-Agent字符串
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 设备类型
     * 
     * <p>DESKTOP, MOBILE, TABLET, BOT</p>
     */
    @TableField("device_type")
    private String deviceType;

    /**
     * 设备品牌
     * 
     * <p>Apple, Samsung, Huawei等</p>
     */
    @TableField("device_brand")
    private String deviceBrand;

    /**
     * 设备型号
     */
    @TableField("device_model")
    private String deviceModel;

    /**
     * 操作系统名称
     * 
     * <p>Windows, macOS, Android, iOS等</p>
     */
    @TableField("os_name")
    private String osName;

    /**
     * 操作系统版本
     */
    @TableField("os_version")
    private String osVersion;

    /**
     * 浏览器名称
     * 
     * <p>Chrome, Firefox, Safari等</p>
     */
    @TableField("browser_name")
    private String browserName;

    /**
     * 浏览器版本
     */
    @TableField("browser_version")
    private String browserVersion;

    /**
     * 设备指纹
     * 
     * <p>MD5哈希值，用于设备识别</p>
     */
    @TableField("device_fingerprint")
    private String deviceFingerprint;

    /**
     * 风险等级
     * 
     * <p>LOW-低风险，MEDIUM-中风险，HIGH-高风险</p>
     */
    @TableField("risk_level")
    private String riskLevel;

    /**
     * 是否可疑登录
     * 
     * <p>0-否，1-是</p>
     */
    @TableField("is_suspicious")
    private Integer isSuspicious;

    /**
     * 可疑原因列表
     * 
     * <p>JSON格式存储，反序列化为List</p>
     */
    @TableField(value = "suspicious_reasons", typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    private List<String> suspiciousReasons;



    /**
     * 请求URI
     */
    @TableField("request_uri")
    private String requestUri;

    /**
     * 请求方法
     * 
     * <p>GET, POST等</p>
     */
    @TableField("request_method")
    private String requestMethod;

    /**
     * 来源页面
     */
    @TableField("referer")
    private String referer;

    /**
     * 客户端语言
     */
    @TableField("language")
    private String language;

    /**
     * 客户端时区
     */
    @TableField("timezone")
    private String timezone;

    /**
     * 扩展数据
     * 
     * <p>JSON格式存储额外信息</p>
     */
    @TableField(value = "extra_data", typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    private Map<String, Object> extraData;

    /**
     * 标签
     * 
     * <p>逗号分隔，用于分类和检索</p>
     */
    @TableField("tags")
    private String tags;


}
