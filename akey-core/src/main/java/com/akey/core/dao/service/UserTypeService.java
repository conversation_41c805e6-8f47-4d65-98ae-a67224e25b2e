package com.akey.core.dao.service;

import com.akey.core.dao.entity.UserType;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 用户类型Service接口
 * 
 * <p>提供用户类型相关的业务操作</p>
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface UserTypeService {

    /**
     * 1. 增加用户类型
     * 
     * @param userType 用户类型信息
     * @return 创建成功返回true，失败返回false
     */
    boolean createUserType(UserType userType);

    /**
     * 2. 修改用户类型名称，是否内置
     * 
     * @param userType 用户类型信息（必须包含id）
     * @return 修改成功返回true，失败返回false
     */
    boolean updateUserType(UserType userType);

    /**
     * 3. 删除用户类型
     * 
     * @param id 用户类型ID
     * @return 删除成功返回true，失败返回false
     */
    boolean deleteUserType(String id);

    /**
     * 4. 分页查询用户类型列表
     * 
     * @param page 分页参数
     * @param typeName 类型名称（模糊查询，可为空）
     * @param isBuiltin 是否内置（可为空，查询全部）
     * @return 分页结果
     */
    IPage<UserType> getUserTypePageList(Page<UserType> page, String typeName, Integer isBuiltin);

    /**
     * 5. 根据用户ID获取用户类型列表，需要排除超级管理员类型
     * 
     * @param userId 当前用户ID
     * @return 可分配的用户类型列表
     */
    List<UserType> getAvailableUserTypesForUser(String userId);

    /**
     * 根据类型名称查询用户类型
     * 
     * @param typeName 类型名称
     * @return 用户类型，如果不存在则返回null
     */
    UserType getUserTypeByName(String typeName);

    /**
     * 根据ID查询用户类型
     * 
     * @param id 用户类型ID
     * @return 用户类型，如果不存在则返回null
     */
    UserType getUserTypeById(String id);

    /**
     * 检查类型名称是否已存在
     * 
     * @param typeName 类型名称
     * @param excludeId 要排除的ID（用于修改时排除自己），可为空
     * @return 存在返回true，不存在返回false
     */
    boolean existsByTypeName(String typeName, String excludeId);

    /**
     * 查询是否有用户正在使用指定的用户类型
     * 
     * @param userTypeId 用户类型ID
     * @return 有用户使用返回true，没有返回false
     */
    boolean hasUsersWithType(String userTypeId);

    /**
     * 获取所有用户类型列表
     *
     * @return 用户类型列表
     */
    List<UserType> getAllUserTypes();

    /**
     * 获取所有用户类型列表,如果登录用户非超级管理员类型，则需要排除
     *
     * @return 用户类型列表
     */
    List<UserType> getAllUserTypes(String userId);

    /**
     * 统计指定用户类型的用户数量
     *
     * @param userTypeId 用户类型ID
     * @return 用户数量
     */
    long countUsersByUserType(String userTypeId);

    /**
     * 分页查询用户类型列表（排除超级管理员类型）
     *
     * @param page 分页参数
     * @param typeName 类型名称（模糊查询，可为空）
     * @param isBuiltin 是否内置（可为空，查询全部）
     * @return 分页结果
     */
    IPage<UserType> getUserTypePageListExcludeAdmin(Page<UserType> page, String typeName, Integer isBuiltin);

    /**
     * 判断指定的用户类型是否为超级管理员类型
     *
     * <p>通过用户类型ID与系统常量中的超级管理员类型ID进行比较</p>
     *
     * @param userTypeId 用户类型ID
     * @return 是超级管理员类型返回true，否则返回false
     */
    boolean isSuperAdminType(String userTypeId);

    /**
     * 判断指定的用户类型对象是否为超级管理员类型
     *
     * <p>通过用户类型对象的ID与系统常量中的超级管理员类型ID进行比较</p>
     *
     * @param userType 用户类型对象
     * @return 是超级管理员类型返回true，否则返回false
     */
    boolean isSuperAdminType(UserType userType);
}