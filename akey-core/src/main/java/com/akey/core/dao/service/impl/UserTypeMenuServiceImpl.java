package com.akey.core.dao.service.impl;

import com.akey.core.cache.UserCacheService;
import com.akey.core.dao.entity.UserTypeMenu;
import com.akey.core.dao.mapper.UserTypeMenuMapper;
import com.akey.core.dao.service.UserTypeMenuService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 用户类型菜单关联Service实现类
 * 
 * <p>提供用户类型与菜单关联关系的业务操作实现</p>
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserTypeMenuServiceImpl implements UserTypeMenuService {

    private final UserTypeMenuMapper userTypeMenuMapper;
    private final UserCacheService userCacheService;

    /**
     * 为用户类型分配单个菜单
     * 
     * @param userTypeId 用户类型ID
     * @param menuId 菜单ID
     * @return 分配成功返回true，失败或已存在返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignMenuToUserType(String userTypeId, String menuId) {
        log.info("开始为用户类型分配菜单, userTypeId: {}, menuId: {}", userTypeId, menuId);
        
        if (!StringUtils.hasText(userTypeId) || !StringUtils.hasText(menuId)) {
            log.warn("用户类型ID或菜单ID不能为空, userTypeId: {}, menuId: {}", userTypeId, menuId);
            return false;
        }
        
        // 检查关联是否已存在
        if (isMenuAssignedToUserType(userTypeId, menuId)) {
            log.warn("用户类型菜单关联已存在, userTypeId: {}, menuId: {}", userTypeId, menuId);
            return false;
        }
        
        UserTypeMenu userTypeMenu = new UserTypeMenu();
        userTypeMenu.setUserTypeId(userTypeId);
        userTypeMenu.setMenuId(menuId);
        
        int result = userTypeMenuMapper.insert(userTypeMenu);
        
        log.info("为用户类型分配菜单完成, userTypeId: {}, menuId: {}, result: {}", 
                userTypeId, menuId, result > 0 ? "成功" : "失败");
        return result > 0;
    }

    /**
     * 为用户类型批量分配菜单
     * 
     * <p>先清空该用户类型的所有菜单关联，再添加新的关联</p>
     * 
     * @param userTypeId 用户类型ID
     * @param menuIds 菜单ID列表
     * @return 分配成功返回true，失败返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignMenusToUserType(String userTypeId, List<String> menuIds) {
        log.info("开始为用户类型批量分配菜单, userTypeId: {}, menuIds: {}", userTypeId, menuIds);
        
        if (!StringUtils.hasText(userTypeId)) {
            log.warn("用户类型ID不能为空, userTypeId: {}", userTypeId);
            return false;
        }
        
        // 先清空该用户类型的所有菜单关联
        int removedCount = removeAllMenusFromUserType(userTypeId);
        log.info("清空用户类型原有菜单关联, userTypeId: {}, 清空数量: {}", userTypeId, removedCount);
        
        // 如果新菜单列表为空，则只清空，不添加新关联
        if (CollectionUtils.isEmpty(menuIds)) {
            log.info("新菜单列表为空，仅清空原有关联, userTypeId: {}", userTypeId);
            return true;
        }
        
        // 过滤重复的菜单ID
        List<String> distinctMenuIds = menuIds.stream()
                .filter(StringUtils::hasText)
                .distinct()
                .collect(Collectors.toList());
        
        if (distinctMenuIds.isEmpty()) {
            log.warn("过滤后的菜单ID列表为空, userTypeId: {}", userTypeId);
            return true;
        }
        
        // 批量插入新的关联
        List<UserTypeMenu> userTypeMenus = new ArrayList<>();
        for (String menuId : distinctMenuIds) {
            UserTypeMenu userTypeMenu = new UserTypeMenu();
            userTypeMenu.setUserTypeId(userTypeId);
            userTypeMenu.setMenuId(menuId);
            userTypeMenus.add(userTypeMenu);
        }
        
        int insertedCount = 0;
        for (UserTypeMenu userTypeMenu : userTypeMenus) {
            int result = userTypeMenuMapper.insert(userTypeMenu);
            if (result > 0) {
                insertedCount++;
            }
        }
        
        log.info("为用户类型批量分配菜单完成, userTypeId: {}, 期望插入: {}, 实际插入: {}",
                userTypeId, distinctMenuIds.size(), insertedCount);

        // 分配成功后清除相关缓存
        if (insertedCount > 0) {
            userCacheService.clearUserTypeCache(userTypeId);
            userCacheService.clearAllUserPermissionsAndRolesCache();
            log.debug("已清除用户类型缓存和所有用户权限角色缓存, userTypeId: {}", userTypeId);
        }

        return insertedCount == distinctMenuIds.size();
    }

    /**
     * 移除用户类型的单个菜单
     * 
     * @param userTypeId 用户类型ID
     * @param menuId 菜单ID
     * @return 移除成功返回true，失败或关联不存在返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeMenuFromUserType(String userTypeId, String menuId) {
        log.info("开始移除用户类型的菜单, userTypeId: {}, menuId: {}", userTypeId, menuId);
        
        if (!StringUtils.hasText(userTypeId) || !StringUtils.hasText(menuId)) {
            log.warn("用户类型ID或菜单ID不能为空, userTypeId: {}, menuId: {}", userTypeId, menuId);
            return false;
        }
        
        LambdaUpdateWrapper<UserTypeMenu> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UserTypeMenu::getUserTypeId, userTypeId)
                    .eq(UserTypeMenu::getMenuId, menuId);
        
        int result = userTypeMenuMapper.delete(updateWrapper);
        
        log.info("移除用户类型的菜单完成, userTypeId: {}, menuId: {}, result: {}", 
                userTypeId, menuId, result > 0 ? "成功" : "失败");
        return result > 0;
    }

    /**
     * 移除用户类型的所有菜单
     * 
     * @param userTypeId 用户类型ID
     * @return 移除的菜单数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int removeAllMenusFromUserType(String userTypeId) {
        log.info("开始移除用户类型的所有菜单, userTypeId: {}", userTypeId);
        
        if (!StringUtils.hasText(userTypeId)) {
            log.warn("用户类型ID不能为空, userTypeId: {}", userTypeId);
            return 0;
        }
        
        LambdaUpdateWrapper<UserTypeMenu> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UserTypeMenu::getUserTypeId, userTypeId);
        
        int result = userTypeMenuMapper.delete(updateWrapper);
        
        log.info("移除用户类型的所有菜单完成, userTypeId: {}, 移除数量: {}", userTypeId, result);
        return result;
    }

    /**
     * 移除菜单的所有用户类型关联
     * 
     * @param menuId 菜单ID
     * @return 移除的用户类型数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int removeAllUserTypesFromMenu(String menuId) {
        log.info("开始移除菜单的所有用户类型关联, menuId: {}", menuId);
        
        if (!StringUtils.hasText(menuId)) {
            log.warn("菜单ID不能为空, menuId: {}", menuId);
            return 0;
        }
        
        LambdaUpdateWrapper<UserTypeMenu> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UserTypeMenu::getMenuId, menuId);
        
        int result = userTypeMenuMapper.delete(updateWrapper);
        
        log.info("移除菜单的所有用户类型关联完成, menuId: {}, 移除数量: {}", menuId, result);
        return result;
    }

    /**
     * 获取用户类型关联的所有菜单ID
     * 
     * @param userTypeId 用户类型ID
     * @return 菜单ID列表
     */
    @Override
    public List<String> getMenuIdsByUserType(String userTypeId) {
        if (!StringUtils.hasText(userTypeId)) {
            return Collections.emptyList();
        }
        
        LambdaQueryWrapper<UserTypeMenu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserTypeMenu::getUserTypeId, userTypeId)
                   .select(UserTypeMenu::getMenuId);
        
        List<UserTypeMenu> userTypeMenus = userTypeMenuMapper.selectList(queryWrapper);
        
        return userTypeMenus.stream()
                .map(UserTypeMenu::getMenuId)
                .filter(StringUtils::hasText)
                .collect(Collectors.toList());
    }

    /**
     * 获取菜单关联的所有用户类型ID
     * 
     * @param menuId 菜单ID
     * @return 用户类型ID列表
     */
    @Override
    public List<String> getUserTypeIdsByMenu(String menuId) {
        if (!StringUtils.hasText(menuId)) {
            return Collections.emptyList();
        }
        
        LambdaQueryWrapper<UserTypeMenu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserTypeMenu::getMenuId, menuId)
                   .select(UserTypeMenu::getUserTypeId);
        
        List<UserTypeMenu> userTypeMenus = userTypeMenuMapper.selectList(queryWrapper);
        
        return userTypeMenus.stream()
                .map(UserTypeMenu::getUserTypeId)
                .filter(StringUtils::hasText)
                .collect(Collectors.toList());
    }

    /**
     * 检查用户类型是否分配了指定菜单
     * 
     * @param userTypeId 用户类型ID
     * @param menuId 菜单ID
     * @return 已分配返回true，未分配返回false
     */
    @Override
    public boolean isMenuAssignedToUserType(String userTypeId, String menuId) {
        if (!StringUtils.hasText(userTypeId) || !StringUtils.hasText(menuId)) {
            return false;
        }
        
        LambdaQueryWrapper<UserTypeMenu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserTypeMenu::getUserTypeId, userTypeId)
                   .eq(UserTypeMenu::getMenuId, menuId);
        
        return userTypeMenuMapper.selectCount(queryWrapper) > 0;
    }

    /**
     * 检查菜单是否分配给了任何用户类型
     * 
     * @param menuId 菜单ID
     * @return 已分配返回true，未分配返回false
     */
    @Override
    public boolean isMenuAssignedToAnyUserType(String menuId) {
        if (!StringUtils.hasText(menuId)) {
            return false;
        }
        
        LambdaQueryWrapper<UserTypeMenu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserTypeMenu::getMenuId, menuId);
        
        return userTypeMenuMapper.selectCount(queryWrapper) > 0;
    }

    /**
     * 检查用户类型是否分配了任何菜单
     * 
     * @param userTypeId 用户类型ID
     * @return 已分配返回true，未分配返回false
     */
    @Override
    public boolean hasUserTypeAssignedAnyMenu(String userTypeId) {
        if (!StringUtils.hasText(userTypeId)) {
            return false;
        }
        
        LambdaQueryWrapper<UserTypeMenu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserTypeMenu::getUserTypeId, userTypeId);
        
        return userTypeMenuMapper.selectCount(queryWrapper) > 0;
    }

    /**
     * 获取用户类型的菜单关联记录
     * 
     * @param userTypeId 用户类型ID
     * @return 关联记录列表
     */
    @Override
    public List<UserTypeMenu> getUserTypeMenusByUserType(String userTypeId) {
        if (!StringUtils.hasText(userTypeId)) {
            return Collections.emptyList();
        }
        
        LambdaQueryWrapper<UserTypeMenu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserTypeMenu::getUserTypeId, userTypeId)
                   .orderByAsc(UserTypeMenu::getCreateTime);
        
        return userTypeMenuMapper.selectList(queryWrapper);
    }

    /**
     * 获取菜单的用户类型关联记录
     * 
     * @param menuId 菜单ID
     * @return 关联记录列表
     */
    @Override
    public List<UserTypeMenu> getUserTypeMenusByMenu(String menuId) {
        if (!StringUtils.hasText(menuId)) {
            return Collections.emptyList();
        }
        
        LambdaQueryWrapper<UserTypeMenu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserTypeMenu::getMenuId, menuId)
                   .orderByAsc(UserTypeMenu::getCreateTime);
        
        return userTypeMenuMapper.selectList(queryWrapper);
    }

    /**
     * 获取所有用户类型菜单关联记录
     * 
     * @return 所有关联记录列表
     */
    @Override
    public List<UserTypeMenu> getAllUserTypeMenus() {
        LambdaQueryWrapper<UserTypeMenu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(UserTypeMenu::getUserTypeId, UserTypeMenu::getCreateTime);
        
        return userTypeMenuMapper.selectList(queryWrapper);
    }

    /**
     * 批量删除用户类型菜单关联
     * 
     * @param userTypeMenuIds 关联记录ID列表
     * @return 删除的记录数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchDeleteUserTypeMenus(List<String> userTypeMenuIds) {
        log.info("开始批量删除用户类型菜单关联, userTypeMenuIds: {}", userTypeMenuIds);
        
        if (CollectionUtils.isEmpty(userTypeMenuIds)) {
            return 0;
        }
        
        // 过滤有效的ID
        List<String> validIds = userTypeMenuIds.stream()
                .filter(StringUtils::hasText)
                .distinct()
                .collect(Collectors.toList());
        
        if (validIds.isEmpty()) {
            return 0;
        }
        
        int result = userTypeMenuMapper.deleteBatchIds(validIds);
        
        log.info("批量删除用户类型菜单关联完成, 删除数量: {}", result);
        return result;
    }

    /**
     * 复制用户类型的菜单权限到另一个用户类型
     * 
     * @param sourceUserTypeId 源用户类型ID
     * @param targetUserTypeId 目标用户类型ID
     * @return 复制成功返回true，失败返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean copyMenusFromUserType(String sourceUserTypeId, String targetUserTypeId) {
        log.info("开始复制用户类型的菜单权限, sourceUserTypeId: {}, targetUserTypeId: {}", 
                sourceUserTypeId, targetUserTypeId);
        
        if (!StringUtils.hasText(sourceUserTypeId) || !StringUtils.hasText(targetUserTypeId)) {
            log.warn("源用户类型ID或目标用户类型ID不能为空, sourceUserTypeId: {}, targetUserTypeId: {}", 
                    sourceUserTypeId, targetUserTypeId);
            return false;
        }
        
        if (Objects.equals(sourceUserTypeId, targetUserTypeId)) {
            log.warn("源用户类型ID和目标用户类型ID不能相同, userTypeId: {}", sourceUserTypeId);
            return false;
        }
        
        // 获取源用户类型的所有菜单ID
        List<String> sourceMenuIds = getMenuIdsByUserType(sourceUserTypeId);
        
        if (sourceMenuIds.isEmpty()) {
            log.info("源用户类型没有关联任何菜单, sourceUserTypeId: {}", sourceUserTypeId);
            // 清空目标用户类型的菜单关联
            removeAllMenusFromUserType(targetUserTypeId);
            return true;
        }
        
        // 为目标用户类型批量分配菜单（会先清空原有关联）
        boolean result = assignMenusToUserType(targetUserTypeId, sourceMenuIds);
        
        log.info("复制用户类型的菜单权限完成, sourceUserTypeId: {}, targetUserTypeId: {}, result: {}", 
                sourceUserTypeId, targetUserTypeId, result ? "成功" : "失败");
        return result;
    }

    /**
     * 统计用户类型的菜单数量
     * 
     * @param userTypeId 用户类型ID
     * @return 菜单数量
     */
    @Override
    public long countMenusByUserType(String userTypeId) {
        if (!StringUtils.hasText(userTypeId)) {
            return 0;
        }
        
        LambdaQueryWrapper<UserTypeMenu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserTypeMenu::getUserTypeId, userTypeId);
        
        return userTypeMenuMapper.selectCount(queryWrapper);
    }

    /**
     * 统计菜单的用户类型数量
     * 
     * @param menuId 菜单ID
     * @return 用户类型数量
     */
    @Override
    public long countUserTypesByMenu(String menuId) {
        if (!StringUtils.hasText(menuId)) {
            return 0;
        }
        
        LambdaQueryWrapper<UserTypeMenu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserTypeMenu::getMenuId, menuId);
        
        return userTypeMenuMapper.selectCount(queryWrapper);
    }
} 