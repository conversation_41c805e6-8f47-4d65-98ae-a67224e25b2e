package com.akey.core.vo;

import com.akey.common.enums.BuiltinEnum;
import com.akey.common.enums.EnableStatusEnum;
import com.akey.common.enums.ExternalLinkEnum;
import com.akey.common.enums.MenuTypeEnum;
import com.akey.common.enums.OpenModeEnum;
import com.akey.common.enums.ShowHideEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 菜单响应视图对象
 * 
 * <p>用于返回菜单的详细信息</p>
 * <p>包含菜单基本信息、路由配置、权限设置、创建更新信息等</p>
 * <p>遵循数据安全原则，返回前端必要的菜单信息</p>
 * 
 * <AUTHOR>
 * @since 2025-07-09
 */
@Data
public class MenuVO {

    /**
     * 菜单ID
     * 
     * <p>菜单的唯一标识符</p>
     */
    private String id;

    /**
     * 父菜单ID
     * 
     * <p>顶级菜单为null</p>
     */
    private String parentId;

    /**
     * 菜单名称
     * 
     * <p>菜单的显示名称</p>
     */
    private String menuName;

    /**
     * 菜单类型
     * 
     * <p>1:目录 2:菜单 3:按钮</p>
     * 
     * @see com.akey.common.enums.MenuTypeEnum
     */
    private MenuTypeEnum menuType;


    /**
     * 菜单编码
     * 
     * <p>唯一标识符，用于权限控制</p>
     */
    private String menuCode;

    /**
     * 路由路径
     * 
     * <p>前端路由地址</p>
     */
    private String routePath;

    /**
     * 组件路径
     * 
     * <p>前端组件文件路径</p>
     */
    private String componentPath;

    /**
     * 图标
     * 
     * <p>菜单显示的图标名称</p>
     */
    private String icon;

    /**
     * 排序号
     * 
     * <p>数值越小越靠前</p>
     */
    private Integer sortOrder;

    /**
     * 是否可见
     * 
     * <p>0:隐藏 1:显示</p>
     * 
     * @see com.akey.common.enums.ShowHideEnum
     */
    private ShowHideEnum visible;


    /**
     * 状态
     * 
     * <p>0:禁用 1:启用</p>
     * 
     * @see com.akey.common.enums.EnableStatusEnum
     */
    private EnableStatusEnum status;


    /**
     * 权限标识
     * 
     * <p>用于权限校验的标识符</p>
     */
    private String permission;

    /**
     * 是否外部链接
     * 
     * <p>0:否 1:是</p>
     * 
     * @see com.akey.common.enums.ExternalLinkEnum
     */
    private ExternalLinkEnum externalLink;


    /**
     * 打开方式
     * 
     * <p>_self:当前窗口 _blank:新窗口</p>
     * 
     * @see com.akey.common.enums.OpenModeEnum
     */
    private OpenModeEnum openMode;


    /**
     * 路由参数
     *
     * <p>JSON格式存储路由参数，用于动态路由传参</p>
     */
    private String routeParams;

    /**
     * 是否内置类型
     *
     * <p>0:是内置,不可删除、修改状态 1:否,可删除</p>
     *
     * @see com.akey.common.enums.BuiltinEnum
     */
    private BuiltinEnum isBuiltin;

    /**
     * 创建时间
     *
     * <p>菜单的创建时间</p>
     */
    private LocalDateTime createTime;

    /**
     * 创建人ID
     * 
     * <p>创建菜单的用户ID</p>
     */
    private String createBy;

    /**
     * 更新时间
     * 
     * <p>菜单的最后更新时间</p>
     */
    private LocalDateTime updateTime;

    /**
     * 更新人ID
     * 
     * <p>最后更新菜单的用户ID</p>
     */
    private String updateBy;

    /**
     * 子菜单列表
     * 
     * <p>树形结构的子节点</p>
     * <p>用于构建菜单树时使用</p>
     */
    private List<MenuVO> children;

    /**
     * 父菜单名称
     * 
     * <p>父菜单的显示名称</p>
     * <p>用于显示菜单的层级关系</p>
     */
    private String parentMenuName;
}
