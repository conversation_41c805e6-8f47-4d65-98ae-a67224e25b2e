package com.akey.core.vo;

import com.akey.common.constant.SystemConstant;
import com.akey.common.entity.BaseEntity;
import com.akey.common.enums.AccountLockStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 用户响应视图对象
 *
 * <p>用于返回用户的详细信息</p>
 * <p>包含用户基本信息、创建更新信息等，不包含敏感信息</p>
 * <p>遵循数据安全原则，不返回密码和谷歌验证KEY等敏感信息</p>
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserVO extends BaseEntity {

    /**
     * 用户名
     *
     * <p>用户的登录名称</p>
     * <p>用于在前端界面显示用户信息</p>
     */
    private String username;

    /**
     * 用户类型ID
     *
     * <p>用户所属的类型标识</p>
     * <p>用于前端根据用户类型显示不同的功能和权限</p>
     */
    private String userTypeId;

    /**
     * 账户锁定状态
     * 
     * <p>用户账户的锁定状态</p>
     * <p>0: 未锁定，1: 已锁定</p>
     * 
     * @see com.akey.common.enums.AccountLockStatusEnum
     */
    private AccountLockStatusEnum accountLocked;

    /**
     * 密码最后修改时间
     * 
     * <p>记录用户最后一次修改密码的时间</p>
     */
    private LocalDateTime passwordUpdateTime;

    /**
     * 最后登录时间
     * 
     * <p>记录用户最后一次成功登录的时间</p>
     */
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     *
     * <p>记录用户最后一次成功登录的IP地址</p>
     */
    private String lastLoginIp;

    /**
     * 是否超级管理员
     *
     * <p>标识该用户是否为超级管理员</p>
     * <p>true: 是超级管理员，拥有系统最高权限</p>
     * <p>false: 非超级管理员，权限受限</p>
     * <p>超级管理员通常具有系统管理、用户管理、权限分配等核心功能权限</p>
     */
    private Boolean isAdmin;

    /**
     * 是否设置谷歌验证KEY
     *
     * <p>标识该用户是否已设置谷歌验证KEY</p>
     * <p>true: 已设置谷歌验证KEY</p>
     * <p>false: 未设置谷歌验证KEY</p>
     */
    private Boolean hasGoogleAuth;

    /**
     * 判断当前用户是否为超级管理员
     *
     * <p>通过用户类型ID与系统常量中的超级管理员类型ID进行比较</p>
     * <p>这是一个便捷方法，用于在前端或业务逻辑中快速判断</p>
     *
     * @return 是超级管理员返回true，否则返回false
     */
    public Boolean getIsAdmin() {
        return Objects.equals(this.userTypeId, SystemConstant.ADMIN_USER_TYPE_ID);
    }

    /**
     * 判断当前用户是否设置了谷歌验证KEY
     *
     * <p>通过检查谷歌验证KEY字段是否有值来判断</p>
     * <p>注意：此方法需要在设置VO时传入googleAuthKey的状态</p>
     *
     * @return 已设置返回true，未设置返回false
     */
    public Boolean getHasGoogleAuth() {
        return this.hasGoogleAuth != null && this.hasGoogleAuth;
    }

    /**
     * 设置是否有谷歌验证KEY
     * 
     * @param googleAuthKey 谷歌验证KEY
     */
    public void setHasGoogleAuthFromKey(String googleAuthKey) {
        this.hasGoogleAuth = StringUtils.hasText(googleAuthKey);
    }
}
