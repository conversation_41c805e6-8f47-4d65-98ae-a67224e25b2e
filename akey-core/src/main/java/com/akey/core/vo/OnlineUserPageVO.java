package com.akey.core.vo;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 在线用户分页响应视图对象
 * 
 * <p>用于返回在线用户的分页查询结果</p>
 * <p>包含分页信息和用户数据列表</p>
 * <p>提供统计信息以便前端展示</p>
 * 
 * <AUTHOR>
 * @since 2025-08-02
 */
@Data
public class OnlineUserPageVO {

    /**
     * 当前页码
     * 
     * <p>当前查询的页码，从1开始</p>
     */
    private Long current;

    /**
     * 每页大小
     * 
     * <p>每页显示的记录数量</p>
     */
    private Long size;

    /**
     * 总记录数
     * 
     * <p>符合查询条件的在线用户总数</p>
     */
    private Long total;

    /**
     * 总页数
     * 
     * <p>根据总记录数和每页大小计算的总页数</p>
     */
    private Long pages;

    /**
     * 在线用户列表
     * 
     * <p>当前页的在线用户数据</p>
     */
    private List<OnlineUserVO> records;

    /**
     * 是否有上一页
     * 
     * <p>判断是否存在上一页</p>
     */
    private Boolean hasPrevious;

    /**
     * 是否有下一页
     * 
     * <p>判断是否存在下一页</p>
     */
    private Boolean hasNext;

    /**
     * 统计信息
     * 
     * <p>在线用户的统计数据</p>
     */
    private OnlineUserStatistics statistics;

    /**
     * 在线用户统计信息
     */
    @Data
    public static class OnlineUserStatistics {

        /**
         * 总在线用户数
         *
         * <p>当前系统中所有在线用户的总数</p>
         */
        private Long totalOnlineUsers;

        /**
         * 按设备类型统计
         *
         * <p>动态统计各种设备类型的在线用户数量</p>
         * <p>Key: 设备类型名称，Value: 该设备类型的在线用户数量</p>
         */
        private Map<String, Long> deviceTypeStatistics;



        /**
         * 多设备登录用户数
         * 
         * <p>同时在多个设备上登录的用户数量</p>
         */
        private Long multiDeviceUsers;

        /**
         * 平均在线时长
         * 
         * <p>所有在线用户的平均在线时长，单位：秒</p>
         */
        private Long averageOnlineDuration;
    }

    /**
     * 构造方法 - 根据分页参数初始化
     * 
     * @param current 当前页码
     * @param size 每页大小
     * @param total 总记录数
     * @param records 数据列表
     */
    public OnlineUserPageVO(Long current, Long size, Long total, List<OnlineUserVO> records) {
        this.current = current;
        this.size = size;
        this.total = total;
        this.records = records;
        
        // 计算总页数
        this.pages = (total + size - 1) / size;
        
        // 计算是否有上一页和下一页
        this.hasPrevious = current > 1;
        this.hasNext = current < pages;
    }

    /**
     * 默认构造方法
     */
    public OnlineUserPageVO() {
    }

    /**
     * 设置统计信息
     * 
     * @param statistics 统计信息对象
     */
    public void setStatistics(OnlineUserStatistics statistics) {
        this.statistics = statistics;
    }

    /**
     * 判断是否为空结果
     * 
     * @return 如果没有数据返回true，否则返回false
     */
    public boolean isEmpty() {
        return records == null || records.isEmpty();
    }

    /**
     * 获取当前页实际记录数
     * 
     * @return 当前页的记录数量
     */
    public int getCurrentPageSize() {
        return records == null ? 0 : records.size();
    }
}
