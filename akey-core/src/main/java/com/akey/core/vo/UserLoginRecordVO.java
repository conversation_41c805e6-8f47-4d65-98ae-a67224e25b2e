package com.akey.core.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户登录记录视图对象
 * 
 * <p>用于返回用户登录记录信息，包含登录时间、设备信息、地理位置等</p>
 * <p>相比完整的LoginLog实体，此VO只包含用户关心的核心字段</p>
 * 
 * <AUTHOR>
 * @since 2025-07-31
 */
@Data
public class UserLoginRecordVO {

    /**
     * 登录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime loginTime;

    /**
     * 登录状态
     * 
     * <p>0-失败，1-成功</p>
     */
    private Integer loginStatus;

    /**
     * 登录状态描述
     */
    private String loginStatusText;

    /**
     * 登录失败原因
     * 
     * <p>登录成功时为空</p>
     */
    private String failureReason;

    /**
     * 客户端IP地址
     */
    private String clientIp;

    /**
     * 地理位置
     * 
     * <p>格式：国家 省份 城市</p>
     */
    private String location;

    /**
     * 设备类型
     * 
     * <p>DESKTOP, MOBILE, TABLET, BOT</p>
     */
    private String deviceType;

    /**
     * 设备品牌
     * 
     * <p>Apple, Samsung, Huawei等</p>
     */
    private String deviceBrand;

    /**
     * 设备型号
     */
    private String deviceModel;

    /**
     * 操作系统名称
     * 
     * <p>Windows, macOS, Android, iOS等</p>
     */
    private String osName;

    /**
     * 操作系统版本
     */
    private String osVersion;

    /**
     * 浏览器名称
     * 
     * <p>Chrome, Firefox, Safari等</p>
     */
    private String browserName;

    /**
     * 浏览器版本
     */
    private String browserVersion;

    /**
     * 风险等级
     * 
     * <p>LOW-低风险，MEDIUM-中风险，HIGH-高风险</p>
     */
    private String riskLevel;

    /**
     * 风险等级描述
     */
    private String riskLevelText;

    /**
     * 是否可疑登录
     * 
     * <p>0-否，1-是</p>
     */
    private Integer isSuspicious;

    /**
     * 可疑原因列表
     * 
     * <p>可疑登录时显示具体原因</p>
     */
    private List<String> suspiciousReasons;

    /**
     * 是否通过代理
     * 
     * <p>0-否，1-是</p>
     */
    private Integer isProxy;

    /**
     * 获取登录状态描述
     * 
     * @return 状态描述
     */
    public String getLoginStatusText() {
        if (loginStatus == null) {
            return "未知";
        }
        return loginStatus == 1 ? "成功" : "失败";
    }

    /**
     * 获取风险等级描述
     * 
     * @return 风险等级描述
     */
    public String getRiskLevelText() {
        if (riskLevel == null) {
            return "未知";
        }
        switch (riskLevel.toUpperCase()) {
            case "LOW":
                return "低风险";
            case "MEDIUM":
                return "中风险";
            case "HIGH":
                return "高风险";
            default:
                return "未知";
        }
    }
}
