package com.akey.core.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户终端信息响应视图对象
 * 
 * <p>用于返回用户登录终端的详细信息</p>
 * <p>包含终端设备类型、Token信息、登录时间等</p>
 * <p>遵循数据安全原则，对敏感信息进行适当脱敏处理</p>
 * 
 * <AUTHOR>
 * @since 2025-08-02
 */
@Data
public class UserTerminalVO {

    /**
     * 登录会话索引值
     * 
     * <p>该账号第几个登录的设备</p>
     * <p>用于标识同一账号下不同终端的登录顺序</p>
     */
    private Integer index;

    /**
     * 设备类型
     * 
     * <p>登录设备的类型标识</p>
     * <p>常见类型：PC、WEB、HD、MOBILE、APP等</p>
     */
    private String deviceType;

    /**
     * Token值
     * 
     * <p>此次登录生成的Token值</p>
     * <p>出于安全考虑，会进行部分脱敏处理</p>
     */
    private String tokenValue;

    /**
     * Token的MD5哈希值
     * 
     * <p>用于前端精确识别和操作特定Token</p>
     * <p>既保护了Token安全，又能唯一标识</p>
     */
    private String tokenMd5;

    /**
     * 登录时间
     * 
     * <p>该终端的登录时间</p>
     * <p>格式：yyyy-MM-dd HH:mm:ss</p>
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime loginTime;

    /**
     * 设备ID
     * 
     * <p>设备的唯一标识</p>
     * <p>用于识别和管理不同的登录设备</p>
     */
    private String deviceId;

    /**
     * 客户端IP地址
     * 
     * <p>该终端登录时的IP地址</p>
     * <p>出于隐私保护，会进行部分脱敏处理</p>
     */
    private String clientIp;

    /**
     * 地理位置信息
     * 
     * <p>根据IP地址解析的地理位置</p>
     * <p>格式：省份-城市</p>
     */
    private String location;

    /**
     * 用户代理信息
     * 
     * <p>浏览器或客户端的User-Agent信息</p>
     * <p>用于识别客户端类型和版本</p>
     */
    private String userAgent;

    /**
     * 是否为当前会话
     * 
     * <p>标识该终端是否为当前操作的会话</p>
     * <p>true: 当前会话</p>
     * <p>false: 其他会话</p>
     */
    private Boolean isCurrent;

    /**
     * Token剩余有效期
     * 
     * <p>该Token的剩余有效时间，单位：秒</p>
     * <p>-1表示永久有效</p>
     */
    private Long tokenTimeout;

    /**
     * 在线时长
     *
     * <p>该终端的在线时长，单位：秒</p>
     * <p>从登录时间到当前时间的时长</p>
     */
    private Long onlineDuration;

    /**
     * 扩展信息
     *
     * <p>登录时设置的自定义扩展参数</p>
     * <p>可包含地理位置、设备信息等额外数据</p>
     */
    private String extraInfo;

    /**
     * 对Token值进行脱敏处理
     * 
     * <p>保留前6位和后4位，中间用*号替代</p>
     * <p>例如：abc123***xyz9</p>
     */
    public void maskTokenValue() {
        if (this.tokenValue != null && this.tokenValue.length() > 10) {
            String prefix = this.tokenValue.substring(0, 6);
            String suffix = this.tokenValue.substring(this.tokenValue.length() - 4);
            this.tokenValue = prefix + "***" + suffix;
        }
    }

    /**
     * 对IP地址进行脱敏处理
     *
     * <p>IPv4地址保留前两段，后两段用*替代</p>
     * <p>例如：192.168.*.*</p>
     */
    public void maskClientIp() {
        if (this.clientIp != null && this.clientIp.contains(".")) {
            String[] parts = this.clientIp.split("\\.");
            if (parts.length == 4) {
                this.clientIp = parts[0] + "." + parts[1] + ".*.*";
            }
        }
    }

    /**
     * 计算在线时长
     *
     * <p>根据登录时间和当前时间计算在线时长</p>
     */
    public void calculateOnlineDuration() {
        if (this.loginTime != null) {
            this.onlineDuration = java.time.Duration.between(this.loginTime, LocalDateTime.now()).getSeconds();
        }
    }
}
