package com.akey.core.vo;

import com.akey.common.enums.EnableStatusEnum;
import com.akey.common.enums.IpWhitelistTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * IP白名单带用户信息VO
 * 
 * <p>用于返回IP白名单信息和关联的用户信息</p>
 * <p>包含用户名等用户相关字段</p>
 * 
 * <AUTHOR>
 * @since 2025-07-31
 */
@Data
public class IpWhitelistWithUserVO {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 白名单类型
     *
     * <p>1-系统白名单，2-接口白名单</p>
     */
    private IpWhitelistTypeEnum whitelistType;

    /**
     * IP地址或IP段
     * 
     * <p>支持单个IP地址和CIDR格式的IP段</p>
     */
    private String ipAddress;

    /**
     * 关联用户ID
     * 
     * <p>用于区分是哪个用户的白名单</p>
     */
    private String userId;

    /**
     * 用户名
     *
     * <p>关联用户的用户名，系统级白名单时为空</p>
     */
    private String username;

    /**
     * 启用状态
     *
     * <p>0-禁用，1-启用</p>
     */
    private EnableStatusEnum enableStatus;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    private String createBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 更新人ID
     */
    private String updateBy;

    /**
     * 判断是否为系统级白名单
     * 
     * @return true:系统级白名单，false:用户级白名单
     */
    public boolean isSystemLevel() {
        return userId == null || userId.trim().isEmpty();
    }

    /**
     * 判断是否为用户级白名单
     * 
     * @return true:用户级白名单，false:系统级白名单
     */
    public boolean isUserLevel() {
        return userId != null && !userId.trim().isEmpty();
    }

    /**
     * 判断是否为系统白名单类型
     * 
     * @return true:系统白名单，false:接口白名单
     */
    public boolean isSystemWhitelist() {
        return whitelistType == IpWhitelistTypeEnum.SYSTEM;
    }

    /**
     * 判断是否为接口白名单类型
     * 
     * @return true:接口白名单，false:系统白名单
     */
    public boolean isApiWhitelist() {
        return whitelistType == IpWhitelistTypeEnum.API;
    }

    /**
     * 判断是否启用
     * 
     * @return true:启用，false:禁用
     */
    public boolean isEnabled() {
        return enableStatus == EnableStatusEnum.ENABLED;
    }
}
