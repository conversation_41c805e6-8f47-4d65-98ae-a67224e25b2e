package com.akey.core.vo;

import com.akey.common.constant.SystemConstant;
import com.akey.common.enums.BuiltinEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 用户类型响应视图对象
 * 
 * <p>用于返回用户类型的详细信息</p>
 * <p>包含用户类型基本信息、创建更新信息等</p>
 * <p>遵循数据安全原则，返回前端必要的用户类型信息</p>
 * 
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
public class UserTypeVO {

    /**
     * 用户类型ID
     * 
     * <p>用户类型的唯一标识符</p>
     */
    private String id;

    /**
     * 类型名称
     * 
     * <p>用户类型的显示名称</p>
     * <p>如：超级管理员、普通管理员、普通用户等</p>
     */
    private String typeName;

    /**
     * 是否内置类型
     * 
     * <p>0: 是内置类型，不可删除</p>
     * <p>1: 否，非内置类型，可删除</p>
     * <p>内置类型通常是系统核心用户类型，不允许用户删除</p>
     * 
     * @see com.akey.common.enums.BuiltinEnum
     */
    private BuiltinEnum isBuiltin;

    /**
     * 创建时间
     * 
     * <p>用户类型的创建时间</p>
     */
    private LocalDateTime createTime;

    /**
     * 创建人ID
     * 
     * <p>创建用户类型的用户ID</p>
     */
    private String createBy;

    /**
     * 更新时间
     * 
     * <p>用户类型的最后更新时间</p>
     */
    private LocalDateTime updateTime;

    /**
     * 更新人ID
     * 
     * <p>最后更新用户类型的用户ID</p>
     */
    private String updateBy;

    /**
     * 关联用户数量
     * 
     * <p>当前使用该用户类型的用户数量</p>
     * <p>用于判断是否可以删除该用户类型</p>
     */
    private Long userCount;

    /**
     * 关联菜单数量
     * 
     * <p>该用户类型分配的菜单权限数量</p>
     * <p>用于显示权限配置情况</p>
     */
    private Long menuCount;

    /**
     * 是否超级管理员类型
     *
     * <p>标识该用户类型是否为超级管理员类型</p>
     * <p>true: 是超级管理员类型，拥有系统最高权限</p>
     * <p>false: 非超级管理员类型，权限受限</p>
     * <p>超级管理员类型通常具有系统管理、用户管理、权限分配等核心功能权限</p>
     */
    private Boolean isAdmin;

    /**
     * 判断当前用户类型是否为超级管理员类型
     *
     * <p>通过用户类型ID与系统常量中的超级管理员类型ID进行比较</p>
     * <p>这是一个便捷方法，用于在前端或业务逻辑中快速判断</p>
     *
     * @return 是超级管理员类型返回true，否则返回false
     */
    public boolean isSuperAdminType() {
        return Objects.equals(this.id, SystemConstant.ADMIN_USER_TYPE_ID);
    }
}
