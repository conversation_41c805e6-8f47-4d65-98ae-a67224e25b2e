package com.akey.core.vo;

import lombok.Data;

/**
 * 谷歌验证器设置响应视图对象
 * 
 * <p>用于返回谷歌验证器设置所需的信息</p>
 * <p>包含密钥、OTP Auth URL等信息，供用户手动设置谷歌验证器</p>
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
public class GoogleAuthSetupVO {

    /**
     * 密钥
     * 
     * <p>Base32编码的谷歌验证器密钥</p>
     * <p>用户可以手动输入此密钥到谷歌验证器应用中</p>
     */
    private String secretKey;



    /**
     * OTP Auth URL
     * 
     * <p>标准的OTP Auth URL</p>
     * <p>包含完整的验证器配置信息</p>
     */
    private String otpAuthUrl;

    /**
     * 账户名称
     * 
     * <p>在谷歌验证器中显示的账户名称</p>
     */
    private String accountName;

    /**
     * 发行者名称
     * 
     * <p>在谷歌验证器中显示的应用名称</p>
     */
    private String issuer;

    /**
     * 验证码剩余时间（秒）
     * 
     * <p>当前验证码还有多少秒过期</p>
     * <p>用于前端显示倒计时</p>
     */
    private Integer remainingTime;
}
