package com.akey.core.vo;

import com.akey.common.enums.BuiltinEnum;
import com.akey.common.enums.EnableStatusEnum;
import com.akey.common.enums.ExternalLinkEnum;
import com.akey.common.enums.MenuTypeEnum;
import com.akey.common.enums.OpenModeEnum;
import com.akey.common.enums.ShowHideEnum;
import lombok.Data;

import java.util.List;

/**
 * 菜单树VO
 * 
 * <p>用于前端展示的菜单树结构，过滤敏感字段</p>
 * <p>符合Element UI Plus等前端组件的树形结构要求</p>
 * 
 * <AUTHOR>
 * @since 2025-07-08
 */
@Data
public class MenuRouterTreeVO {

    /**
     * 菜单ID
     */
    private String id;

    /**
     * 父菜单ID
     */
    private String parentId;

    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 菜单类型
     * 1:目录 2:菜单 3:按钮
     */
    private MenuTypeEnum menuType;

    /**
     * 菜单编码
     * 唯一标识符，用于权限控制
     */
    private String menuCode;

    /**
     * 路由路径
     * 前端路由地址
     */
    private String routePath;

    /**
     * 组件路径
     * 前端组件文件路径
     */
    private String componentPath;

    /**
     * 图标
     * 菜单显示的图标名称
     */
    private String icon;

    /**
     * 排序号
     * 数值越小越靠前
     */
    private Integer sortOrder;

    /**
     * 是否可见
     * 0:隐藏 1:显示
     */
    private ShowHideEnum visible;


    /**
     * 权限标识
     * 用于权限校验的标识符
     */
    private String permission;

    /**
     * 是否外部链接
     * 0:否 1:是
     */
    private ExternalLinkEnum externalLink;

    /**
     * 打开方式
     * _self:当前窗口 _blank:新窗口
     */
    private OpenModeEnum openMode;

    /**
     * 路由参数
     * JSON格式存储路由参数，用于动态路由传参
     *
     * <p>示例格式：</p>
     * <pre>
     * {
     *   "id": "123",
     *   "type": "edit",
     *   "tab": "basic"
     * }
     * </pre>
     */
    private String routeParams;

    /**
     * 是否内置类型
     *
     * <p>0:是内置,不可删除、修改状态 1:否,可删除</p>
     *
     * @see com.akey.common.enums.BuiltinEnum
     */
    private BuiltinEnum isBuiltin;

    /**
     * 子菜单列表
     * 树形结构的子节点
     */
    private List<MenuRouterTreeVO> children;
}
