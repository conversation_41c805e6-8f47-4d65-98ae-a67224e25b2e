package com.akey.core.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 在线用户信息响应视图对象
 * 
 * <p>用于返回在线用户的详细信息</p>
 * <p>包含用户基本信息、登录状态、终端信息等</p>
 * <p>遵循数据安全原则，不返回敏感信息</p>
 * 
 * <AUTHOR>
 * @since 2025-08-02
 */
@Data
public class OnlineUserVO {

    /**
     * 登录ID
     * 
     * <p>用户的登录标识</p>
     * <p>通常为用户ID或用户名</p>
     */
    private String loginId;

    /**
     * 用户名
     * 
     * <p>用户的登录名称</p>
     * <p>用于在界面上显示用户信息</p>
     */
    private String username;

    /**
     * 用户类型ID
     * 
     * <p>用户所属的类型标识</p>
     * <p>用于区分不同类型的用户（如管理员、普通用户等）</p>
     */
    private String userTypeId;

    /**
     * 会话ID
     * 
     * <p>Sa-Token生成的会话标识</p>
     * <p>用于标识用户的登录会话</p>
     */
    private String sessionId;

    /**
     * 最近登录时间
     * 
     * <p>用户最近一次登录的时间</p>
     * <p>格式：yyyy-MM-dd HH:mm:ss</p>
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime loginTime;



    /**
     * 登录设备数量
     * 
     * <p>该用户当前登录的设备总数</p>
     * <p>包括PC、移动端、Web端等所有设备</p>
     */
    private Integer deviceCount;

    /**
     * 主要设备类型
     *
     * <p>用户最近登录的设备类型</p>
     * <p>常见类型：PC、WEB、APP、MOBILE、HD等</p>
     */
    private String primaryDeviceType;



    /**
     * 主要登录IP
     * 
     * <p>用户最近登录的IP地址</p>
     * <p>出于隐私保护，会进行脱敏处理</p>
     */
    private String primaryClientIp;

    /**
     * 会话剩余有效期
     * 
     * <p>用户会话的剩余有效时间，单位：秒</p>
     * <p>-1表示永久有效</p>
     */
    private Long sessionTimeout;



    /**
     * 终端信息列表
     *
     * <p>该用户所有登录终端的详细信息</p>
     * <p>包括设备类型、Token、登录时间等</p>
     */
    private List<UserTerminalVO> terminals;

    /**
     * 在线状态
     * 
     * <p>用户当前的在线状态</p>
     * <p>ONLINE: 在线</p>
     * <p>IDLE: 空闲</p>
     * <p>OFFLINE: 离线</p>
     */
    private OnlineStatus onlineStatus;

    /**
     * 地理位置信息
     * 
     * <p>根据IP地址解析的地理位置</p>
     * <p>格式：省份-城市</p>
     */
    private String location;

    /**
     * 在线时长
     * 
     * <p>用户本次在线的总时长，单位：秒</p>
     */
    private Long onlineDuration;

    /**
     * 在线状态枚举
     */
    public enum OnlineStatus {
        /**
         * 在线状态
         */
        ONLINE,
        
        /**
         * 空闲状态
         */
        IDLE,
        
        /**
         * 离线状态
         */
        OFFLINE
    }

    /**
     * 计算在线时长
     * 
     * <p>根据登录时间和当前时间计算在线时长</p>
     */
    public void calculateOnlineDuration() {
        if (this.loginTime != null) {
            this.onlineDuration = java.time.Duration.between(this.loginTime, LocalDateTime.now()).getSeconds();
        }
    }

    /**
     * 对主要登录IP进行脱敏处理
     */
    public void maskPrimaryClientIp() {
        if (this.primaryClientIp != null && this.primaryClientIp.contains(".")) {
            String[] parts = this.primaryClientIp.split("\\.");
            if (parts.length == 4) {
                this.primaryClientIp = parts[0] + "." + parts[1] + ".*.*";
            }
        }
    }


}
