package com.akey.core.auth;

import cn.dev33.satoken.stp.StpInterface;
import com.akey.core.cache.UserCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Sa-Token 权限认证接口实现
 *
 * <p>实现Sa-Token的StpInterface接口，为Sa-Token提供权限数据源</p>
 * <p>基于Redis缓存优化的用户权限和角色信息获取</p>
 *
 * <p>性能优化特性：</p>
 * <ul>
 *   <li>Redis缓存优先，数据库降级</li>
 *   <li>缓存雪崩防护（随机过期时间）</li>
 *   <li>缓存穿透防护（空值缓存）</li>
 *   <li>异常降级处理</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-07-05
 * @version 3.0 (Redis缓存优化版本)
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StpInterfaceImpl implements StpInterface {

    private final UserCacheService userCacheService;

    /**
     * 返回指定账号id所拥有的权限码集合
     *
     * <p>性能优化：</p>
     * <ul>
     *   <li>优先从Redis缓存获取权限列表</li>
     *   <li>缓存未命中时查询数据库并缓存结果</li>
     *   <li>异常时降级到数据库查询</li>
     * </ul>
     *
     * @param loginId   账号id
     * @param loginType 账号类型
     * @return 权限码集合
     */
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        log.debug("获取用户权限列表, loginId: {}, loginType: {}", loginId, loginType);

        try {
            String userId = String.valueOf(loginId);

            // 优先从缓存获取用户权限列表
            List<String> permissions = userCacheService.getUserPermissions(userId);

            log.debug("用户权限列表获取成功, loginId: {}, permissions count: {}", loginId, permissions.size());
            return permissions;

        } catch (Exception e) {
            log.error("获取用户权限列表失败, loginId: {}, error: {}", loginId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 返回指定账号id所拥有的角色标识集合
     *
     * <p>性能优化：</p>
     * <ul>
     *   <li>优先从Redis缓存获取角色列表</li>
     *   <li>缓存未命中时查询数据库并缓存结果</li>
     *   <li>异常时降级到数据库查询</li>
     * </ul>
     *
     * @param loginId   账号id
     * @param loginType 账号类型
     * @return 角色标识集合
     */
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        log.debug("获取用户角色列表, loginId: {}, loginType: {}", loginId, loginType);

        try {
            String userId = String.valueOf(loginId);

            // 优先从缓存获取用户角色列表
            List<String> roles = userCacheService.getUserRoles(userId);

            log.debug("用户角色列表获取成功, loginId: {}, roles count: {}", loginId, roles.size());
            return roles;

        } catch (Exception e) {
            log.error("获取用户角色列表失败, loginId: {}, error: {}", loginId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }
}
