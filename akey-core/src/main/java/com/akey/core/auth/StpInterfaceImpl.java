package com.akey.core.auth;

import cn.dev33.satoken.stp.StpInterface;
import com.akey.common.constant.SystemConstant;
import com.akey.core.dao.entity.User;
import com.akey.core.dao.entity.UserType;
import com.akey.core.dao.service.MenuService;
import com.akey.core.dao.service.UserService;
import com.akey.core.dao.service.UserTypeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Sa-Token 权限认证接口实现
 *
 * <p>实现Sa-Token的StpInterface接口，为Sa-Token提供权限数据源</p>
 * <p>基于现有的用户、菜单、用户类型实体提供权限和角色信息</p>
 * <AUTHOR>
 * @since 2025-07-05
 * @version 2.0 (性能优化版本)
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StpInterfaceImpl implements StpInterface {

    private final UserService userService;
    private final MenuService menuService;
    private final UserTypeService userTypeService;

    /**
     * 返回指定账号id所拥有的权限码集合
     * @param loginId   账号id
     * @param loginType 账号类型
     * @return 权限码集合
     */
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        log.debug("获取用户权限列表, loginId: {}, loginType: {}", loginId, loginType);

        try {
            // 根据用户ID获取用户信息
            User user = userService.getUserById(String.valueOf(loginId));
            if (user == null) {
                log.warn("用户不存在, loginId: {}", loginId);
                return new ArrayList<>();
            }

            // 获取用户类型
            String userTypeId = user.getUserTypeId();

            // 获取权限标识列表
            List<String> permissions;

            // 如果是超级管理员用户类型，则拥有所有权限
            if (SystemConstant.ADMIN_USER_TYPE_ID.equals(userTypeId)) {
                // 使用优化方法：只查询权限字段，直接返回权限列表
                permissions = menuService.getAllEnabledMenuPermissions();
            } else {
                // 使用优化方法：只查询权限字段，直接返回权限列表
                permissions = menuService.getMenuPermissionsByUserTypeId(userTypeId);
            }

            log.debug("用户权限列表获取成功, loginId: {}, permissions: {}", loginId, permissions);
            return permissions;
        } catch (Exception e) {
            log.error("获取用户权限列表失败, loginId: {}, error: {}", loginId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 返回指定账号id所拥有的角色标识集合
     * 
     * @param loginId   账号id
     * @param loginType 账号类型
     * @return 角色标识集合
     */
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        log.debug("获取用户角色列表, loginId: {}, loginType: {}", loginId, loginType);
        
        try {
            // 根据用户ID获取用户信息
            User user = userService.getUserById(String.valueOf(loginId));
            if (user == null) {
                log.warn("用户不存在, loginId: {}", loginId);
                return new ArrayList<>();
            }

            // 根据用户类型ID获取用户类型信息
            UserType userType = userTypeService.getUserTypeById(user.getUserTypeId());
            if (userType == null) {
                log.warn("用户类型不存在, userTypeId: {}", user.getUserTypeId());
                return new ArrayList<>();
            }

            // 将用户类型ID作为角色标识
            List<String> roles = new ArrayList<>();
            roles.add(userType.getId());
            
            log.debug("用户角色列表获取成功, loginId: {}, roles: {}", loginId, roles);
            return roles;
            
        } catch (Exception e) {
            log.error("获取用户角色列表失败, loginId: {}, error: {}", loginId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }
}
