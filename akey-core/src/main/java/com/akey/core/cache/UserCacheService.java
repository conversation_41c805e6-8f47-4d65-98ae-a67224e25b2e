package com.akey.core.cache;

import com.akey.common.constant.SystemConstant;
import com.akey.core.dao.entity.User;
import com.akey.core.dao.entity.UserType;
import com.akey.core.dao.service.MenuService;
import com.akey.core.dao.service.UserService;
import com.akey.core.dao.service.UserTypeService;
import com.akey.framework.core.redis.util.RedisUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 用户缓存服务
 * 
 * <p>功能说明：</p>
 * <ul>
 *   <li>提供用户信息的缓存操作</li>
 *   <li>提供用户权限和角色的缓存操作</li>
 *   <li>实现缓存降级机制</li>
 *   <li>支持主动缓存清除</li>
 * </ul>
 * 
 * <p>缓存策略：</p>
 * <ul>
 *   <li>用户基本信息：30分钟 + 随机时间</li>
 *   <li>用户权限列表：15分钟 + 随机时间</li>
 *   <li>用户角色列表：15分钟 + 随机时间</li>
 *   <li>用户类型信息：1小时 + 随机时间</li>
 *   <li>全局权限列表：1小时 + 随机时间</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-08-03
 * @version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserCacheService {

    private final RedisUtil redisUtil;
    private final UserService userService;
    private final MenuService menuService;
    private final UserTypeService userTypeService;

    // 缓存Key前缀
    private static final String CACHE_PREFIX = "akey:cache:";
    private static final String USER_KEY_PREFIX = CACHE_PREFIX + "user:";
    private static final String USER_PERMISSIONS_KEY_PREFIX = CACHE_PREFIX + "user:permissions:";
    private static final String USER_ROLES_KEY_PREFIX = CACHE_PREFIX + "user:roles:";
    private static final String USER_TYPE_KEY_PREFIX = CACHE_PREFIX + "usertype:";
    private static final String ALL_PERMISSIONS_KEY = CACHE_PREFIX + "permissions:all";
    private static final String USER_TYPE_PERMISSIONS_KEY_PREFIX = CACHE_PREFIX + "permissions:usertype:";

    // 缓存过期时间（秒）
    private static final long USER_CACHE_EXPIRE = 30 * 60; // 30分钟
    private static final long PERMISSIONS_CACHE_EXPIRE = 15 * 60; // 15分钟
    private static final long ROLES_CACHE_EXPIRE = 15 * 60; // 15分钟
    private static final long USER_TYPE_CACHE_EXPIRE = 60 * 60; // 1小时
    private static final long ALL_PERMISSIONS_CACHE_EXPIRE = 60 * 60; // 1小时
    private static final long NULL_CACHE_EXPIRE = 5 * 60; // 5分钟（空值缓存）

    /**
     * 获取用户信息（优先从缓存获取）
     * 
     * @param userId 用户ID
     * @return 用户信息，不存在返回null
     */
    public User getUserById(String userId) {
        if (!StringUtils.hasText(userId)) {
            return null;
        }

        String cacheKey = USER_KEY_PREFIX + userId;
        
        try {
            // 先从缓存获取
            User cachedUser = redisUtil.get(cacheKey, User.class);
            if (cachedUser != null) {
                log.debug("从缓存获取用户信息成功, userId: {}", userId);
                return cachedUser;
            }

            // 缓存未命中，从数据库查询
            User user = userService.getUserById(userId);
            
            // 缓存查询结果（包括null值）
            long expireTime = user != null ? 
                USER_CACHE_EXPIRE + getRandomSeconds(300) : NULL_CACHE_EXPIRE;
            redisUtil.set(cacheKey, user, expireTime);
            
            log.debug("从数据库获取用户信息并缓存, userId: {}, found: {}", userId, user != null);
            return user;
            
        } catch (Exception e) {
            log.error("获取用户信息缓存异常，降级到数据库查询, userId: {}", userId, e);
            return userService.getUserById(userId);
        }
    }

    /**
     * 获取用户权限列表（优先从缓存获取）
     * 
     * @param userId 用户ID
     * @return 权限列表
     */
    @SuppressWarnings("unchecked")
    public List<String> getUserPermissions(String userId) {
        if (!StringUtils.hasText(userId)) {
            return new ArrayList<>();
        }

        String cacheKey = USER_PERMISSIONS_KEY_PREFIX + userId;
        
        try {
            // 先从缓存获取
            List<String> cachedPermissions = redisUtil.get(cacheKey, List.class);
            if (cachedPermissions != null) {
                log.debug("从缓存获取用户权限列表成功, userId: {}, count: {}", userId, cachedPermissions.size());
                return cachedPermissions;
            }

            // 缓存未命中，从数据库查询
            List<String> permissions = getUserPermissionsFromDatabase(userId);
            
            // 缓存查询结果
            long expireTime = PERMISSIONS_CACHE_EXPIRE + getRandomSeconds(300);
            redisUtil.set(cacheKey, permissions, expireTime);
            
            log.debug("从数据库获取用户权限列表并缓存, userId: {}, count: {}", userId, permissions.size());
            return permissions;
            
        } catch (Exception e) {
            log.error("获取用户权限列表缓存异常，降级到数据库查询, userId: {}", userId, e);
            return getUserPermissionsFromDatabase(userId);
        }
    }

    /**
     * 获取用户角色列表（优先从缓存获取）
     * 
     * @param userId 用户ID
     * @return 角色列表
     */
    @SuppressWarnings("unchecked")
    public List<String> getUserRoles(String userId) {
        if (!StringUtils.hasText(userId)) {
            return new ArrayList<>();
        }

        String cacheKey = USER_ROLES_KEY_PREFIX + userId;
        
        try {
            // 先从缓存获取
            List<String> cachedRoles = redisUtil.get(cacheKey, List.class);
            if (cachedRoles != null) {
                log.debug("从缓存获取用户角色列表成功, userId: {}, count: {}", userId, cachedRoles.size());
                return cachedRoles;
            }

            // 缓存未命中，从数据库查询
            List<String> roles = getUserRolesFromDatabase(userId);
            
            // 缓存查询结果
            long expireTime = ROLES_CACHE_EXPIRE + getRandomSeconds(300);
            redisUtil.set(cacheKey, roles, expireTime);
            
            log.debug("从数据库获取用户角色列表并缓存, userId: {}, count: {}", userId, roles.size());
            return roles;
            
        } catch (Exception e) {
            log.error("获取用户角色列表缓存异常，降级到数据库查询, userId: {}", userId, e);
            return getUserRolesFromDatabase(userId);
        }
    }

    /**
     * 获取用户类型信息（优先从缓存获取）
     * 
     * @param userTypeId 用户类型ID
     * @return 用户类型信息，不存在返回null
     */
    public UserType getUserTypeById(String userTypeId) {
        if (!StringUtils.hasText(userTypeId)) {
            return null;
        }

        String cacheKey = USER_TYPE_KEY_PREFIX + userTypeId;
        
        try {
            // 先从缓存获取
            UserType cachedUserType = redisUtil.get(cacheKey, UserType.class);
            if (cachedUserType != null) {
                log.debug("从缓存获取用户类型信息成功, userTypeId: {}", userTypeId);
                return cachedUserType;
            }

            // 缓存未命中，从数据库查询
            UserType userType = userTypeService.getUserTypeById(userTypeId);
            
            // 缓存查询结果（包括null值）
            long expireTime = userType != null ? 
                USER_TYPE_CACHE_EXPIRE + getRandomSeconds(600) : NULL_CACHE_EXPIRE;
            redisUtil.set(cacheKey, userType, expireTime);
            
            log.debug("从数据库获取用户类型信息并缓存, userTypeId: {}, found: {}", userTypeId, userType != null);
            return userType;
            
        } catch (Exception e) {
            log.error("获取用户类型信息缓存异常，降级到数据库查询, userTypeId: {}", userTypeId, e);
            return userTypeService.getUserTypeById(userTypeId);
        }
    }

    /**
     * 获取所有启用的权限列表（优先从缓存获取）
     * 
     * @return 权限列表
     */
    @SuppressWarnings("unchecked")
    public List<String> getAllEnabledPermissions() {
        try {
            // 先从缓存获取
            List<String> cachedPermissions = redisUtil.get(ALL_PERMISSIONS_KEY, List.class);
            if (cachedPermissions != null) {
                log.debug("从缓存获取全局权限列表成功, count: {}", cachedPermissions.size());
                return cachedPermissions;
            }

            // 缓存未命中，从数据库查询
            List<String> permissions = menuService.getAllEnabledMenuPermissions();
            
            // 缓存查询结果
            long expireTime = ALL_PERMISSIONS_CACHE_EXPIRE + getRandomSeconds(600);
            redisUtil.set(ALL_PERMISSIONS_KEY, permissions, expireTime);
            
            log.debug("从数据库获取全局权限列表并缓存, count: {}", permissions.size());
            return permissions;
            
        } catch (Exception e) {
            log.error("获取全局权限列表缓存异常，降级到数据库查询", e);
            return menuService.getAllEnabledMenuPermissions();
        }
    }

    /**
     * 获取用户类型权限列表（优先从缓存获取）
     * 
     * @param userTypeId 用户类型ID
     * @return 权限列表
     */
    @SuppressWarnings("unchecked")
    public List<String> getUserTypePermissions(String userTypeId) {
        if (!StringUtils.hasText(userTypeId)) {
            return new ArrayList<>();
        }

        String cacheKey = USER_TYPE_PERMISSIONS_KEY_PREFIX + userTypeId;
        
        try {
            // 先从缓存获取
            List<String> cachedPermissions = redisUtil.get(cacheKey, List.class);
            if (cachedPermissions != null) {
                log.debug("从缓存获取用户类型权限列表成功, userTypeId: {}, count: {}", userTypeId, cachedPermissions.size());
                return cachedPermissions;
            }

            // 缓存未命中，从数据库查询
            List<String> permissions = menuService.getMenuPermissionsByUserTypeId(userTypeId);
            
            // 缓存查询结果
            long expireTime = ALL_PERMISSIONS_CACHE_EXPIRE + getRandomSeconds(600);
            redisUtil.set(cacheKey, permissions, expireTime);
            
            log.debug("从数据库获取用户类型权限列表并缓存, userTypeId: {}, count: {}", userTypeId, permissions.size());
            return permissions;
            
        } catch (Exception e) {
            log.error("获取用户类型权限列表缓存异常，降级到数据库查询, userTypeId: {}", userTypeId, e);
            return menuService.getMenuPermissionsByUserTypeId(userTypeId);
        }
    }

    /**
     * 清除用户相关的所有缓存
     * 
     * @param userId 用户ID
     */
    public void clearUserCache(String userId) {
        if (!StringUtils.hasText(userId)) {
            return;
        }

        try {
            String userKey = USER_KEY_PREFIX + userId;
            String permissionsKey = USER_PERMISSIONS_KEY_PREFIX + userId;
            String rolesKey = USER_ROLES_KEY_PREFIX + userId;
            
            redisUtil.del(userKey);
            redisUtil.del(permissionsKey);
            redisUtil.del(rolesKey);
            
            log.info("清除用户缓存成功, userId: {}", userId);
        } catch (Exception e) {
            log.error("清除用户缓存失败, userId: {}", userId, e);
        }
    }

    /**
     * 清除用户权限缓存
     * 
     * @param userId 用户ID
     */
    public void clearUserPermissionsCache(String userId) {
        if (!StringUtils.hasText(userId)) {
            return;
        }

        try {
            String permissionsKey = USER_PERMISSIONS_KEY_PREFIX + userId;
            redisUtil.del(permissionsKey);
            
            log.info("清除用户权限缓存成功, userId: {}", userId);
        } catch (Exception e) {
            log.error("清除用户权限缓存失败, userId: {}", userId, e);
        }
    }

    /**
     * 清除用户角色缓存
     *
     * @param userId 用户ID
     */
    public void clearUserRolesCache(String userId) {
        if (!StringUtils.hasText(userId)) {
            return;
        }

        try {
            String rolesKey = USER_ROLES_KEY_PREFIX + userId;
            redisUtil.del(rolesKey);

            log.info("清除用户角色缓存成功, userId: {}", userId);
        } catch (Exception e) {
            log.error("清除用户角色缓存失败, userId: {}", userId, e);
        }
    }

    /**
     * 清除用户类型缓存
     *
     * @param userTypeId 用户类型ID
     */
    public void clearUserTypeCache(String userTypeId) {
        if (!StringUtils.hasText(userTypeId)) {
            return;
        }

        try {
            String userTypeKey = USER_TYPE_KEY_PREFIX + userTypeId;
            String userTypePermissionsKey = USER_TYPE_PERMISSIONS_KEY_PREFIX + userTypeId;

            redisUtil.del(userTypeKey);
            redisUtil.del(userTypePermissionsKey);

            log.info("清除用户类型缓存成功, userTypeId: {}", userTypeId);
        } catch (Exception e) {
            log.error("清除用户类型缓存失败, userTypeId: {}", userTypeId, e);
        }
    }

    /**
     * 清除全局权限缓存
     */
    public void clearAllPermissionsCache() {
        try {
            redisUtil.del(ALL_PERMISSIONS_KEY);
            log.info("清除全局权限缓存成功");
        } catch (Exception e) {
            log.error("清除全局权限缓存失败", e);
        }
    }

    /**
     * 清除所有用户类型权限缓存
     */
    public void clearAllUserTypePermissionsCache() {
        try {
            // 使用通配符删除所有用户类型权限缓存
            String pattern = USER_TYPE_PERMISSIONS_KEY_PREFIX + "*";
            redisUtil.deleteByPattern(pattern);

            log.info("清除所有用户类型权限缓存成功");
        } catch (Exception e) {
            log.error("清除所有用户类型权限缓存失败", e);
        }
    }

    /**
     * 清除所有用户权限和角色缓存
     */
    public void clearAllUserPermissionsAndRolesCache() {
        try {
            // 使用通配符删除所有用户权限和角色缓存
            String permissionsPattern = USER_PERMISSIONS_KEY_PREFIX + "*";
            String rolesPattern = USER_ROLES_KEY_PREFIX + "*";

            redisUtil.deleteByPattern(permissionsPattern);
            redisUtil.deleteByPattern(rolesPattern);

            log.info("清除所有用户权限和角色缓存成功");
        } catch (Exception e) {
            log.error("清除所有用户权限和角色缓存失败", e);
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 从数据库获取用户权限列表
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    private List<String> getUserPermissionsFromDatabase(String userId) {
        try {
            // 获取用户信息
            User user = userService.getUserById(userId);
            if (user == null) {
                log.warn("用户不存在, userId: {}", userId);
                return new ArrayList<>();
            }

            // 获取用户类型
            String userTypeId = user.getUserTypeId();

            // 如果是超级管理员用户类型，则拥有所有权限
            if (SystemConstant.ADMIN_USER_TYPE_ID.equals(userTypeId)) {
                return menuService.getAllEnabledMenuPermissions();
            } else {
                return menuService.getMenuPermissionsByUserTypeId(userTypeId);
            }
        } catch (Exception e) {
            log.error("从数据库获取用户权限列表失败, userId: {}", userId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 从数据库获取用户角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    private List<String> getUserRolesFromDatabase(String userId) {
        try {
            // 获取用户信息
            User user = userService.getUserById(userId);
            if (user == null) {
                log.warn("用户不存在, userId: {}", userId);
                return new ArrayList<>();
            }

            // 根据用户类型ID获取用户类型信息
            UserType userType = userTypeService.getUserTypeById(user.getUserTypeId());
            if (userType == null) {
                log.warn("用户类型不存在, userTypeId: {}", user.getUserTypeId());
                return new ArrayList<>();
            }

            // 将用户类型ID作为角色标识
            List<String> roles = new ArrayList<>();
            roles.add(userType.getId());

            return roles;
        } catch (Exception e) {
            log.error("从数据库获取用户角色列表失败, userId: {}", userId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取随机秒数，用于避免缓存雪崩
     *
     * @param maxSeconds 最大随机秒数
     * @return 随机秒数
     */
    private int getRandomSeconds(int maxSeconds) {
        return ThreadLocalRandom.current().nextInt(0, maxSeconds);
    }
}
