package com.akey.core.cache;

import com.akey.framework.core.redis.util.RedisUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 缓存监控服务
 * 
 * <p>功能说明：</p>
 * <ul>
 *   <li>提供缓存统计信息</li>
 *   <li>监控缓存命中率</li>
 *   <li>提供缓存清理功能</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-08-03
 * @version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CacheMonitorService {

    private final RedisUtil redisUtil;

    // 缓存Key前缀
    private static final String CACHE_PREFIX = "akey:cache:";
    private static final String USER_KEY_PREFIX = CACHE_PREFIX + "user:";
    private static final String USER_PERMISSIONS_KEY_PREFIX = CACHE_PREFIX + "user:permissions:";
    private static final String USER_ROLES_KEY_PREFIX = CACHE_PREFIX + "user:roles:";
    private static final String USER_TYPE_KEY_PREFIX = CACHE_PREFIX + "usertype:";
    private static final String ALL_PERMISSIONS_KEY = CACHE_PREFIX + "permissions:all";
    private static final String USER_TYPE_PERMISSIONS_KEY_PREFIX = CACHE_PREFIX + "permissions:usertype:";

    /**
     * 获取缓存统计信息
     * 
     * @return 缓存统计信息
     */
    public Map<String, Object> getCacheStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        try {
            // 统计各类缓存的数量
            statistics.put("userCacheCount", getCacheCount(USER_KEY_PREFIX + "*"));
            statistics.put("userPermissionsCacheCount", getCacheCount(USER_PERMISSIONS_KEY_PREFIX + "*"));
            statistics.put("userRolesCacheCount", getCacheCount(USER_ROLES_KEY_PREFIX + "*"));
            statistics.put("userTypeCacheCount", getCacheCount(USER_TYPE_KEY_PREFIX + "*"));
            statistics.put("userTypePermissionsCacheCount", getCacheCount(USER_TYPE_PERMISSIONS_KEY_PREFIX + "*"));
            
            // 检查全局权限缓存是否存在
            statistics.put("allPermissionsCacheExists", redisUtil.hasKey(ALL_PERMISSIONS_KEY));
            
            // 总缓存数量
            statistics.put("totalCacheCount", getCacheCount(CACHE_PREFIX + "*"));
            
            log.debug("获取缓存统计信息成功: {}", statistics);
            
        } catch (Exception e) {
            log.error("获取缓存统计信息失败", e);
            statistics.put("error", "获取缓存统计信息失败: " + e.getMessage());
        }
        
        return statistics;
    }

    /**
     * 清除所有用户相关缓存
     * 
     * @return 清除的缓存数量
     */
    public long clearAllUserCache() {
        try {
            long count = 0;
            
            // 清除用户基本信息缓存
            count += deleteByPattern(USER_KEY_PREFIX + "*");
            
            // 清除用户权限缓存
            count += deleteByPattern(USER_PERMISSIONS_KEY_PREFIX + "*");
            
            // 清除用户角色缓存
            count += deleteByPattern(USER_ROLES_KEY_PREFIX + "*");
            
            log.info("清除所有用户相关缓存成功, 清除数量: {}", count);
            return count;
            
        } catch (Exception e) {
            log.error("清除所有用户相关缓存失败", e);
            return 0;
        }
    }

    /**
     * 清除所有权限相关缓存
     * 
     * @return 清除的缓存数量
     */
    public long clearAllPermissionsCache() {
        try {
            long count = 0;
            
            // 清除全局权限缓存
            if (redisUtil.hasKey(ALL_PERMISSIONS_KEY)) {
                redisUtil.del(ALL_PERMISSIONS_KEY);
                count++;
            }
            
            // 清除用户类型权限缓存
            count += deleteByPattern(USER_TYPE_PERMISSIONS_KEY_PREFIX + "*");
            
            // 清除用户权限和角色缓存
            count += deleteByPattern(USER_PERMISSIONS_KEY_PREFIX + "*");
            count += deleteByPattern(USER_ROLES_KEY_PREFIX + "*");
            
            log.info("清除所有权限相关缓存成功, 清除数量: {}", count);
            return count;
            
        } catch (Exception e) {
            log.error("清除所有权限相关缓存失败", e);
            return 0;
        }
    }

    /**
     * 清除所有缓存
     * 
     * @return 清除的缓存数量
     */
    public long clearAllCache() {
        try {
            long count = deleteByPattern(CACHE_PREFIX + "*");
            log.info("清除所有缓存成功, 清除数量: {}", count);
            return count;
            
        } catch (Exception e) {
            log.error("清除所有缓存失败", e);
            return 0;
        }
    }

    /**
     * 获取指定模式的缓存Key列表
     * 
     * @param pattern 匹配模式
     * @return 缓存Key列表
     */
    public Set<String> getCacheKeys(String pattern) {
        try {
            return redisUtil.keys(pattern);
        } catch (Exception e) {
            log.error("获取缓存Key列表失败, pattern: {}", pattern, e);
            return Set.of();
        }
    }

    /**
     * 检查缓存Key是否存在
     * 
     * @param key 缓存Key
     * @return 存在返回true，不存在返回false
     */
    public boolean hasKey(String key) {
        try {
            return redisUtil.hasKey(key);
        } catch (Exception e) {
            log.error("检查缓存Key是否存在失败, key: {}", key, e);
            return false;
        }
    }

    /**
     * 获取缓存Key的过期时间
     * 
     * @param key 缓存Key
     * @return 过期时间（秒），-1表示永不过期，-2表示Key不存在
     */
    public long getExpire(String key) {
        try {
            return redisUtil.getExpire(key);
        } catch (Exception e) {
            log.error("获取缓存Key过期时间失败, key: {}", key, e);
            return -2;
        }
    }

    /**
     * 预热缓存
     * 
     * <p>预加载一些常用的缓存数据</p>
     */
    public void warmUpCache() {
        log.info("开始预热缓存...");
        
        try {
            // 这里可以添加预热逻辑，比如预加载全局权限列表
            // 由于我们的缓存是懒加载的，这里暂时不做具体实现
            
            log.info("缓存预热完成");
            
        } catch (Exception e) {
            log.error("缓存预热失败", e);
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 获取指定模式的缓存数量
     * 
     * @param pattern 匹配模式
     * @return 缓存数量
     */
    private long getCacheCount(String pattern) {
        try {
            Set<String> keys = redisUtil.keys(pattern);
            return keys != null ? keys.size() : 0;
        } catch (Exception e) {
            log.error("获取缓存数量失败, pattern: {}", pattern, e);
            return 0;
        }
    }

    /**
     * 按模式删除缓存
     * 
     * @param pattern 匹配模式
     * @return 删除的数量
     */
    private long deleteByPattern(String pattern) {
        try {
            return redisUtil.deleteByPattern(pattern);
        } catch (Exception e) {
            log.error("按模式删除缓存失败, pattern: {}", pattern, e);
            return 0;
        }
    }
}
