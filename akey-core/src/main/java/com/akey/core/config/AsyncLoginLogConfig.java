package com.akey.core.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步登录日志配置
 * 
 * <p>配置专门用于登录日志记录的异步线程池</p>
 * <p>确保登录日志记录不会影响主业务流程的性能</p>
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Configuration
@EnableAsync
public class AsyncLoginLogConfig {

    /**
     * 登录日志专用线程池
     * 
     * <p>配置说明：</p>
     * <ul>
     *   <li>核心线程数：2个，保证基本的并发处理能力</li>
     *   <li>最大线程数：5个，应对高峰期的登录量</li>
     *   <li>队列容量：100个，缓冲突发的登录请求</li>
     *   <li>线程存活时间：60秒，及时回收空闲线程</li>
     *   <li>拒绝策略：CallerRunsPolicy，确保日志不丢失</li>
     * </ul>
     * 
     * @return 登录日志线程池执行器
     */
    @Bean("loginLogExecutor")
    public Executor loginLogExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数
        executor.setCorePoolSize(2);
        
        // 最大线程数
        executor.setMaxPoolSize(5);
        
        // 队列容量
        executor.setQueueCapacity(100);
        
        // 线程存活时间（秒）
        executor.setKeepAliveSeconds(60);
        
        // 线程名前缀
        executor.setThreadNamePrefix("LoginLog-");
        
        // 拒绝策略：由调用线程执行，确保任务不丢失
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待所有任务完成后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间（秒）
        executor.setAwaitTerminationSeconds(30);
        
        // 初始化线程池
        executor.initialize();
        
        log.info("登录日志异步线程池初始化完成 - 核心线程数: {}, 最大线程数: {}, 队列容量: {}", 
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
        
        return executor;
    }

    /**
     * 异步异常处理器
     * 
     * <p>处理异步方法中未捕获的异常</p>
     */
    @Bean
    public org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler asyncUncaughtExceptionHandler() {
        return (throwable, method, objects) -> {
            log.error("异步登录日志记录发生未捕获异常 - 方法: {}, 参数: {}, 异常: {}", 
                     method.getName(), objects, throwable.getMessage(), throwable);
        };
    }
}
