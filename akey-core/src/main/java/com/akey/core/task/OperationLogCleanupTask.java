package com.akey.core.task;

import com.akey.core.dao.service.OperationLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 操作日志清理定时任务
 * 
 * <p>每天凌晨1点执行，清理超过3天的操作日志记录</p>
 * <p>可通过配置项 akey.task.operation-log-cleanup.enabled 控制是否启用</p>
 * 
 * <AUTHOR>
 * @since 2025-07-31
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(
    prefix = "akey.task.operation-log-cleanup", 
    name = "enabled", 
    havingValue = "true", 
    matchIfMissing = true
)
public class OperationLogCleanupTask {

    private final OperationLogService operationLogService;

    /**
     * 保留天数（从配置文件读取）
     */
    @Value("${akey.task.operation-log-cleanup.retain-days:3}")
    private int retainDays;

    /**
     * 清理过期操作日志
     *
     * <p>根据配置的cron表达式执行，清理超过指定天数的操作日志</p>
     */
    @Scheduled(cron = "${akey.task.operation-log-cleanup.cron:0 0 1 * * ?}")
    public void cleanupExpiredOperationLogs() {
        log.info("开始执行操作日志清理任务，保留天数: {}", retainDays);

        try {
            // 计算清理时间点
            LocalDateTime beforeTime = LocalDateTime.now().minusDays(retainDays);

            log.info("清理时间点: {}, 将清理此时间之前的所有操作日志", beforeTime);

            // 执行清理
            Long deletedCount = operationLogService.cleanupExpiredLogs(beforeTime);

            if (deletedCount > 0) {
                log.info("操作日志清理任务完成，共清理 {} 条记录", deletedCount);
            } else {
                log.info("操作日志清理任务完成，没有需要清理的记录");
            }

        } catch (Exception e) {
            log.error("操作日志清理任务执行失败", e);
        }
    }

    /**
     * 手动触发清理任务（用于测试）
     * 
     * <p>注意：此方法仅用于开发和测试环境，生产环境请通过定时任务自动执行</p>
     */
    public void manualCleanup() {
        log.warn("手动触发操作日志清理任务");
        cleanupExpiredOperationLogs();
    }

    /**
     * 获取下次执行时间信息
     * 
     * @return 任务配置信息
     */
    public String getTaskInfo() {
        return String.format("操作日志清理任务 - 保留天数: %d天, 执行时间: 每天凌晨1点", retainDays);
    }
}
