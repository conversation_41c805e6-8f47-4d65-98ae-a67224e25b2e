package com.akey.core.task;

import com.akey.core.dao.service.LoginLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 登录日志清理定时任务
 * 
 * <p>每天凌晨0点执行，清理超过3天的登录日志记录</p>
 * <p>可通过配置项 akey.task.login-log-cleanup.enabled 控制是否启用</p>
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(
    prefix = "akey.task.login-log-cleanup", 
    name = "enabled", 
    havingValue = "true", 
    matchIfMissing = true
)
public class LoginLogCleanupTask {

    private final LoginLogService loginLogService;

    /**
     * 保留天数（从配置文件读取）
     */
    @Value("${akey.task.login-log-cleanup.retain-days:3}")
    private int retainDays;

    /**
     * 清理过期登录日志
     *
     * <p>根据配置的cron表达式执行，清理超过指定天数的登录日志</p>
     */
    @Scheduled(cron = "${akey.task.login-log-cleanup.cron:0 0 0 * * ?}")
    public void cleanupExpiredLoginLogs() {
        log.info("开始执行登录日志清理任务，保留天数: {}", retainDays);

        try {
            // 计算清理时间点
            LocalDateTime beforeTime = LocalDateTime.now().minusDays(retainDays);

            log.info("清理时间点: {}, 将清理此时间之前的所有登录日志", beforeTime);

            // 执行清理
            Long deletedCount = loginLogService.cleanupExpiredLogs(beforeTime);

            if (deletedCount > 0) {
                log.info("登录日志清理任务完成，共清理 {} 条记录", deletedCount);
            } else {
                log.info("登录日志清理任务完成，没有需要清理的记录");
            }

        } catch (Exception e) {
            log.error("登录日志清理任务执行失败", e);
        }
    }
}
