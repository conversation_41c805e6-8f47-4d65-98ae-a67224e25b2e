package com.akey.core.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 重置用户密码请求数据传输对象
 * 
 * <p>用于接收重置用户密码时提交的参数</p>
 * <p>包含新密码信息</p>
 * <p>使用Bean Validation注解进行参数校验，确保数据的有效性</p>
 * 
 * <AUTHOR>
 * @since 2025-07-23
 */
@Data
public class UserResetPasswordDTO {

    /**
     * 新密码
     * 
     * <p>用户的新登录密码</p>
     * <p>不能为空，长度限制在6-100个字符之间</p>
     * <p>实际存储时会进行加密处理</p>
     */
    @NotBlank(message = "新密码不能为空")
    @Size(min = 6, max = 100, message = "新密码长度必须在6-100个字符之间")
    private String newPassword;
}
