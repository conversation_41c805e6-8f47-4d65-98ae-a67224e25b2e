package com.akey.core.dto;

import com.akey.common.enums.AccountLockStatusEnum;
import lombok.Data;

import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

/**
 * 用户查询请求数据传输对象
 * 
 * <p>用于接收用户分页查询时提交的参数</p>
 * <p>包含分页参数、用户名筛选、用户类型筛选、账户锁定状态筛选等查询条件</p>
 * <p>使用Bean Validation注解进行参数校验，确保数据的有效性</p>
 * 
 * <AUTHOR>
 * @since 2025-07-23
 */
@Data
public class UserQueryDTO {

    /**
     * 页码
     * 
     * <p>当前查询的页码，从1开始</p>
     * <p>最小值为1，默认为1</p>
     */
    @Min(value = 1, message = "页码不能小于1")
    private Long current = 1L;

    /**
     * 每页大小
     * 
     * <p>每页返回的记录数量</p>
     * <p>最小值为1，最大值为100，默认为10</p>
     */
    @Min(value = 1, message = "每页大小不能小于1")
    @Max(value = 100, message = "每页大小不能超过100")
    private Long size = 10L;

    /**
     * 用户名
     * 
     * <p>用于模糊查询的用户名</p>
     * <p>可为空，为空时不进行用户名筛选</p>
     * <p>长度限制在50个字符以内</p>
     */
    @Size(max = 50, message = "用户名长度不能超过50个字符")
    private String username;

    /**
     * 用户类型ID
     * 
     * <p>用于筛选特定类型的用户</p>
     * <p>可为空，为空时查询所有类型的用户</p>
     */
    private String userTypeId;

    /**
     * 账户锁定状态
     * 
     * <p>用于筛选特定锁定状态的用户</p>
     * <p>可为空，为空时查询所有状态的用户</p>
     * <p>0: 未锁定，1: 已锁定</p>
     * 
     * @see com.akey.common.enums.AccountLockStatusEnum
     */
    private AccountLockStatusEnum accountLocked;
}
