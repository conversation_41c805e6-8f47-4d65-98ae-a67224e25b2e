package com.akey.core.dto;

import com.akey.common.enums.EnableStatusEnum;
import com.akey.common.enums.MenuTypeEnum;
import lombok.Data;

import jakarta.validation.constraints.Size;

/**
 * 菜单查询请求数据传输对象
 *
 * 
 * <AUTHOR>
 * @since 2025-07-09
 */
@Data
public class MenuQueryDTO {

    /**
     * 菜单名称
     * 
     * <p>用于模糊查询的菜单名称</p>
     * <p>可为空，为空时不进行菜单名称筛选</p>
     * <p>长度限制在100个字符以内</p>
     */
    @Size(max = 100, message = "菜单名称长度不能超过100个字符")
    private String menuName;

    /**
     * 菜单类型
     * 
     * <p>用于筛选特定类型的菜单</p>
     * <p>可为空，为空时查询所有类型的菜单</p>
     * <p>1:目录 2:菜单 3:按钮</p>
     * 
     * @see com.akey.common.enums.MenuTypeEnum
     */
    private MenuTypeEnum menuType;

    /**
     * 菜单状态
     * 
     * <p>用于筛选特定状态的菜单</p>
     * <p>可为空，为空时查询所有状态的菜单</p>
     * <p>0:禁用 1:启用</p>
     * 
     * @see com.akey.common.enums.EnableStatusEnum
     */
    private EnableStatusEnum status;
}
