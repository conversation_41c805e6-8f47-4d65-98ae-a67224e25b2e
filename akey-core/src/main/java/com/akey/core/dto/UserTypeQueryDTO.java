package com.akey.core.dto;

import com.akey.common.enums.BuiltinEnum;
import lombok.Data;

import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

/**
 * 用户类型查询请求数据传输对象
 * 
 * <p>用于接收用户类型分页查询时提交的参数</p>
 * <p>包含分页参数、类型名称筛选、是否内置筛选等查询条件</p>
 * <p>使用Bean Validation注解进行参数校验，确保数据的有效性</p>
 * 
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
public class UserTypeQueryDTO {

    /**
     * 页码
     * 
     * <p>当前查询的页码，从1开始</p>
     * <p>最小值为1，默认为1</p>
     */
    @Min(value = 1, message = "页码不能小于1")
    private Long current = 1L;

    /**
     * 每页大小
     * 
     * <p>每页返回的记录数量</p>
     * <p>最小值为1，最大值为100，默认为10</p>
     */
    @Min(value = 1, message = "每页大小不能小于1")
    @Max(value = 100, message = "每页大小不能超过100")
    private Long size = 10L;

    /**
     * 类型名称
     * 
     * <p>用于模糊查询的类型名称</p>
     * <p>可为空，为空时不进行类型名称筛选</p>
     * <p>长度限制在100个字符以内</p>
     */
    @Size(max = 100, message = "类型名称长度不能超过100个字符")
    private String typeName;

    /**
     * 是否内置类型
     * 
     * <p>用于筛选特定内置状态的用户类型</p>
     * <p>可为空，为空时查询所有状态的用户类型</p>
     * <p>0: 是内置类型，不可删除</p>
     * <p>1: 否，非内置类型，可删除</p>
     * 
     * @see com.akey.common.enums.BuiltinEnum
     */
    private BuiltinEnum isBuiltin;
}
