package com.akey.core.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

/**
 * 谷歌验证码验证请求数据传输对象
 * 
 * <p>用于接收验证谷歌验证码时提交的参数</p>
 * <p>用于登录时的二次验证或敏感操作的验证</p>
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
public class GoogleAuthVerifyDTO {

    /**
     * 验证码
     * 
     * <p>用户通过谷歌验证器应用生成的6位数字验证码</p>
     * <p>用于验证用户身份</p>
     */
    @NotBlank(message = "验证码不能为空")
    @Pattern(regexp = "^\\d{6}$", message = "验证码必须是6位数字")
    private String code;
}
