package com.akey.core.dto;

import com.akey.common.enums.EnableStatusEnum;
import com.akey.common.enums.IpWhitelistTypeEnum;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * IP白名单DTO
 * 
 * <p>用于IP白名单添加和更新的参数封装</p>
 * <p>包含必要的参数验证注解</p>
 * 
 * <AUTHOR>
 * @since 2025-07-31
 */
@Data
public class IpWhitelistDTO {

    /**
     * 主键ID
     * 
     * <p>更新时必填，添加时不填</p>
     */
    private String id;

    /**
     * 白名单类型
     * 
     * <p>1-系统白名单，2-接口白名单</p>
     */
    @NotNull(message = "白名单类型不能为空")
    private IpWhitelistTypeEnum whitelistType;

    /**
     * IP地址或IP段
     * 
     * <p>支持单个IP地址和CIDR格式的IP段</p>
     * <p>例如：************* 或 ***********/24</p>
     */
    @NotBlank(message = "IP地址不能为空")
    @Size(max = 50, message = "IP地址长度不能超过50个字符")
    private String ipAddress;



    /**
     * 启用状态
     * 
     * <p>0-禁用，1-启用</p>
     * <p>默认为启用</p>
     */
    private EnableStatusEnum enableStatus = EnableStatusEnum.ENABLED;

    /**
     * 备注说明
     * 
     * <p>记录IP的用途等信息</p>
     */
    @Size(max = 500, message = "备注说明长度不能超过500个字符")
    private String remark;

    /**
     * 判断是否为更新操作
     * 
     * @return true:更新操作，false:添加操作
     */
    public boolean isUpdate() {
        return id != null && !id.trim().isEmpty();
    }


}
