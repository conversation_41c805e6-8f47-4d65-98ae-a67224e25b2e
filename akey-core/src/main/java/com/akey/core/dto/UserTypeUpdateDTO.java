package com.akey.core.dto;

import com.akey.common.enums.BuiltinEnum;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 修改用户类型请求数据传输对象
 * 
 * <p>用于接收修改用户类型时提交的参数</p>
 * <p>包含类型名称、是否内置等可修改的信息</p>
 * <p>使用Bean Validation注解进行参数校验，确保数据的有效性</p>
 * 
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
public class UserTypeUpdateDTO {

    /**
     * 类型名称
     * 
     * <p>用户类型的显示名称</p>
     * <p>不能为空，长度限制在1-100个字符之间</p>
     * <p>如：超级管理员、普通管理员、普通用户等</p>
     */
    @NotBlank(message = "类型名称不能为空")
    @Size(min = 1, max = 100, message = "类型名称长度必须在1-100个字符之间")
    private String typeName;

    /**
     * 是否内置类型
     * 
     * <p>0: 是内置类型，不可删除</p>
     * <p>1: 否，非内置类型，可删除</p>
     * <p>注意：内置类型的此字段不允许修改</p>
     * 
     * @see com.akey.common.enums.BuiltinEnum
     */
    private BuiltinEnum isBuiltin;
}
