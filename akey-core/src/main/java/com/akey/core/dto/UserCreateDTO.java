package com.akey.core.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 创建用户请求数据传输对象
 * 
 * <p>用于接收创建用户时提交的参数</p>
 * <p>包含用户名、密码、用户类型等基本信息</p>
 * <p>使用Bean Validation注解进行参数校验，确保数据的有效性</p>
 * 
 * <AUTHOR>
 * @since 2025-07-23
 */
@Data
public class UserCreateDTO {

    /**
     * 用户名
     * 
     * <p>用户的登录名称</p>
     * <p>不能为空，长度限制在1-50个字符之间</p>
     * <p>必须在系统中唯一</p>
     */
    @NotBlank(message = "用户名不能为空")
    @Size(min = 1, max = 50, message = "用户名长度必须在1-50个字符之间")
    private String username;

    /**
     * 用户密码
     * 
     * <p>用户的登录密码</p>
     * <p>不能为空，长度限制在6-100个字符之间</p>
     * <p>实际存储时会进行加密处理</p>
     */
    @NotBlank(message = "用户密码不能为空")
    @Size(min = 6, max = 100, message = "用户密码长度必须在6-100个字符之间")
    private String password;

    /**
     * 用户类型ID
     * 
     * <p>用户所属的类型标识</p>
     * <p>不能为空，必须是系统中存在的用户类型</p>
     */
    @NotBlank(message = "用户类型ID不能为空")
    private String userTypeId;
}
