package com.akey.core.dto;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * 批量踢出用户请求数据传输对象
 * 
 * <p>用于接收批量踢出在线用户时提交的参数</p>
 * <p>支持按用户ID列表、设备类型等条件进行批量操作</p>
 * <p>使用Bean Validation注解进行参数校验，确保数据的有效性</p>
 * 
 * <AUTHOR>
 * @since 2025-08-02
 */
@Data
public class BatchKickoutDTO {

    /**
     * 用户ID列表
     * 
     * <p>要踢出的用户ID集合</p>
     * <p>不能为空，至少包含一个用户ID</p>
     * <p>建议单次操作不超过100个用户以保证性能</p>
     */
    @NotEmpty(message = "用户ID列表不能为空")
    @Size(max = 100, message = "单次最多只能踢出100个用户")
    private List<String> loginIds;

    /**
     * 设备类型
     * 
     * <p>指定要踢出的设备类型</p>
     * <p>可为空，为空时踢出用户的所有设备</p>
     * <p>常见设备类型：PC、WEB、APP、MOBILE、HD等</p>
     */
    @Size(max = 20, message = "设备类型长度不能超过20个字符")
    private String deviceType;

    /**
     * 操作类型
     * 
     * <p>指定踢出操作的类型</p>
     * <p>LOGOUT: 强制注销（完全清除Token信息）</p>
     * <p>KICKOUT: 踢人下线（标记为"已被踢下线"状态）</p>
     * <p>默认为KICKOUT</p>
     */
    private KickoutType operationType = KickoutType.KICKOUT;

    /**
     * 操作原因
     * 
     * <p>执行批量踢出操作的原因说明</p>
     * <p>可选字段，用于记录操作日志</p>
     * <p>长度限制在200个字符以内</p>
     */
    @Size(max = 200, message = "操作原因长度不能超过200个字符")
    private String reason;

    /**
     * 踢出操作类型枚举
     */
    public enum KickoutType {
        /**
         * 强制注销 - 完全清除Token信息
         */
        LOGOUT,
        
        /**
         * 踢人下线 - 标记为"已被踢下线"状态
         */
        KICKOUT
    }
}
