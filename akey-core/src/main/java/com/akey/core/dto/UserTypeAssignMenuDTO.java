package com.akey.core.dto;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 用户类型分配菜单请求数据传输对象
 * 
 * <p>用于接收为用户类型分配菜单权限时提交的参数</p>
 * <p>包含要分配的菜单ID列表</p>
 * <p>使用Bean Validation注解进行参数校验，确保数据的有效性</p>
 * 
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
public class UserTypeAssignMenuDTO {

    /**
     * 菜单ID列表
     * 
     * <p>要分配给用户类型的菜单ID集合</p>
     * <p>不能为null，但可以为空列表（表示清空所有菜单权限）</p>
     * <p>系统会先清空该用户类型的所有菜单关联，再添加新的关联</p>
     * <p>菜单ID必须是有效的菜单标识符</p>
     */
    @NotNull(message = "菜单ID列表不能为null")
    private List<String> menuIds;
}
