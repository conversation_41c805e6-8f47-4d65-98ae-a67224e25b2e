package com.akey.core.dto;

import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;

/**
 * 在线用户查询请求数据传输对象
 * 
 * <p>用于接收查询在线用户时提交的参数</p>
 * <p>包含分页参数、搜索关键词、设备类型筛选等查询条件</p>
 * <p>使用Bean Validation注解进行参数校验，确保数据的有效性</p>
 * 
 * <AUTHOR>
 * @since 2025-08-02
 */
@Data
public class OnlineUserQueryDTO {

    /**
     * 当前页码
     * 
     * <p>分页查询的页码，从1开始</p>
     * <p>默认为第1页</p>
     */
    @Min(value = 1, message = "页码必须大于0")
    private Long current = 1L;

    /**
     * 每页大小
     * 
     * <p>每页显示的记录数量</p>
     * <p>默认为10条记录</p>
     * <p>建议不超过100条以保证查询性能</p>
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Long size = 10L;

    /**
     * 搜索关键词
     * 
     * <p>用于模糊搜索用户ID或用户名</p>
     * <p>可为空，为空时不进行关键词筛选</p>
     * <p>长度限制在100个字符以内</p>
     */
    @Size(max = 100, message = "搜索关键词长度不能超过100个字符")
    private String keyword;

    /**
     * 设备类型筛选
     * 
     * <p>用于筛选特定设备类型的在线用户</p>
     * <p>可为空，为空时查询所有设备类型的用户</p>
     * <p>常见设备类型：PC、WEB、APP、MOBILE、HD等</p>
     */
    @Size(max = 20, message = "设备类型长度不能超过20个字符")
    private String deviceType;

    /**
     * 排序类型
     * 
     * <p>用于指定查询结果的排序方式</p>
     * <p>true: 升序排列</p>
     * <p>false: 降序排列</p>
     * <p>默认为降序，按登录时间倒序排列</p>
     */
    private Boolean sortType = false;
}
