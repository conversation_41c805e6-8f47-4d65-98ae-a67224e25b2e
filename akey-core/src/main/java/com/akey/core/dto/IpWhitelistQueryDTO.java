package com.akey.core.dto;

import com.akey.common.enums.EnableStatusEnum;
import com.akey.common.enums.IpWhitelistTypeEnum;
import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

/**
 * IP白名单查询DTO
 * 
 * <p>用于IP白名单分页查询的参数封装</p>
 * <p>包含分页参数和筛选条件</p>
 * 
 * <AUTHOR>
 * @since 2025-07-31
 */
@Data
public class IpWhitelistQueryDTO {

    /**
     * 页码
     * 
     * <p>从1开始，默认为1</p>
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码必须大于0")
    private Long current = 1L;

    /**
     * 每页大小
     * 
     * <p>默认为10，最大不超过100</p>
     */
    @NotNull(message = "每页大小不能为空")
    @Min(value = 1, message = "每页大小必须大于0")
    private Long size = 10L;

    /**
     * 白名单类型
     * 
     * <p>1-系统白名单，2-接口白名单</p>
     * <p>可选参数，为空时查询所有类型</p>
     */
    private IpWhitelistTypeEnum whitelistType;

    /**
     * IP地址
     * 
     * <p>支持模糊查询</p>
     * <p>可选参数，为空时不按IP地址筛选</p>
     */
    private String ipAddress;

    /**
     * 用户名
     *
     * <p>根据用户名模糊查询白名单</p>
     * <p>可选参数，为空时不按用户名筛选</p>
     */
    private String username;

    /**
     * 启用状态
     * 
     * <p>0-禁用，1-启用</p>
     * <p>可选参数，为空时查询所有状态</p>
     */
    private EnableStatusEnum enableStatus;

    /**
     * 获取页码，确保不小于1
     * 
     * @return 页码
     */
    public Long getCurrent() {
        return current != null && current > 0 ? current : 1L;
    }

    /**
     * 获取每页大小，确保在合理范围内
     * 
     * @return 每页大小
     */
    public Long getSize() {
        if (size == null || size <= 0) {
            return 10L;
        }
        // 限制最大每页大小为100
        return Math.min(size, 100L);
    }
}
