package com.akey.core.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 用户修改密码请求数据传输对象
 * 
 * <p>用于接收用户自身修改密码时提交的参数</p>
 * <p>包含旧密码、新密码信息，用于验证和更新密码</p>
 * <p>使用Bean Validation注解进行参数校验，确保数据的有效性</p>
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
public class UserChangePasswordDTO {

    /**
     * 旧密码
     * 
     * <p>用户当前的登录密码</p>
     * <p>用于验证用户身份，确保是本人操作</p>
     */
    @NotBlank(message = "旧密码不能为空")
    private String oldPassword;

    /**
     * 新密码
     * 
     * <p>用户要设置的新登录密码</p>
     * <p>不能为空，长度限制在6-100个字符之间</p>
     * <p>实际存储时会进行加密处理</p>
     */
    @NotBlank(message = "新密码不能为空")
    @Size(min = 6, max = 100, message = "新密码长度必须在6-100个字符之间")
    private String newPassword;

    /**
     * 确认新密码
     * 
     * <p>用户再次输入的新密码</p>
     * <p>用于确认用户输入的新密码正确，防止输入错误</p>
     */
    @NotBlank(message = "确认密码不能为空")
    private String confirmPassword;
}
