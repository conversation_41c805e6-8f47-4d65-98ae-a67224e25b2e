package com.akey.core.dto;

import com.akey.common.enums.EnableStatusEnum;
import com.akey.common.enums.ExternalLinkEnum;
import com.akey.common.enums.MenuTypeEnum;
import com.akey.common.enums.OpenModeEnum;
import com.akey.common.enums.ShowHideEnum;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;

/**
 * 更新菜单请求数据传输对象
 * 
 * <p>用于接收更新菜单时提交的参数</p>
 * <p>包含菜单的基本信息、路由配置、权限设置等</p>
 * <p>使用Bean Validation注解进行参数校验，确保数据的有效性</p>
 * 
 * <AUTHOR>
 * @since 2025-07-09
 */
@Data
public class MenuUpdateDTO {

    /**
     * 菜单ID
     * 
     * <p>要更新的菜单的唯一标识</p>
     * <p>必须指定有效的菜单ID</p>
     */
    @NotBlank(message = "菜单ID不能为空")
    private String id;

    /**
     * 父菜单ID
     * 
     * <p>顶级菜单时为空</p>
     * <p>子菜单时必须指定有效的父菜单ID</p>
     */
    private String parentId;

    /**
     * 菜单名称
     * 
     * <p>菜单的显示名称</p>
     * <p>不能为空，长度限制在1-100个字符之间</p>
     */
    @NotBlank(message = "菜单名称不能为空")
    @Size(min = 1, max = 100, message = "菜单名称长度必须在1-100个字符之间")
    private String menuName;

    /**
     * 菜单类型
     * 
     * <p>1:目录 2:菜单 3:按钮</p>
     * <p>必须指定有效的菜单类型</p>
     * 
     * @see com.akey.common.enums.MenuTypeEnum
     */
    @NotNull(message = "菜单类型不能为空")
    private MenuTypeEnum menuType;

    /**
     * 菜单编码
     * 
     * <p>唯一标识符，用于权限控制</p>
     * <p>不能为空，长度限制在1-100个字符之间</p>
     * <p>建议使用英文字母、数字、下划线组合</p>
     */
    @NotBlank(message = "菜单编码不能为空")
    @Size(min = 1, max = 100, message = "菜单编码长度必须在1-100个字符之间")
    private String menuCode;

    /**
     * 路由路径
     * 
     * <p>前端路由地址</p>
     * <p>菜单类型时必填，目录和按钮类型时可选</p>
     * <p>长度限制在255个字符以内</p>
     */
    @Size(max = 255, message = "路由路径长度不能超过255个字符")
    private String routePath;

    /**
     * 组件路径
     * 
     * <p>前端组件文件路径</p>
     * <p>菜单类型时必填，目录和按钮类型时可选</p>
     * <p>长度限制在255个字符以内</p>
     */
    @Size(max = 255, message = "组件路径长度不能超过255个字符")
    private String componentPath;

    /**
     * 图标
     * 
     * <p>菜单显示的图标名称</p>
     * <p>可选字段，长度限制在100个字符以内</p>
     */
    @Size(max = 100, message = "图标名称长度不能超过100个字符")
    private String icon;

    /**
     * 排序号
     * 
     * <p>数值越小越靠前</p>
     * <p>最小值为0</p>
     */
    @Min(value = 0, message = "排序号不能小于0")
    private Integer sortOrder;

    /**
     * 是否可见
     * 
     * <p>0:隐藏 1:显示</p>
     * 
     * @see com.akey.common.enums.ShowHideEnum
     */
    private ShowHideEnum visible;

    /**
     * 状态
     * 
     * <p>0:禁用 1:启用</p>
     * 
     * @see com.akey.common.enums.EnableStatusEnum
     */
    private EnableStatusEnum status;

    /**
     * 权限标识
     * 
     * <p>用于权限校验的标识符</p>
     * <p>可选字段，长度限制在200个字符以内</p>
     * <p>建议格式：模块:功能:操作，如 system:user:view</p>
     */
    @Size(max = 200, message = "权限标识长度不能超过200个字符")
    private String permission;

    /**
     * 是否外部链接
     * 
     * <p>0:否 1:是</p>
     * 
     * @see com.akey.common.enums.ExternalLinkEnum
     */
    private ExternalLinkEnum externalLink;

    /**
     * 打开方式
     * 
     * <p>_self:当前窗口 _blank:新窗口</p>
     * 
     * @see com.akey.common.enums.OpenModeEnum
     */
    private OpenModeEnum openMode;

    /**
     * 路由参数
     *
     * <p>JSON格式存储路由参数，用于动态路由传参</p>
     * <p>可选字段，长度限制在1000个字符以内</p>
     *
     * <p>示例格式：</p>
     * <pre>
     * {
     *   "id": "123",
     *   "type": "edit",
     *   "tab": "basic"
     * }
     * </pre>
     */
    @Size(max = 1000, message = "路由参数长度不能超过1000个字符")
    private String routeParams;
}
