package com.akey.core.dto;

import com.akey.common.enums.AccountLockStatusEnum;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 修改用户请求数据传输对象
 *
 * <p>用于接收修改用户时提交的参数</p>
 * <p>包含用户名、用户类型和账户锁定状态的修改，密码有单独的重置接口</p>
 * <p>使用Bean Validation注解进行参数校验，确保数据的有效性</p>
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Data
public class UserUpdateDTO {

    /**
     * 用户名
     *
     * <p>用户的登录名称</p>
     * <p>不能为空，长度限制在1-50个字符之间</p>
     * <p>必须在系统中唯一</p>
     */
    @NotBlank(message = "用户名不能为空")
    @Size(min = 1, max = 50, message = "用户名长度必须在1-50个字符之间")
    private String username;

    /**
     * 用户类型ID
     *
     * <p>用户所属的类型标识</p>
     * <p>不能为空，必须是系统中存在的用户类型</p>
     */
    @NotBlank(message = "用户类型ID不能为空")
    private String userTypeId;

    /**
     * 账户锁定状态
     *
     * <p>用户账户的锁定状态</p>
     * <p>0: 未锁定，1: 已锁定</p>
     * <p>可为空，为空时不修改锁定状态</p>
     *
     * @see com.akey.common.enums.AccountLockStatusEnum
     */
    private AccountLockStatusEnum accountLocked;
}
