package com.akey.core.factory;

import cn.dev33.satoken.context.mock.SaTokenContextMockUtil;
import cn.dev33.satoken.stp.StpUtil;
import com.akey.common.entity.DeviceInfo;
import com.akey.core.builder.LoginLogBuilder;
import com.akey.core.dao.entity.LoginLog;
import com.akey.core.dao.service.LoginLogService;
import com.akey.common.service.AddressResolveService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 异步登录日志工厂
 * 
 * <p>提供异步记录登录日志的功能，避免影响登录响应时间</p>
 * <p>支持登录成功和失败两种情况的日志记录</p>
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AsyncLoginLogFactory {

    private final LoginLogService loginLogService;
    private final AddressResolveService addressResolveService;

    /**
     * 异步记录登录成功日志
     *
     * @param userId 用户ID
     * @param username 用户名
     * @param sessionId 会话ID
     * @param deviceInfo 设备信息
     */
    @Async("loginLogExecutor")
    public void recordSuccessLoginAsync(String userId, String username, String sessionId,
                                       DeviceInfo deviceInfo) {
        try {
            SaTokenContextMockUtil.setMockContext(()->{
                StpUtil.setTokenValueToStorage(deviceInfo.getToken());
                log.debug("开始异步记录登录成功日志, userId: {}, username: {}", userId, username);

                LoginLog loginLog = LoginLogBuilder.create()
                        .user(userId, username)
                        .success(sessionId)
                        .deviceInfo(deviceInfo)
                        .build();

                // 评估风险等级和可疑行为
                enhanceLoginLogSecurity(loginLog, userId, deviceInfo);

                loginLogService.saveLoginLog(loginLog);

                log.debug("异步记录登录成功日志完成, userId: {}, username: {}", userId, username);
            });

            
        } catch (Exception e) {
            log.error("异步记录登录成功日志失败, userId: {}, username: {}, error: {}", 
                     userId, username, e.getMessage(), e);
        }
    }

    /**
     * 异步记录登录失败日志
     *
     * @param username 用户名（可能为空）
     * @param failureReason 失败原因
     * @param deviceInfo 设备信息
     */
    @Async("loginLogExecutor")
    public void recordFailureLoginAsync(String username, String failureReason,
                                       DeviceInfo deviceInfo) {
        try {
            log.debug("开始异步记录登录失败日志, username: {}, reason: {}", username, failureReason);
            
            LoginLog loginLog = LoginLogBuilder.create()
                    .user(null, username)
                    .failure(failureReason)
                    .deviceInfo(deviceInfo)
                    .build();
            
            // 评估风险等级和可疑行为
            enhanceLoginLogSecurity(loginLog, null, deviceInfo);
            
            loginLogService.saveLoginLog(loginLog);
            
            log.debug("异步记录登录失败日志完成, username: {}, reason: {}", username, failureReason);
            
        } catch (Exception e) {
            log.error("异步记录登录失败日志失败, username: {}, reason: {}, error: {}", 
                     username, failureReason, e.getMessage(), e);
        }
    }



    /**
     * 增强登录日志的安全信息
     *
     * @param loginLog 登录日志对象
     * @param userId 用户ID
     * @param deviceInfo 设备信息
     */
    private void enhanceLoginLogSecurity(LoginLog loginLog, String userId, DeviceInfo deviceInfo) {
        try {
            // 异步获取地理位置信息
            if (deviceInfo != null && deviceInfo.getClientIp() != null) {
                String location = addressResolveService.getLocationByIP(deviceInfo.getClientIp());
                if (StringUtils.hasText(location)) {
                    loginLog.setLocation(location);

                    log.debug("异步获取地理位置信息完成, ip: {}, location: {}",
                             deviceInfo.getClientIp(), location);
                }
            }

            // 设置风险等级
            if (deviceInfo != null && deviceInfo.getRiskLevel() != null) {
                loginLog.setRiskLevel(deviceInfo.getRiskLevel());
            }



            // 检测可疑活动
            if (userId != null && deviceInfo != null) {
                var suspiciousReasons = loginLogService.detectSuspiciousActivity(userId, deviceInfo);
                if (!suspiciousReasons.isEmpty()) {
                    loginLog.setIsSuspicious(1);
                    loginLog.setSuspiciousReasons(suspiciousReasons);
                }
            }



        } catch (Exception e) {
            log.warn("增强登录日志安全信息时发生异常: {}", e.getMessage());
            // 不抛出异常，避免影响日志记录
        }
    }

    /**
     * 异步记录用户名密码验证失败
     *
     * @param username 用户名
     * @param deviceInfo 设备信息
     * @param specificReason 具体失败原因（用户不存在、密码错误等）
     */
    @Async("loginLogExecutor")
    public void recordCredentialFailureAsync(String username, DeviceInfo deviceInfo, String specificReason) {
        recordFailureLoginAsync(username, specificReason, deviceInfo);
    }

    /**
     * 批量异步记录登录日志（用于批量操作场景）
     * 
     * @param loginLogs 登录日志列表
     */
    @Async("loginLogExecutor")
    public void batchRecordLoginLogsAsync(java.util.List<LoginLog> loginLogs) {
        try {
            log.debug("开始批量异步记录登录日志, 数量: {}", loginLogs.size());
            
            for (LoginLog loginLog : loginLogs) {
                loginLogService.saveLoginLog(loginLog);
            }
            
            log.debug("批量异步记录登录日志完成, 数量: {}", loginLogs.size());
            
        } catch (Exception e) {
            log.error("批量异步记录登录日志失败, 数量: {}, error: {}", 
                     loginLogs.size(), e.getMessage(), e);
        }
    }
}
