package com.akey.core.builder;

import com.akey.common.entity.DeviceInfo;
import com.akey.common.enums.LoginStatusEnum;
import com.akey.common.enums.RiskLevelEnum;
import com.akey.core.dao.entity.LoginLog;

import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 登录日志构建器
 *
 * <p>提供便捷的方式构建登录日志对象</p>
 * <p>LoginLog继承BaseEntity，自动获得id、createTime、updateTime等公共字段</p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public class LoginLogBuilder {

    private final LoginLog loginLog;

    private LoginLogBuilder() {
        this.loginLog = new LoginLog();
        // 设置业务字段默认值
        this.loginLog.setLoginTime(LocalDateTime.now());
        this.loginLog.setRiskLevel(RiskLevelEnum.LOW.getCode());
        this.loginLog.setIsSuspicious(0);
        this.loginLog.setIsProxy(0);
        // 注意：id、createTime、updateTime等字段由BaseEntity和MyBatis-Plus自动处理
    }

    /**
     * 创建构建器实例
     * 
     * @return 构建器实例
     */
    public static LoginLogBuilder create() {
        return new LoginLogBuilder();
    }

    /**
     * 设置用户信息
     * 
     * @param userId 用户ID
     * @param username 用户名
     * @return 构建器实例
     */
    public LoginLogBuilder user(String userId, String username) {
        this.loginLog.setUserId(userId);
        this.loginLog.setUsername(username);
        return this;
    }

    /**
     * 设置登录成功
     * 
     * @param sessionId 会话ID
     * @return 构建器实例
     */
    public LoginLogBuilder success(String sessionId) {
        this.loginLog.setLoginStatus(LoginStatusEnum.SUCCESS.getValue());
        this.loginLog.setSessionId(sessionId);
        return this;
    }

    /**
     * 设置登录失败
     * 
     * @param failureReason 失败原因
     * @return 构建器实例
     */
    public LoginLogBuilder failure(String failureReason) {
        this.loginLog.setLoginStatus(LoginStatusEnum.FAILURE.getValue());
        this.loginLog.setFailureReason(failureReason);
        return this;
    }



    /**
     * 设置设备信息
     * 
     * @param deviceInfo 设备信息
     * @return 构建器实例
     */
    public LoginLogBuilder deviceInfo(DeviceInfo deviceInfo) {
        if (deviceInfo == null) {
            return this;
        }

        // 网络信息
        this.loginLog.setClientIp(deviceInfo.getClientIp());
        this.loginLog.setIsProxy(deviceInfo.getIsProxy() != null && deviceInfo.getIsProxy() ? 1 : 0);
        this.loginLog.setProxyChain(deviceInfo.getProxyIp());

        // 注意：地理位置信息（location）将在AsyncLoginLogFactory中异步获取，不在此处设置

        // 设备信息
        this.loginLog.setUserAgent(deviceInfo.getUserAgent());
        this.loginLog.setDeviceType(deviceInfo.getDeviceType());
        this.loginLog.setDeviceBrand(deviceInfo.getDeviceBrand());
        this.loginLog.setDeviceModel(deviceInfo.getDeviceModel());
        this.loginLog.setOsName(deviceInfo.getOperatingSystem()); // DeviceInfo中是operatingSystem
        this.loginLog.setOsVersion(deviceInfo.getOsVersion());
        this.loginLog.setBrowserName(deviceInfo.getBrowserName());
        this.loginLog.setBrowserVersion(deviceInfo.getBrowserVersion());
        this.loginLog.setDeviceFingerprint(deviceInfo.getDeviceFingerprint());

        // 请求信息（DeviceInfo中没有这些字段，设置为null或默认值）
        this.loginLog.setRequestUri(null); // DeviceInfo中没有requestUri字段
        this.loginLog.setRequestMethod(null); // DeviceInfo中没有requestMethod字段
        this.loginLog.setReferer(null); // DeviceInfo中没有referer字段
        this.loginLog.setLanguage(deviceInfo.getLanguage());
        this.loginLog.setTimezone(deviceInfo.getTimezone());

        return this;
    }

    /**
     * 设置风险等级
     * 
     * @param riskLevel 风险等级
     * @return 构建器实例
     */
    public LoginLogBuilder riskLevel(String riskLevel) {
        this.loginLog.setRiskLevel(riskLevel);
        return this;
    }

    /**
     * 设置风险等级
     * 
     * @param riskLevel 风险等级枚举
     * @return 构建器实例
     */
    public LoginLogBuilder riskLevel(RiskLevelEnum riskLevel) {
        this.loginLog.setRiskLevel(riskLevel.getCode());
        return this;
    }

    /**
     * 设置可疑登录
     * 
     * @param suspicious 是否可疑
     * @param reasons 可疑原因列表
     * @return 构建器实例
     */
    public LoginLogBuilder suspicious(boolean suspicious, List<String> reasons) {
        this.loginLog.setIsSuspicious(suspicious ? 1 : 0);
        this.loginLog.setSuspiciousReasons(reasons);
        return this;
    }



    /**
     * 设置扩展数据
     * 
     * @param key 键
     * @param value 值
     * @return 构建器实例
     */
    public LoginLogBuilder extraData(String key, Object value) {
        if (this.loginLog.getExtraData() == null) {
            this.loginLog.setExtraData(new HashMap<>());
        }
        this.loginLog.getExtraData().put(key, value);
        return this;
    }

    /**
     * 设置扩展数据
     * 
     * @param extraData 扩展数据Map
     * @return 构建器实例
     */
    public LoginLogBuilder extraData(Map<String, Object> extraData) {
        this.loginLog.setExtraData(extraData);
        return this;
    }

    /**
     * 设置标签
     * 
     * @param tags 标签，逗号分隔
     * @return 构建器实例
     */
    public LoginLogBuilder tags(String tags) {
        this.loginLog.setTags(tags);
        return this;
    }

    /**
     * 添加标签
     * 
     * @param tag 标签
     * @return 构建器实例
     */
    public LoginLogBuilder addTag(String tag) {
        if (!StringUtils.hasText(tag)) {
            return this;
        }
        
        String currentTags = this.loginLog.getTags();
        if (!StringUtils.hasText(currentTags)) {
            this.loginLog.setTags(tag);
        } else {
            this.loginLog.setTags(currentTags + "," + tag);
        }
        return this;
    }

    /**
     * 构建登录日志对象
     * 
     * @return 登录日志对象
     */
    public LoginLog build() {
        return this.loginLog;
    }
}
