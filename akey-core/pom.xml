<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <!--
        继承父项目配置 - 从父项目继承依赖管理和插件配置
    -->
    <parent>
        <groupId>com.akey</groupId>
        <artifactId>akey</artifactId>
        <version>0.0.1-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    
    <!--
        数据访问模块配置
        模块作用：负责数据持久化操作，包括实体类定义、数据访问接口、数据库配置
        提供数据库操作的统一接口和实现
    -->
    <artifactId>akey-core</artifactId>
    <name>akey-core</name>
    <description>核心业务与数据模块，包含系统的核心数据模型、数据访问层（DAO/Repository）和业务逻辑实现（Service），是应用程序的核心功能载体。</description>
    
    <!-- 
        依赖配置
    -->
    <dependencies>
        <!--
            公共模块依赖 - 使用公共工具类和常量定义
        -->
        <dependency>
            <groupId>com.akey</groupId>
            <artifactId>akey-framework-core</artifactId>
            <version>${project.version}</version>
        </dependency>

    </dependencies>
    
    <!-- 
        构建配置
        继承父项目的插件配置，无需额外配置
    -->
    <build>
        <plugins>
            <!-- Maven编译插件 - 继承父配置 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project> 
