<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!-- 继承父项目配置 - 继承Spring Boot父项目获得版本管理和插件配置 -->
    <parent>
        <groupId>com.akey</groupId>
        <artifactId>akey</artifactId>
        <version>0.0.1-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <!--  -->
    <artifactId>akey-framework-core</artifactId>
    <name>akey-framework-core</name>
    <description>Framework-core框架核心模块 - 对MybatisPlus、Redis等进行配置</description>

    <!-- 依赖配置 -->
    <dependencies>
        <!-- 公共模块依赖 -->
        <dependency>
            <groupId>com.akey</groupId>
            <artifactId>akey-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- Spring Boot基础依赖 - 提供Spring核心功能 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <!-- Spring Boot 参数验证 - 提供Bean Validation支持 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- MySQL 驱动 -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
        </dependency>

        <!-- Druid数据库连接池 - 阿里巴巴开源的数据库连接池，提供监控功能 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-3-starter</artifactId>
        </dependency>

        <!-- MyBatis-Plus - MyBatis的增强工具，简化CRUD操作 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>

        <!-- Redis相关依赖 -->
        <!-- Spring Boot Redis Starter - 提供Redis操作支持 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <!-- Lettuce连接池依赖 - Redis连接池 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>

        <!-- Sa-Token 整合 RedisTemplate -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-redis-template</artifactId>
        </dependency>

        <!-- Sa-Token 整合 Redis （使用 Fastjson2 序列化方式） -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-fastjson2</artifactId>
        </dependency>

    </dependencies>

    <!--
       构建配置
       继承父项目的插件配置，无需额外配置
   -->
    <build>
        <plugins>
            <!-- Maven编译插件 - 继承父配置 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
