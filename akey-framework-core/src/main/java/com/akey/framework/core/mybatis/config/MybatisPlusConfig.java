package com.akey.framework.core.mybatis.config;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.BlockAttackInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;

/**
 * MyBatis-Plus配置类
 * 
 * <p>功能说明：</p>
 * <ul>
 *   <li>分页插件配置 - 支持多数据库分页</li>
 *   <li>乐观锁插件配置 - 防止并发更新问题</li>
 *   <li>自动填充配置 - 自动填充创建时间、更新时间等字段</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Configuration
public class MybatisPlusConfig {

    /**
     * 配置MyBatis-Plus拦截器
     * 
     * <p>包含以下插件：</p>
     * <ul>
     *   <li>分页插件 - 自动处理分页SQL</li>
     *   <li>乐观锁插件 - 自动处理version字段</li>
     * </ul>
     * 
     * @return MybatisPlusInterceptor 拦截器实例
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        log.info("初始化 - MyBatis-Plus拦截器...");

        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        
        // 分页插件 - 支持MySQL分页查询
        PaginationInnerInterceptor paginationInterceptor = new PaginationInnerInterceptor(DbType.MYSQL);
        // 设置请求的页面大于最大页后操作，true调回到首页，false继续请求，默认false
        paginationInterceptor.setOverflow(false);
        // 单页分页条数限制，默认无限制
        paginationInterceptor.setMaxLimit(500L);
        interceptor.addInnerInterceptor(paginationInterceptor);
        
        // 阻止全表更新删除
        interceptor.addInnerInterceptor(new BlockAttackInnerInterceptor());

        // 乐观锁插件 - 自动处理version字段
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());

        return interceptor;
    }

    /**
     * 自动填充配置
     * 
     * <p>功能说明：</p>
     * <ul>
     *   <li>插入时自动填充：createTime、updateTime</li>
     *   <li>更新时自动填充：updateTime</li>
     * </ul>
     * 
     * @return MetaObjectHandler 自动填充处理器
     */
    @Bean
    public MetaObjectHandler metaObjectHandler() {
        log.info("初始化 - MyBatis-Plus自动填充处理器...");
        
        return new MetaObjectHandler() {
            
            /**
             * 插入时的填充策略
             * 
             * @param metaObject 元对象
             */
            @Override
            public void insertFill(MetaObject metaObject) {
                log.debug("执行插入时自动填充...");
                LocalDateTime now = LocalDateTime.now();
                // 自动填充创建时间
                this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, now);
                // 自动填充创建人
                this.strictInsertFill(metaObject, "createBy", String.class, getUserId());

            }

            /**
             * 更新时的填充策略
             * 
             * @param metaObject 元对象
             */
            @Override
            public void updateFill(MetaObject metaObject) {
                log.debug("执行更新时自动填充...");
                // 自动填充更新时间
                this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
                // 自动填充更新人
                this.strictUpdateFill(metaObject, "updateBy", String.class, getUserId());
            }
        };
    }

    /**
     * 获取当前用户ID
     * @return 用户ID
     */
    public String getUserId(){
        String userId = null;
        try {
            userId= StpUtil.getLoginIdAsString();
        }catch (Exception e){
            log.error("用户ID获取失败：{}", e.getMessage());
        }finally {
            if(null == userId || "".equals(userId)){
                userId= "-1";
            }
        }
        return userId;
    }
} 