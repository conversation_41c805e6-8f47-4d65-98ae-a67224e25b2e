package com.akey.framework.core.redis.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.Callable;
import java.util.function.Supplier;

/**
 * 缓存工具类
 * 
 * <p>功能说明：</p>
 * <ul>
 *   <li>提供高级缓存操作封装</li>
 *   <li>支持缓存穿透、缓存雪崩防护</li>
 *   <li>提供缓存更新策略</li>
 *   <li>支持缓存预热功能</li>
 * </ul>
 * 
 * <p>使用示例：</p>
 * <pre>
 * // 获取缓存，如果不存在则执行supplier并缓存结果
 * User user = cacheUtil.getOrSet("user:1", 3600, () -> userService.getById(1));
 * 
 * // 删除缓存
 * cacheUtil.evict("user:1");
 * 
 * // 批量删除缓存
 * cacheUtil.evictByPattern("user:*");
 * </pre>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class CacheUtil {

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 缓存前缀
     */
    private static final String CACHE_PREFIX = "akey:cache:";
    
    /**
     * 空值缓存标识
     */
    private static final String NULL_VALUE = "NULL";
    
    /**
     * 空值缓存时间(秒)
     */
    private static final long NULL_CACHE_TIME = 300;

    /**
     * 获取缓存，如果不存在则执行supplier并缓存结果
     * 
     * @param key      缓存key
     * @param expireTime 过期时间(秒)
     * @param supplier 数据提供者
     * @param <T>      数据类型
     * @return 缓存数据
     */
    public <T> T getOrSet(String key, long expireTime, Supplier<T> supplier) {
        return getOrSet(key, expireTime, supplier, null);
    }

    /**
     * 获取缓存，如果不存在则执行supplier并缓存结果
     * 
     * @param key        缓存key
     * @param expireTime 过期时间(秒)
     * @param supplier   数据提供者
     * @param clazz      返回类型
     * @param <T>        数据类型
     * @return 缓存数据
     */
    @SuppressWarnings("unchecked")
    public <T> T getOrSet(String key, long expireTime, Supplier<T> supplier, Class<T> clazz) {
        String cacheKey = buildCacheKey(key);
        
        try {
            // 先从缓存获取
            Object cached = redisUtil.get(cacheKey);
            if (cached != null) {
                // 检查是否是空值缓存
                if (NULL_VALUE.equals(cached)) {
                    return null;
                }
                
                if (clazz != null) {
                    return redisUtil.get(cacheKey, clazz);
                } else {
                    return (T) cached;
                }
            }
            
            // 缓存不存在，执行supplier获取数据
            T data = supplier.get();
            
            // 缓存数据
            if (data != null) {
                redisUtil.set(cacheKey, data, expireTime);
                log.debug("缓存数据成功, key: {}, expireTime: {}", cacheKey, expireTime);
            } else {
                // 缓存空值，防止缓存穿透
                redisUtil.set(cacheKey, NULL_VALUE, NULL_CACHE_TIME);
                log.debug("缓存空值成功, key: {}, expireTime: {}", cacheKey, NULL_CACHE_TIME);
            }
            
            return data;
        } catch (Exception e) {
            log.error("获取或设置缓存失败, key: {}", cacheKey, e);
            // 缓存异常时直接执行supplier
            return supplier.get();
        }
    }

    /**
     * 获取缓存，如果不存在则执行callable并缓存结果
     *
     * @param key        缓存key
     * @param expireTime 过期时间(秒)
     * @param callable   数据提供者
     * @param <T>        数据类型
     * @return 缓存数据
     * @throws Exception callable执行异常
     */
    public <T> T getOrSetWithCallable(String key, long expireTime, Callable<T> callable) throws Exception {
        return getOrSet(key, expireTime, () -> {
            try {
                return callable.call();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * 设置缓存
     * 
     * @param key        缓存key
     * @param value      缓存值
     * @param expireTime 过期时间(秒)
     * @return 是否成功
     */
    public boolean set(String key, Object value, long expireTime) {
        String cacheKey = buildCacheKey(key);
        return redisUtil.set(cacheKey, value, expireTime);
    }

    /**
     * 获取缓存
     * 
     * @param key   缓存key
     * @param clazz 返回类型
     * @param <T>   数据类型
     * @return 缓存数据
     */
    public <T> T get(String key, Class<T> clazz) {
        String cacheKey = buildCacheKey(key);
        Object cached = redisUtil.get(cacheKey);
        
        if (cached == null || NULL_VALUE.equals(cached)) {
            return null;
        }
        
        return redisUtil.get(cacheKey, clazz);
    }

    /**
     * 获取缓存
     * 
     * @param key 缓存key
     * @return 缓存数据
     */
    public Object get(String key) {
        String cacheKey = buildCacheKey(key);
        Object cached = redisUtil.get(cacheKey);
        
        if (NULL_VALUE.equals(cached)) {
            return null;
        }
        
        return cached;
    }

    /**
     * 删除缓存
     * 
     * @param key 缓存key
     */
    public void evict(String key) {
        String cacheKey = buildCacheKey(key);
        redisUtil.del(cacheKey);
        log.debug("删除缓存成功, key: {}", cacheKey);
    }

    /**
     * 批量删除缓存
     * 
     * @param keys 缓存key数组
     */
    public void evict(String... keys) {
        if (keys == null || keys.length == 0) {
            return;
        }
        
        String[] cacheKeys = new String[keys.length];
        for (int i = 0; i < keys.length; i++) {
            cacheKeys[i] = buildCacheKey(keys[i]);
        }
        
        redisUtil.del(cacheKeys);
        log.debug("批量删除缓存成功, keys: {}", (Object) cacheKeys);
    }

    /**
     * 根据模式删除缓存
     * 
     * @param pattern 缓存key模式，支持通配符
     * @return 删除的缓存数量
     */
    public long evictByPattern(String pattern) {
        String cachePattern = buildCacheKey(pattern);
        long count = redisUtil.deleteByPattern(cachePattern);
        log.debug("根据模式删除缓存成功, pattern: {}, count: {}", cachePattern, count);
        return count;
    }

    /**
     * 检查缓存是否存在
     * 
     * @param key 缓存key
     * @return 是否存在
     */
    public boolean exists(String key) {
        String cacheKey = buildCacheKey(key);
        return redisUtil.hasKey(cacheKey);
    }

    /**
     * 设置缓存过期时间
     * 
     * @param key        缓存key
     * @param expireTime 过期时间(秒)
     * @return 是否成功
     */
    public boolean expire(String key, long expireTime) {
        String cacheKey = buildCacheKey(key);
        return redisUtil.expire(cacheKey, expireTime);
    }

    /**
     * 获取缓存过期时间
     * 
     * @param key 缓存key
     * @return 过期时间(秒)
     */
    public long getExpire(String key) {
        String cacheKey = buildCacheKey(key);
        return redisUtil.getExpire(cacheKey);
    }

    /**
     * 缓存预热
     * 
     * @param key        缓存key
     * @param expireTime 过期时间(秒)
     * @param supplier   数据提供者
     * @param <T>        数据类型
     */
    public <T> void warmUp(String key, long expireTime, Supplier<T> supplier) {
        try {
            T data = supplier.get();
            if (data != null) {
                set(key, data, expireTime);
                log.info("缓存预热成功, key: {}", key);
            }
        } catch (Exception e) {
            log.error("缓存预热失败, key: {}", key, e);
        }
    }

    /**
     * 构建缓存key
     * 
     * @param key 原始key
     * @return 带前缀的缓存key
     */
    private String buildCacheKey(String key) {
        return CACHE_PREFIX + key;
    }

    /**
     * 清空所有缓存
     * 
     * @return 清空的缓存数量
     */
    public long clear() {
        long count = redisUtil.deleteByPattern(CACHE_PREFIX + "*");
        log.info("清空所有缓存成功, count: {}", count);
        return count;
    }
}
