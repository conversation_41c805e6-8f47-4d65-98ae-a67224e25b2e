package com.akey.framework.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 验证码配置属性类
 * 
 * <p>功能说明：</p>
 * <ul>
 *   <li>从配置文件中读取验证码相关配置</li>
 *   <li>支持不同环境的配置差异化</li>
 *   <li>提供验证码生成的默认参数</li>
 *   <li>支持动态配置更新</li>
 * </ul>
 * 
 * <p>配置示例：</p>
 * <pre>
 * akey:
 *   captcha:
 *     enabled: true
 *     default-type: NUMBER
 *     default-length: 4
 *     image:
 *       width: 120
 *       height: 40
 *     cache:
 *       expire-minutes: 5
 *       key-prefix: "captcha:"
 * </pre>
 *
 * <AUTHOR>
 * @since 2025-07-06
 */
@Data
@ConfigurationProperties(prefix = "akey.captcha")
public class CaptchaProperties {

    /**
     * 是否启用验证码功能
     * 默认启用
     */
    private Boolean enabled = true;

    /**
     * 默认验证码类型
     * 可选值：NUMBER、LETTER、MIXED、ARITHMETIC、RANDOM
     */
    private String defaultType = "NUMBER";

    /**
     * 默认验证码长度
     * 对算式类型无效
     */
    private Integer defaultLength = 4;

    /**
     * 图片配置
     */
    private ImageConfig image = new ImageConfig();

    /**
     * 缓存配置
     */
    private CacheConfig cache = new CacheConfig();

    /**
     * 安全配置
     */
    private SecurityConfig security = new SecurityConfig();

    /**
     * 图片配置类
     */
    @Data
    public static class ImageConfig {
        
        /**
         * 图片宽度（像素）
         */
        private Integer width = 120;
        
        /**
         * 图片高度（像素）
         */
        private Integer height = 40;
        
        /**
         * 字体大小
         */
        private Integer fontSize = 24;
        
        /**
         * 干扰线数量
         */
        private Integer interferenceLineCount = 5;
        
        /**
         * 噪点数量
         */
        private Integer noisePointCount = 50;
        
        /**
         * 是否启用字符旋转
         */
        private Boolean enableRotation = true;
        
        /**
         * 字符旋转角度范围（度）
         */
        private Integer rotationRange = 15;
    }

    /**
     * 缓存配置类
     */
    @Data
    public static class CacheConfig {
        
        /**
         * 验证码过期时间（分钟）
         */
        private Integer expireMinutes = 5;
        
        /**
         * 缓存键前缀
         */
        private String keyPrefix = "captcha:";
        
        /**
         * 是否启用缓存
         */
        private Boolean enabled = true;
        
        /**
         * 最大验证次数
         */
        private Integer maxVerifyCount = 3;
    }

    /**
     * 安全配置类
     */
    @Data
    public static class SecurityConfig {
        
        /**
         * 是否忽略大小写
         */
        private Boolean ignoreCase = true;
        
        /**
         * 是否启用防暴力破解
         */
        private Boolean enableAntibrute = true;
        
        /**
         * 同一IP最大生成次数（每分钟）
         */
        private Integer maxGeneratePerMinute = 10;
        
        /**
         * 同一IP最大验证次数（每分钟）
         */
        private Integer maxVerifyPerMinute = 20;
        
        /**
         * 是否记录验证日志
         */
        private Boolean enableLogging = true;
    }

    /**
     * 验证配置是否有效
     * 
     * @return 配置验证结果
     */
    public boolean isValid() {
        if (!enabled) {
            return true; // 如果禁用，则不需要验证配置
        }
        
        // 验证默认类型
        if (defaultType == null || defaultType.trim().isEmpty()) {
            return false;
        }
        
        String[] validTypes = {"NUMBER", "LETTER", "MIXED", "ARITHMETIC", "RANDOM"};
        boolean validType = false;
        for (String type : validTypes) {
            if (type.equals(defaultType.toUpperCase())) {
                validType = true;
                break;
            }
        }
        if (!validType) {
            return false;
        }
        
        // 验证长度
        if (defaultLength == null || defaultLength <= 0 || defaultLength > 20) {
            return false;
        }
        
        // 验证图片尺寸
        if (image.width == null || image.width <= 0 || image.width > 1000) {
            return false;
        }
        if (image.height == null || image.height <= 0 || image.height > 500) {
            return false;
        }
        
        // 验证过期时间
        if (cache.expireMinutes == null || cache.expireMinutes <= 0 || cache.expireMinutes > 60) {
            return false;
        }
        
        return true;
    }

    /**
     * 获取完整的缓存键
     * 
     * @param captchaId 验证码ID
     * @return 完整的缓存键
     */
    public String getCacheKey(String captchaId) {
        return cache.keyPrefix + captchaId;
    }

    /**
     * 获取防暴力破解缓存键
     * 
     * @param ip IP地址
     * @param action 操作类型（generate/verify）
     * @return 防暴力破解缓存键
     */
    public String getAntibruteKey(String ip, String action) {
        return cache.keyPrefix + "antibrute:" + action + ":" + ip;
    }

    /**
     * 重置为默认配置
     */
    public void resetToDefault() {
        this.enabled = true;
        this.defaultType = "NUMBER";
        this.defaultLength = 4;
        this.image = new ImageConfig();
        this.cache = new CacheConfig();
        this.security = new SecurityConfig();
    }

    /**
     * 获取配置摘要信息
     * 
     * @return 配置摘要
     */
    public String getConfigSummary() {
        return String.format(
            "CaptchaConfig[enabled=%s, type=%s, length=%d, size=%dx%d, expire=%dmin]",
            enabled, defaultType, defaultLength, 
            image.width, image.height, cache.expireMinutes
        );
    }
}
