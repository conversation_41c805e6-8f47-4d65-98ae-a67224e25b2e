package com.akey.framework.core.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 验证码自动配置类
 * 
 * <p>自动配置验证码相关的组件</p>
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@Configuration
@ConditionalOnProperty(prefix = "akey.captcha", name = "enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties(CaptchaProperties.class)
public class CaptchaAutoConfiguration {

    public CaptchaAutoConfiguration() {
        log.info("验证码自动配置已启用");
    }
}
