package com.akey.framework.core.redis.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Redis配置属性类
 * 
 * <p>功能说明：</p>
 * <ul>
 *   <li>自定义Redis配置属性</li>
 *   <li>支持缓存相关配置</li>
 *   <li>支持分布式锁配置</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@ConfigurationProperties(prefix = "akey.redis")
public class RedisProperties {

    /**
     * 是否启用Redis
     */
    private boolean enabled = true;

    /**
     * 缓存配置
     */
    private Cache cache = new Cache();

    /**
     * 分布式锁配置
     */
    private Lock lock = new Lock();

    /**
     * 缓存配置
     */
    @Data
    public static class Cache {
        
        /**
         * 默认缓存过期时间(秒)
         */
        private long defaultExpireTime = 3600;
        
        /**
         * 空值缓存时间(秒)
         */
        private long nullCacheTime = 300;
        
        /**
         * 缓存key前缀
         */
        private String keyPrefix = "akey:cache:";
        
        /**
         * 是否启用缓存穿透保护
         */
        private boolean enableNullCache = true;
    }

    /**
     * 分布式锁配置
     */
    @Data
    public static class Lock {
        
        /**
         * 默认锁过期时间(秒)
         */
        private long defaultExpireTime = 30;
        
        /**
         * 锁key前缀
         */
        private String keyPrefix = "akey:lock:";
        
        /**
         * 获取锁的重试次数
         */
        private int retryCount = 3;
        
        /**
         * 获取锁的重试间隔(毫秒)
         */
        private long retryInterval = 100;
    }
}
