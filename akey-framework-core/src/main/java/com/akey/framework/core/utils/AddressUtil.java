package com.akey.framework.core.utils;

import com.akey.common.service.AddressResolveService;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.TimeUnit;

/**
 * 地址信息获取工具类
 * 
 * <p>通过IP地址获取地理位置信息</p>
 * <p>支持多种IP地址查询服务，使用Redis缓存</p>
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AddressUtil implements AddressResolveService {

    private final RedisTemplate<String, Object> redisTemplate;

    /**
     * IP地址查询URL - 太平洋电脑网
     */
    public static final String PCONLINE_URL = "http://whois.pconline.com.cn/ipJson.jsp";

    /**
     * IP地址查询URL - 淘宝IP库（备用）
     */
    public static final String TAOBAO_URL = "https://ip.taobao.com/outGetIpInfo";

    /**
     * 未知地址
     */
    public static final String UNKNOWN = "XX XX";

    /**
     * 内网IP标识
     */
    public static final String INTERNAL_IP = "内网IP";

    /**
     * 本地IP标识
     */
    public static final String LOCAL_IP = "本地IP";

    /**
     * 请求超时时间（毫秒）
     */
    private static final int TIMEOUT = 5000;

    /**
     * Redis缓存键前缀
     */
    private static final String CACHE_KEY_PREFIX = "address:ip:";

    /**
     * 缓存过期时间 - 1小时
     */
    private static final long CACHE_EXPIRE_TIME = 1;



    /**
     * 根据IP地址获取真实地址信息
     *
     * @param ip IP地址
     * @return 地址信息字符串，格式：省份 城市
     */
    public String getRealAddressByIP(String ip) {
        return getLocationByIP(ip);
    }

    /**
     * 根据IP地址获取地理位置信息（实现AddressResolveService接口）
     *
     * @param ip IP地址
     * @return 地理位置信息字符串
     */
    @Override
    public String getLocationByIP(String ip) {
        if (!StringUtils.hasText(ip)) {
            log.warn("IP地址为空");
            return UNKNOWN;
        }

        // 检查是否为内网IP
        if (isInternalIp(ip)) {
            return "本机";
        }

        // 检查是否为本地IP
        if (isLocalIp(ip)) {
            return "本机";
        }

        // 先从Redis缓存中获取
        String cachedLocation = getLocationFromCache(ip);
        if (StringUtils.hasText(cachedLocation)) {
            log.debug("从Redis缓存获取IP地址信息: {}", ip);
            return cachedLocation;
        }

        // 缓存中没有，重新查询
        String location = queryLocationFromRemote(ip);

        // 将结果放入Redis缓存
        if (StringUtils.hasText(location)) {
            putLocationToCache(ip, location);
        }

        return location != null ? location : UNKNOWN;
    }

    /**
     * 从远程服务查询地理位置信息
     *
     * @param ip IP地址
     * @return 地理位置字符串
     */
    private String queryLocationFromRemote(String ip) {
        // 首先尝试太平洋电脑网接口
        String location = queryLocationFromPconline(ip);

        // 如果太平洋接口失败，尝试淘宝接口（备用）
        if (!StringUtils.hasText(location)) {
            location = queryLocationFromTaobao(ip);
        }

        return StringUtils.hasText(location) ? location : UNKNOWN;
    }

    /**
     * 从太平洋电脑网查询地理位置信息
     *
     * @param ip IP地址
     * @return 地理位置字符串
     */
    private String queryLocationFromPconline(String ip) {
        try {
            String url = PCONLINE_URL + "?ip=" + ip + "&json=true";
            String response = sendHttpGet(url, "GBK");

            if (!StringUtils.hasText(response)) {
                return null;
            }

            JSONObject jsonObject = JSON.parseObject(response);
            if (jsonObject != null) {
                String addr = jsonObject.getString("addr");

                log.debug("太平洋接口查询成功, ip: {}, addr: {}", ip, addr);

                return StringUtils.hasText(addr) ? addr : null;
            }

        } catch (Exception e) {
            log.warn("太平洋接口查询失败, ip: {}, error: {}", ip, e.getMessage());
        }

        return null;
    }



    /**
     * 从淘宝IP库查询地理位置信息（备用）
     *
     * @param ip IP地址
     * @return 地理位置字符串
     */
    private String queryLocationFromTaobao(String ip) {
        try {
            String url = TAOBAO_URL + "?ip=" + ip + "&accessKey=alibaba-inc";
            String response = sendHttpGet(url, "UTF-8");

            if (!StringUtils.hasText(response)) {
                log.warn("淘宝接口返回空响应, ip: {}", ip);
                return null;
            }

            JSONObject jsonObject = JSON.parseObject(response);
            JSONObject data = jsonObject.getJSONObject("data");

            if (data != null) {
                String country = data.getString("country");
                String province = data.getString("region");
                String city = data.getString("city");



                String local = buildLocationString(country, province, city).replaceAll("XX", "").trim();
                log.debug("淘宝接口查询成功, ip: {}, local: {}",
                        ip, local);
                return local;
            }

        } catch (Exception e) {
            log.warn("淘宝接口查询失败, ip: {}, error: {}", ip, e.getMessage());
        }

        return null;
    }

    /**
     * 从Redis缓存中获取地理位置信息
     *
     * @param ip IP地址
     * @return 地理位置字符串，如果不存在则返回null
     */
    private String getLocationFromCache(String ip) {
        try {
            String cacheKey = CACHE_KEY_PREFIX + ip;
            Object cached = redisTemplate.opsForValue().get(cacheKey);

            if (cached != null) {
                return cached.toString();
            }
        } catch (Exception e) {
            log.warn("从Redis缓存获取IP地址信息失败, ip: {}, error: {}", ip, e.getMessage());
        }

        return null;
    }

    /**
     * 将地理位置信息存入Redis缓存
     *
     * @param ip IP地址
     * @param location 地理位置字符串
     */
    private void putLocationToCache(String ip, String location) {
        try {
            String cacheKey = CACHE_KEY_PREFIX + ip;
            redisTemplate.opsForValue().set(cacheKey, location, CACHE_EXPIRE_TIME, TimeUnit.HOURS);
            log.debug("IP地址信息已存入Redis缓存, ip: {}, key: {}, location: {}", ip, cacheKey, location);
        } catch (Exception e) {
            log.warn("将IP地址信息存入Redis缓存失败, ip: {}, error: {}", ip, e.getMessage());
        }
    }

    /**
     * 清空指定IP的缓存
     * 
     * @param ip IP地址
     */
    public void clearCache(String ip) {
        try {
            String cacheKey = CACHE_KEY_PREFIX + ip;
            redisTemplate.delete(cacheKey);
            log.debug("清空IP地址缓存完成, ip: {}", ip);
        } catch (Exception e) {
            log.warn("清空IP地址缓存失败, ip: {}, error: {}", ip, e.getMessage());
        }
    }

    /**
     * 发送HTTP GET请求
     * 
     * @param urlString 请求URL
     * @param charset 字符编码
     * @return 响应内容
     */
    private String sendHttpGet(String urlString, String charset) {
        HttpURLConnection connection = null;
        BufferedReader reader = null;
        
        try {
            URL url = new URL(urlString);
            connection = (HttpURLConnection) url.openConnection();
            
            // 设置请求属性
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(TIMEOUT);
            connection.setReadTimeout(TIMEOUT);
            connection.setRequestProperty("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            
            // 读取响应
            reader = new BufferedReader(new InputStreamReader(
                connection.getInputStream(), charset));
            
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            
            return response.toString();
            
        } catch (Exception e) {
            log.warn("HTTP请求失败, url: {}, error: {}", urlString, e.getMessage());
            return null;
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (Exception e) {
                    log.warn("关闭BufferedReader失败", e);
                }
            }
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    /**
     * 判断是否为内网IP
     */
    private boolean isInternalIp(String ip) {
        if (!StringUtils.hasText(ip)) {
            return false;
        }
        
        return ip.startsWith("10.") || 
               ip.startsWith("192.168.") ||
               (ip.startsWith("172.") && isInRange172(ip));
    }

    /**
     * 判断172网段是否为内网IP
     */
    private boolean isInRange172(String ip) {
        String[] parts = ip.split("\\.");
        if (parts.length >= 2) {
            try {
                int second = Integer.parseInt(parts[1]);
                return second >= 16 && second <= 31;
            } catch (NumberFormatException e) {
                return false;
            }
        }
        return false;
    }

    /**
     * 判断是否为本地IP
     */
    private boolean isLocalIp(String ip) {
        return "127.0.0.1".equals(ip) || "localhost".equals(ip) || "0:0:0:0:0:0:0:1".equals(ip);
    }

    /**
     * 构建地理位置字符串
     *
     * @param country 国家
     * @param province 省份
     * @param city 城市
     * @return 地理位置字符串
     */
    private String buildLocationString(String country, String province, String city) {
        StringBuilder sb = new StringBuilder();

        if (StringUtils.hasText(country) && !UNKNOWN.equals(country)) {
            sb.append(country);
        }

        if (StringUtils.hasText(province) && !UNKNOWN.equals(province)) {
            if (sb.length() > 0) sb.append(" ");
            sb.append(province);
        }

        if (StringUtils.hasText(city) && !UNKNOWN.equals(city) && !city.equals(province)) {
            if (sb.length() > 0) sb.append(" ");
            sb.append(city);
        }

        return sb.length() > 0 ? sb.toString() : UNKNOWN;
    }
}
