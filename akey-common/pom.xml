<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <!--
        继承父项目配置 - 从父项目继承依赖管理和插件配置
    -->
    <parent>
        <groupId>com.akey</groupId>
        <artifactId>akey</artifactId>
        <version>0.0.1-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    
    <!--
        公共模块配置
        模块作用：提供项目中通用的工具类、常量定义、异常定义、通用配置
        被其他所有模块依赖，不依赖任何业务模块
    -->
    <artifactId>akey-common</artifactId>
    <name>akey-common</name>
    <description>公共模块 - 提供通用工具类、常量、异常定义等公共组件</description>
    
    <!-- 
        依赖配置
        公共模块只包含最基础的依赖，避免传递给其他模块不必要的依赖
    -->
    <dependencies>
        <!-- 
            Lombok依赖 - 用于简化实体类代码
            提供@Data、@Builder等注解，减少样板代码
        -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <!-- 单独引入jackson注解依赖 -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>

        <!-- MyBatis-Plus 注解依赖 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-annotation</artifactId>
            <optional>true</optional>
        </dependency>

         <!-- 阿里JSON解析器 -->
         <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
        </dependency>

        <!-- BCrypt密码加密库 -->
        <dependency>
            <groupId>at.favre.lib</groupId>
            <artifactId>bcrypt</artifactId>
        </dependency>
    </dependencies>
    
    <!-- 
        构建配置
        继承父项目的插件配置，无需额外配置
    -->
    <build>
        <plugins>
            <!-- Maven编译插件 - 继承父配置 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project> 
