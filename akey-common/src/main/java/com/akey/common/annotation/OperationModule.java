package com.akey.common.annotation;

import java.lang.annotation.*;

/**
 * 操作模块注解
 * 
 * <p>用于标注在Controller类上，定义操作模块名称</p>
 * <p>该注解的值将作为操作日志中的operation_module字段</p>
 * <p>支持类级别注解，为该类下所有标注了@OperationLog的方法提供模块信息</p>
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperationModule {

    /**
     * 操作模块名称
     * 
     * <p>用于标识操作所属的业务模块，如：</p>
     * <ul>
     *   <li>用户管理</li>
     *   <li>角色管理</li>
     *   <li>菜单管理</li>
     *   <li>系统管理</li>
     * </ul>
     * 
     * @return 模块名称
     */
    String value();

    /**
     * 模块描述
     * 
     * <p>对模块的详细描述，可选字段</p>
     * 
     * @return 模块描述
     */
    String description() default "";
}
