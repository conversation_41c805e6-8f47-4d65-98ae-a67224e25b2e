package com.akey.common.annotation;

import com.akey.common.enums.OperationType;

import java.lang.annotation.*;

/**
 * 操作日志注解
 * 
 * <p>用于标注在Controller方法上，标记该接口需要记录操作日志</p>
 * <p>配合@OperationModule注解使用，自动记录用户操作行为</p>
 * <p>支持异步记录，不影响主业务性能</p>
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperationLog {

    /**
     * 操作描述
     * 
     * <p>描述具体的操作内容，如：</p>
     * <ul>
     *   <li>创建用户</li>
     *   <li>删除角色</li>
     *   <li>修改菜单</li>
     *   <li>查询用户列表</li>
     * </ul>
     * 
     * @return 操作描述
     */
    String value();

    /**
     * 操作描述（别名）
     * 
     * <p>与value()功能相同，提供更语义化的属性名</p>
     * 
     * @return 操作描述
     */
    String description() default "";

    /**
     * 操作类型
     * 
     * <p>使用OperationType枚举定义操作类型：</p>
     * <ul>
     *   <li>CREATE - 新增操作</li>
     *   <li>UPDATE - 修改操作</li>
     *   <li>DELETE - 删除操作</li>
     *   <li>QUERY - 查询操作</li>
     *   <li>LOGIN - 登录操作</li>
     *   <li>LOGOUT - 登出操作</li>
     *   <li>EXPORT - 导出操作</li>
     *   <li>IMPORT - 导入操作</li>
     * </ul>
     * 
     * @return 操作类型
     */
    OperationType type();

    /**
     * 是否记录请求参数
     * 
     * <p>控制是否将请求参数记录到操作日志中</p>
     * <p>注意：敏感参数会自动脱敏处理</p>
     * 
     * @return true-记录请求参数，false-不记录
     */
    boolean recordParams() default true;

    /**
     * 是否记录响应结果
     *
     * <p>控制是否将响应结果记录到操作日志中</p>
     * <p>默认为true，记录响应状态码、响应消息等基本信息</p>
     * <p>注意：响应结果会自动脱敏处理，敏感字段会被过滤</p>
     *
     * @return true-记录响应结果，false-不记录
     */
    boolean recordResult() default true;

    /**
     * 是否异步记录
     * 
     * <p>控制操作日志的记录方式：</p>
     * <ul>
     *   <li>true - 异步记录，不影响主业务性能（推荐）</li>
     *   <li>false - 同步记录，确保日志记录完成后才返回</li>
     * </ul>
     * 
     * @return true-异步记录，false-同步记录
     */
    boolean async() default true;

    /**
     * 忽略的参数名称
     * 
     * <p>指定不需要记录的请求参数名称，支持敏感信息过滤</p>
     * <p>常见的敏感参数：password、token、secret等</p>
     * 
     * @return 忽略的参数名称数组
     */
    String[] ignoreParams() default {"password", "oldPassword", "newPassword", "token", "secret", "key"};

    /**
     * 忽略的响应字段
     * 
     * <p>指定不需要记录的响应字段名称，支持敏感信息过滤</p>
     * <p>常见的敏感字段：password、token、secret等</p>
     * 
     * @return 忽略的响应字段名称数组
     */
    String[] ignoreResult() default {"password", "token", "secret", "key"};

    /**
     * 操作失败时是否记录日志
     * 
     * <p>控制当操作执行失败（抛出异常）时是否仍然记录操作日志</p>
     * 
     * @return true-失败时也记录，false-失败时不记录
     */
    boolean recordOnFailure() default true;

    /**
     * 自定义操作模块
     * 
     * <p>如果指定此值，将覆盖类级别@OperationModule注解的值</p>
     * <p>用于特殊情况下需要为单个方法指定不同模块的场景</p>
     * 
     * @return 自定义操作模块名称
     */
    String module() default "";
}
