package com.akey.common;

import com.akey.common.enums.ResultCode;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 统一响应结果类
 * 用于封装所有API接口的响应数据格式
 * 
 * @param <T> 响应数据类型
 * <AUTHOR>
 * @since 2025-07-02
 */
@Data
@NoArgsConstructor
public class Result<T> implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 响应状态码
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 响应时间戳
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime timestamp;
    
    /**
     * 请求是否成功
     */
    private Boolean success;
    
    /**
     * 私有构造方法
     *
     * @param code      状态码
     * @param message   消息
     * @param data      数据
     * @param success   是否成功
     */
    private Result(Integer code, String message, T data, Boolean success) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.success = success;
        this.timestamp = LocalDateTime.now();
    }
    
    /**
     * 成功响应 - 无数据
     *
     * @param <T> 泛型类型
     * @return Result<T>
     */
    public static <T> Result<T> success() {
        return new Result<>(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), null, true);
    }
    
    /**
     * 成功响应 - 有数据
     *
     * @param data 响应数据
     * @param <T>  泛型类型
     * @return Result<T>
     */
    public static <T> Result<T> success(T data) {
        return new Result<>(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), data, true);
    }
    
    /**
     * 成功响应 - 自定义消息
     *
     * @param message 自定义消息
     * @param <T>     泛型类型
     * @return Result<T>
     */
    public static <T> Result<T> success(String message) {
        return new Result<>(ResultCode.SUCCESS.getCode(), message, null, true);
    }
    
    /**
     * 成功响应 - 自定义消息和数据
     *
     * @param message 自定义消息
     * @param data    响应数据
     * @param <T>     泛型类型
     * @return Result<T>
     */
    public static <T> Result<T> success(String message, T data) {
        return new Result<>(ResultCode.SUCCESS.getCode(), message, data, true);
    }
    
    /**
     * 失败响应 - 使用默认错误信息
     *
     * @param <T> 泛型类型
     * @return Result<T>
     */
    public static <T> Result<T> error() {
        return new Result<>(ResultCode.INTERNAL_SERVER_ERROR.getCode(), 
                          ResultCode.INTERNAL_SERVER_ERROR.getMessage(), null, false);
    }
    
    /**
     * 失败响应 - 自定义错误消息
     *
     * @param message 错误消息
     * @param <T>     泛型类型
     * @return Result<T>
     */
    public static <T> Result<T> error(String message) {
        return new Result<>(ResultCode.INTERNAL_SERVER_ERROR.getCode(), message, null, false);
    }
    
    /**
     * 失败响应 - 使用状态码枚举
     *
     * @param resultCode 状态码枚举
     * @param <T>        泛型类型
     * @return Result<T>
     */
    public static <T> Result<T> error(ResultCode resultCode) {
        return new Result<>(resultCode.getCode(), resultCode.getMessage(), null, false);
    }
    
    /**
     * 失败响应 - 使用状态码枚举和自定义消息
     *
     * @param resultCode 状态码枚举
     * @param message    自定义消息
     * @param <T>        泛型类型
     * @return Result<T>
     */
    public static <T> Result<T> error(ResultCode resultCode, String message) {
        return new Result<>(resultCode.getCode(), message, null, false);
    }
    
    /**
     * 失败响应 - 完全自定义
     *
     * @param code    状态码
     * @param message 错误消息
     * @param <T>     泛型类型
     * @return Result<T>
     */
    public static <T> Result<T> error(Integer code, String message) {
        return new Result<>(code, message, null, false);
    }
    
    /**
     * 自定义响应
     *
     * @param code    状态码
     * @param message 消息
     * @param data    数据
     * @param success 是否成功
     * @param <T>     泛型类型
     * @return Result<T>
     */
    public static <T> Result<T> build(Integer code, String message, T data, Boolean success) {
        return new Result<>(code, message, data, success);
    }
    
    /**
     * 根据状态码枚举构建响应
     *
     * @param resultCode 状态码枚举
     * @param data       数据
     * @param <T>        泛型类型
     * @return Result<T>
     */
    public static <T> Result<T> build(ResultCode resultCode, T data) {
        return new Result<>(resultCode.getCode(), resultCode.getMessage(), data, 
                          ResultCode.SUCCESS.equals(resultCode));
    }
    
    /**
     * 判断响应是否成功
     *
     * @return 是否成功
     */
    public boolean isSuccess() {
        return this.success != null && this.success;
    }

} 