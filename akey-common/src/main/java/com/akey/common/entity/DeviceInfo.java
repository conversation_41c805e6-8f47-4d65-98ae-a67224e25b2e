package com.akey.common.entity;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;

/**
 * 设备信息实体类
 * 
 * <p>用于存储客户端设备的详细信息</p>
 * <p>包含IP地址、浏览器、操作系统、设备类型等信息</p>
 * <p>主要用于用户行为分析、安全监控和审计日志</p>
 * 
 * <AUTHOR>
 * @since 2025-07-05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户端IP
     */
    private String clientIp;


    /**
     * 代理IP地址
     *
     * <p>如果客户端使用了代理，记录代理服务器的IP</p>
     * <p>可能为空，表示未使用代理</p>
     */
    private String proxyIp;

    /**
     * 原始User-Agent字符串
     * 
     * <p>浏览器发送的完整User-Agent信息</p>
     * <p>用于设备指纹识别和安全分析</p>
     */
    private String userAgent;

    /**
     * 操作系统名称
     * 
     * <p>如：Windows 10、macOS、Android、iOS等</p>
     */
    private String operatingSystem;

    /**
     * 操作系统版本
     * 
     * <p>操作系统的具体版本号</p>
     */
    private String osVersion;

    /**
     * 浏览器名称
     * 
     * <p>如：Chrome、Firefox、Safari、Edge等</p>
     */
    private String browserName;

    /**
     * 浏览器版本
     * 
     * <p>浏览器的具体版本号</p>
     */
    private String browserVersion;

    /**
     * 设备类型
     * 
     * <p>如：Desktop、Mobile、Tablet、Bot等</p>
     */
    private String deviceType;

    /**
     * 设备品牌
     * 
     * <p>如：Apple、Samsung、Huawei等</p>
     * <p>主要针对移动设备</p>
     */
    private String deviceBrand;

    /**
     * 设备型号
     * 
     * <p>具体的设备型号信息</p>
     * <p>如：iPhone 13、Galaxy S21等</p>
     */
    private String deviceModel;

    /**
     * 屏幕分辨率
     * 
     * <p>设备屏幕的分辨率信息</p>
     * <p>格式：宽x高，如：1920x1080</p>
     */
    private String screenResolution;

    /**
     * 语言设置
     * 
     * <p>客户端的语言偏好设置</p>
     * <p>如：zh-CN、en-US等</p>
     */
    private String language;

    /**
     * 时区信息
     * 
     * <p>客户端所在的时区</p>
     * <p>如：Asia/Shanghai、America/New_York等</p>
     */
    private String timezone;

    /**
     * 是否为移动设备
     * 
     * <p>标识是否为移动设备（手机、平板）</p>
     */
    private Boolean isMobile;

    /**
     * 是否为机器人/爬虫
     * 
     * <p>标识是否为搜索引擎爬虫或其他自动化程序</p>
     */
    private Boolean isBot;

    /**
     * 是否使用代理
     * 
     * <p>标识客户端是否通过代理服务器访问</p>
     */
    private Boolean isProxy;

    /**
     * 地点
     */
    private String location;



    /**
     * 设备指纹
     * 
     * <p>基于多个设备特征生成的唯一标识</p>
     * <p>用于设备识别和风险控制</p>
     */
    private String deviceFingerprint;

    /**
     * 风险等级
     * 
     * <p>基于设备信息评估的风险等级</p>
     * <p>如：LOW、MEDIUM、HIGH</p>
     */
    private String riskLevel;

    /**
     * token
     */
    private String token;

    /**
     * 获取设备描述信息
     * 
     * @return 设备的简要描述
     */
    public String getDeviceDescription() {
        StringBuilder description = new StringBuilder();
        
        if (deviceType != null) {
            description.append(deviceType);
        }
        
        if (operatingSystem != null) {
            if (description.length() > 0) {
                description.append(" - ");
            }
            description.append(operatingSystem);
            if (osVersion != null) {
                description.append(" ").append(osVersion);
            }
        }
        
        if (browserName != null) {
            if (description.length() > 0) {
                description.append(" - ");
            }
            description.append(browserName);
            if (browserVersion != null) {
                description.append(" ").append(browserVersion);
            }
        }
        
        return description.toString();
    }

}
