package com.akey.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 验证码结果实体
 * 
 * <p>功能说明：</p>
 * <ul>
 *   <li>封装验证码生成的结果数据</li>
 *   <li>包含验证码答案和Base64编码的图片数据</li>
 *   <li>支持不同类型验证码的统一返回格式</li>
 * </ul>
 * 
 * <p>使用示例：</p>
 * <pre>
 * CaptchaResult result = CaptchaResult.builder()
 *     .answer("1234")
 *     .imageBase64("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...")
 *     .build();
 * </pre>
 *
 * <AUTHOR>
 * @since 2025-07-06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CaptchaResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 验证码ID
     *
     * <p>用于标识验证码的唯一ID，前端需要保存此ID用于后续验证</p>
     */
    private String captchaId;

    /**
     * 验证码答案
     *
     * <p>说明：</p>
     * <ul>
     *   <li>对于字符类型验证码：存储生成的字符串</li>
     *   <li>对于算式类型验证码：存储计算结果</li>
     *   <li>验证时需要将用户输入与此答案进行比较</li>
     *   <li>注意：返回给前端时不应包含此字段</li>
     * </ul>
     */
    private String answer;

    /**
     * Base64编码的验证码图片
     * 
     * <p>格式：data:image/png;base64,{base64数据}</p>
     * <p>可以直接在前端img标签的src属性中使用</p>
     */
    private String imageBase64;

    /**
     * 验证码表达式（仅算式类型使用）
     * 
     * <p>对于算式类型验证码，此字段存储显示的数学表达式</p>
     * <p>例如：3 + 5 = ?</p>
     */
    private String expression;

    /**
     * 验证码类型
     * 
     * <p>标识当前验证码的生成类型</p>
     */
    private String type;

    /**
     * 验证码长度
     * 
     * <p>对于字符类型验证码，表示字符个数</p>
     * <p>对于算式类型验证码，此字段无意义</p>
     */
    private Integer length;

    /**
     * 创建时间戳
     * 
     * <p>用于验证码过期时间判断</p>
     */
    private Long timestamp;

    /**
     * 创建纯答案结果（用于测试或简单场景）
     * 
     * @param answer 验证码答案
     * @return 验证码结果对象
     */
    public static CaptchaResult ofAnswer(String answer) {
        return CaptchaResult.builder()
                .answer(answer)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 创建完整结果
     * 
     * @param answer 验证码答案
     * @param imageBase64 Base64图片数据
     * @param type 验证码类型
     * @return 验证码结果对象
     */
    public static CaptchaResult of(String answer, String imageBase64, String type) {
        return CaptchaResult.builder()
                .answer(answer)
                .imageBase64(imageBase64)
                .type(type)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 创建算式类型结果
     * 
     * @param answer 计算结果
     * @param expression 数学表达式
     * @param imageBase64 Base64图片数据
     * @return 验证码结果对象
     */
    public static CaptchaResult ofArithmetic(String answer, String expression, String imageBase64) {
        return CaptchaResult.builder()
                .answer(answer)
                .expression(expression)
                .imageBase64(imageBase64)
                .type("ARITHMETIC")
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 判断验证码是否过期
     * 
     * @param expireMinutes 过期时间（分钟）
     * @return true:已过期，false:未过期
     */
    public boolean isExpired(int expireMinutes) {
        if (timestamp == null) {
            return true;
        }
        
        long expireTime = timestamp + (expireMinutes * 60 * 1000L);
        return System.currentTimeMillis() > expireTime;
    }

    /**
     * 验证用户输入
     * 
     * @param userInput 用户输入的验证码
     * @param ignoreCase 是否忽略大小写
     * @return true:验证通过，false:验证失败
     */
    public boolean verify(String userInput, boolean ignoreCase) {
        if (userInput == null || answer == null) {
            return false;
        }
        
        if (ignoreCase) {
            return answer.equalsIgnoreCase(userInput.trim());
        } else {
            return answer.equals(userInput.trim());
        }
    }

    /**
     * 验证用户输入（默认忽略大小写）
     * 
     * @param userInput 用户输入的验证码
     * @return true:验证通过，false:验证失败
     */
    public boolean verify(String userInput) {
        return verify(userInput, true);
    }
}
