package com.akey.common.entity;

import com.akey.common.enums.DeletedStatusEnum;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 基础实体类
 * 
 * <p>功能说明：</p>
 * <ul>
 *   <li>提供所有实体的公共字段：id、创建时间、更新时间、逻辑删除标记</li>
 *   <li>配置MyBatis-Plus注解，支持自动填充和逻辑删除</li>
 *   <li>实现Serializable接口，支持序列化</li>
 * </ul>
 * 
 * <p>字段说明：</p>
 * <ul>
 *   <li>id - 主键，使用雪花算法自动生成</li>
 *   <li>createTime - 创建时间，插入时自动填充</li>
 *   <li>updateTime - 更新时间，插入和更新时自动填充</li>
 *   <li>deleted - 逻辑删除标记，0=未删除，1=已删除</li>
 *   <li>version - 乐观锁版本号，用于防止并发更新</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public abstract class BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     * 使用UUID算法自动生成唯一ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 创建时间
     * 插入时自动填充当前时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     * 插入时自动填充当前用户
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     * 插入和更新时自动填充当前时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     * 插入和更新时自动填充当前用户
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 逻辑删除标记
     * 0=未删除，1=已删除
     * 配置逻辑删除后，删除操作会自动转换为更新操作
     */
    @TableLogic
    @TableField(value = "deleted")
    private DeletedStatusEnum deleted;

    /**
     * 乐观锁版本号
     * 用于防止并发更新问题
     * 每次更新时版本号会自动+1
     */
    @Version
    @TableField(value = "version")
    private Integer version;
} 