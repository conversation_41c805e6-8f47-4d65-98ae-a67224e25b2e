package com.akey.common.service;

import com.akey.common.enums.OperationStatus;
import com.akey.common.enums.OperationType;

import java.time.LocalDateTime;

/**
 * 操作日志记录服务接口
 * 
 * <p>定义操作日志记录的标准接口，用于解耦web层和dao层</p>
 * <p>web层负责收集上下文信息，dao层负责数据持久化</p>
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface OperationLogRecordService {

    /**
     * 异步记录操作日志
     * 
     * @param logInfo 操作日志信息
     */
    void recordOperationLogAsync(OperationLogInfo logInfo);

    /**
     * 同步记录操作日志
     * 
     * @param logInfo 操作日志信息
     * @return 记录是否成功
     */
    boolean recordOperationLogSync(OperationLogInfo logInfo);

    /**
     * 操作日志信息数据传输对象
     * 
     * <p>用于在web层和dao层之间传递操作日志信息</p>
     * <p>避免dao层直接依赖web相关的类</p>
     */
    class OperationLogInfo {
        
        // 基本信息
        private String module;
        private OperationType type;
        private String description;
        private OperationStatus status;
        private String errorMessage;
        private Long executionTime;
        private LocalDateTime operationTime;

        // 用户信息
        private String userId;
        private String username;

        // 请求信息
        private String requestMethod;
        private String requestUrl;
        private Object requestParams;

        // 响应信息
        private Object responseResult;
        private Integer responseStatus;
        private String responseMessage;

        // 系统信息
        private String clientIp;
        private String userAgent;

        // 地理位置信息
        private String location;

        // 用于获取上下文
        private String token;

        // 构造方法
        public OperationLogInfo() {
            this.operationTime = LocalDateTime.now();
        }

        // Getter和Setter方法
        public String getModule() {
            return module;
        }

        public void setModule(String module) {
            this.module = module;
        }

        public OperationType getType() {
            return type;
        }

        public void setType(OperationType type) {
            this.type = type;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public OperationStatus getStatus() {
            return status;
        }

        public void setStatus(OperationStatus status) {
            this.status = status;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public Long getExecutionTime() {
            return executionTime;
        }

        public void setExecutionTime(Long executionTime) {
            this.executionTime = executionTime;
        }

        public LocalDateTime getOperationTime() {
            return operationTime;
        }

        public void setOperationTime(LocalDateTime operationTime) {
            this.operationTime = operationTime;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getRequestMethod() {
            return requestMethod;
        }

        public void setRequestMethod(String requestMethod) {
            this.requestMethod = requestMethod;
        }

        public String getRequestUrl() {
            return requestUrl;
        }

        public void setRequestUrl(String requestUrl) {
            this.requestUrl = requestUrl;
        }

        public Object getRequestParams() {
            return requestParams;
        }

        public void setRequestParams(Object requestParams) {
            this.requestParams = requestParams;
        }

        public Object getResponseResult() {
            return responseResult;
        }

        public void setResponseResult(Object responseResult) {
            this.responseResult = responseResult;
        }

        public Integer getResponseStatus() {
            return responseStatus;
        }

        public void setResponseStatus(Integer responseStatus) {
            this.responseStatus = responseStatus;
        }

        public String getResponseMessage() {
            return responseMessage;
        }

        public void setResponseMessage(String responseMessage) {
            this.responseMessage = responseMessage;
        }

        public String getClientIp() {
            return clientIp;
        }

        public void setClientIp(String clientIp) {
            this.clientIp = clientIp;
        }

        public String getUserAgent() {
            return userAgent;
        }

        public void setUserAgent(String userAgent) {
            this.userAgent = userAgent;
        }

        public String getLocation() {
            return location;
        }

        public void setLocation(String location) {
            this.location = location;
        }

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }
    }
}
