package com.akey.common.service;

/**
 * 谷歌验证码用户信息服务接口
 * 
 * <p>用于获取用户的谷歌验证器相关信息</p>
 * <p>由具体的业务模块实现，供Web框架层调用</p>
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
public interface GoogleAuthUserService {

    /**
     * 获取用户的谷歌验证器密钥
     * 
     * @param userId 用户ID
     * @return 谷歌验证器密钥，如果用户不存在返回null，如果用户未设置返回空字符串
     */
    String getUserGoogleAuthKey(String userId);

    /**
     * 检查用户是否存在
     * 
     * @param userId 用户ID
     * @return 用户是否存在
     */
    boolean userExists(String userId);

    /**
     * 检查用户是否已设置谷歌验证器
     * 
     * @param userId 用户ID
     * @return 是否已设置谷歌验证器
     */
    boolean hasGoogleAuthSetup(String userId);
}
