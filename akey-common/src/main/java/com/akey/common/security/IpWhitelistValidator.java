package com.akey.common.security;

import com.akey.common.enums.IpWhitelistTypeEnum;

/**
 * IP白名单验证器接口
 *
 * <p>用于验证IP是否在白名单中</p>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
public interface IpWhitelistValidator {

    /**
     * 验证IP是否在白名单中
     * @param type 白名单类型
     * @param userId 用户ID
     * @param clientIp 客户端IP
     * @return true:验证通过，false:验证失败
     */
    boolean isIpAllowed(IpWhitelistTypeEnum type, String userId, String clientIp);
}
