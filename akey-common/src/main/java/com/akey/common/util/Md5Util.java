package com.akey.common.util;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * MD5工具类
 *
 * <p>提供字符串MD5哈希计算功能</p>
 * <p>主要用于Token等敏感信息的哈希处理</p>
 *
 * <AUTHOR>
 * @since 2025-08-02
 */
public class Md5Util {

    /**
     * 计算字符串的MD5哈希值
     * 
     * @param input 输入字符串
     * @return MD5哈希值（32位小写十六进制字符串），如果计算失败返回null
     */
    public static String md5(String input) {
        if (input == null) {
            return null;
        }

        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(input.getBytes(StandardCharsets.UTF_8));
            
            // 将字节数组转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return hexString.toString();
            
        } catch (NoSuchAlgorithmException e) {
            return null;
        }
    }

    /**
     * 验证输入字符串的MD5值是否匹配
     * 
     * @param input 输入字符串
     * @param expectedMd5 期望的MD5值
     * @return 是否匹配
     */
    public static boolean verify(String input, String expectedMd5) {
        if (input == null || expectedMd5 == null) {
            return false;
        }
        
        String actualMd5 = md5(input);
        return expectedMd5.equalsIgnoreCase(actualMd5);
    }

    /**
     * 计算Token的MD5哈希值
     * 
     * <p>专门用于Token哈希计算的便捷方法</p>
     * 
     * @param token Token值
     * @return Token的MD5哈希值
     */
    public static String tokenMd5(String token) {
        return md5(token);
    }
}
