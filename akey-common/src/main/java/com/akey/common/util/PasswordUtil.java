package com.akey.common.util;

import at.favre.lib.crypto.bcrypt.BCrypt;

/**
 * 密码工具类
 *
 * <p>提供基于BCrypt的密码加密、验证等功能</p>
 * <p>BCrypt是一种安全的密码哈希函数，内置盐值生成，抗彩虹表攻击</p>
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
public class PasswordUtil {

    /**
     * BCrypt加密强度（工作因子），范围4-31，推荐12
     * 数值越高越安全，但计算时间也越长
     */
    private static final int BCRYPT_COST = 12;

    /**
     * 使用BCrypt加密密码
     *
     * <p>BCrypt会自动生成盐值并包含在返回的哈希中</p>
     *
     * @param password 原始密码
     * @return BCrypt加密后的密码哈希
     */
    public static String encryptPassword(String password) {
        if (password == null) {
            return null;
        }

        try {
            return BCrypt.withDefaults().hashToString(BCRYPT_COST, password.toCharArray());
        } catch (Exception e) {
            throw new RuntimeException("密码加密失败", e);
        }
    }

    /**
     * 使用BCrypt验证密码
     *
     * @param inputPassword 输入的原始密码
     * @param storedHash 存储的BCrypt密码哈希
     * @return 验证结果
     */
    public static boolean verifyPassword(String inputPassword, String storedHash) {
        if (inputPassword == null || storedHash == null) {
            return false;
        }

        try {
            BCrypt.Result result = BCrypt.verifyer().verify(inputPassword.toCharArray(), storedHash);
            return result.verified;
        } catch (Exception e) {
            // 验证失败时记录日志但不抛出异常，返回false
            return false;
        }
    }
}
