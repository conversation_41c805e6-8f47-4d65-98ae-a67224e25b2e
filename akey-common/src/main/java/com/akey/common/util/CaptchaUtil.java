package com.akey.common.util;

import com.akey.common.entity.CaptchaResult;
import com.akey.common.enums.CaptchaTypeEnum;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * 验证码生成工具类
 * 
 * <p>功能说明：</p>
 * <ul>
 *   <li>支持多种验证码类型：纯数字、算式、英文大小写、英文加数字</li>
 *   <li>生成美观的验证码图片，包含干扰线、噪点、颜色渐变</li>
 *   <li>返回Base64编码的图片数据，便于前端直接使用</li>
 *   <li>提供灵活的配置选项，支持自定义图片尺寸、字体等</li>
 * </ul>
 * 
 * <p>使用示例：</p>
 * <pre>
 * // 生成默认数字验证码
 * CaptchaResult result = CaptchaUtil.generateCaptcha(CaptchaTypeEnum.NUMBER);
 * 
 * // 生成自定义长度的验证码
 * CaptchaResult result = CaptchaUtil.generateCaptcha(CaptchaTypeEnum.MIXED, 6);
 * 
 * // 生成算式验证码
 * CaptchaResult result = CaptchaUtil.generateArithmeticCaptcha();
 * </pre>
 *
 * <AUTHOR>
 * @since 2025-07-06
 */
public class CaptchaUtil {

    /**
     * 默认图片宽度
     */
    private static final int DEFAULT_WIDTH = 120;

    /**
     * 默认图片高度
     */
    private static final int DEFAULT_HEIGHT = 40;

    /**
     * 默认字体大小
     */
    private static final int DEFAULT_FONT_SIZE = 24;

    /**
     * 干扰线数量
     */
    private static final int INTERFERENCE_LINE_COUNT = 5;

    /**
     * 噪点数量
     */
    private static final int NOISE_POINT_COUNT = 50;

    /**
     * 安全随机数生成器
     */
    private static final SecureRandom RANDOM = new SecureRandom();

    /**
     * 字体名称数组
     */
    private static final String[] FONT_NAMES = {
        "Arial", "Times New Roman", "Courier New", "Verdana", "Georgia"
    };

    /**
     * 颜色数组（用于字符颜色）
     */
    private static final Color[] CHAR_COLORS = {
        new Color(0, 102, 204),    // 蓝色
        new Color(204, 0, 102),    // 紫红色
        new Color(0, 153, 51),     // 绿色
        new Color(255, 102, 0),    // 橙色
        new Color(153, 51, 0),     // 棕色
        new Color(51, 51, 153)     // 深蓝色
    };

    /**
     * 生成指定类型的验证码（使用默认长度）
     * 
     * @param type 验证码类型
     * @return 验证码结果
     */
    public static CaptchaResult generateCaptcha(CaptchaTypeEnum type) {
        if (type == null) {
            throw new IllegalArgumentException("验证码类型不能为空");
        }

        if (type.isArithmetic()) {
            return generateArithmeticCaptcha();
        } else {
            return generateCharacterCaptcha(type, type.getDefaultLength());
        }
    }

    /**
     * 生成指定类型和长度的验证码
     * 
     * @param type 验证码类型
     * @param length 验证码长度
     * @return 验证码结果
     */
    public static CaptchaResult generateCaptcha(CaptchaTypeEnum type, int length) {
        if (type == null) {
            throw new IllegalArgumentException("验证码类型不能为空");
        }

        if (length <= 0) {
            throw new IllegalArgumentException("验证码长度必须大于0");
        }

        if (type.isArithmetic()) {
            return generateArithmeticCaptcha();
        } else {
            return generateCharacterCaptcha(type, length);
        }
    }

    /**
     * 生成字符类型验证码
     * 
     * @param type 验证码类型
     * @param length 验证码长度
     * @return 验证码结果
     */
    private static CaptchaResult generateCharacterCaptcha(CaptchaTypeEnum type, int length) {
        String charset = type.getCharset();
        if (charset.isEmpty()) {
            throw new IllegalArgumentException("验证码类型的字符集不能为空");
        }

        // 生成验证码字符串
        StringBuilder codeBuilder = new StringBuilder();
        for (int i = 0; i < length; i++) {
            int index = RANDOM.nextInt(charset.length());
            codeBuilder.append(charset.charAt(index));
        }
        String code = codeBuilder.toString();

        // 生成验证码图片
        String imageBase64 = generateCaptchaImage(code, DEFAULT_WIDTH, DEFAULT_HEIGHT);

        return CaptchaResult.builder()
                .answer(code)
                .imageBase64(imageBase64)
                .type(type.getCode())
                .length(length)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 生成算式验证码
     * 
     * @return 验证码结果
     */
    public static CaptchaResult generateArithmeticCaptcha() {
        // 生成两个1-20之间的随机数
        int num1 = RANDOM.nextInt(20) + 1;
        int num2 = RANDOM.nextInt(20) + 1;
        
        // 随机选择运算符
        String[] operators = {"+", "-", "×"};
        String operator = operators[RANDOM.nextInt(operators.length)];
        
        int result;
        String expression;
        
        switch (operator) {
            case "+":
                result = num1 + num2;
                expression = num1 + " + " + num2 + " = ?";
                break;
            case "-":
                // 确保结果为正数
                if (num1 < num2) {
                    int temp = num1;
                    num1 = num2;
                    num2 = temp;
                }
                result = num1 - num2;
                expression = num1 + " - " + num2 + " = ?";
                break;
            case "×":
                // 使用较小的数字进行乘法运算
                num1 = RANDOM.nextInt(9) + 1;
                num2 = RANDOM.nextInt(9) + 1;
                result = num1 * num2;
                expression = num1 + " × " + num2 + " = ?";
                break;
            default:
                throw new IllegalStateException("未知的运算符: " + operator);
        }

        // 生成验证码图片
        String imageBase64 = generateCaptchaImage(expression, DEFAULT_WIDTH + 40, DEFAULT_HEIGHT);

        return CaptchaResult.ofArithmetic(String.valueOf(result), expression, imageBase64);
    }

    /**
     * 生成验证码图片
     * 
     * @param text 要显示的文本
     * @param width 图片宽度
     * @param height 图片高度
     * @return Base64编码的图片数据
     */
    private static String generateCaptchaImage(String text, int width, int height) {
        try {
            // 创建图片缓冲区
            BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = image.createGraphics();

            // 设置抗锯齿
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

            // 绘制背景
            drawBackground(g2d, width, height);

            // 绘制干扰线
            drawInterferenceLines(g2d, width, height);

            // 绘制文本
            drawText(g2d, text, width, height);

            // 绘制噪点
            drawNoisePoints(g2d, width, height);

            g2d.dispose();

            // 转换为Base64
            return imageToBase64(image);

        } catch (Exception e) {
            throw new RuntimeException("生成验证码图片失败", e);
        }
    }

    /**
     * 绘制背景
     *
     * @param g2d 图形上下文
     * @param width 图片宽度
     * @param height 图片高度
     */
    private static void drawBackground(Graphics2D g2d, int width, int height) {
        // 创建渐变背景
        GradientPaint gradient = new GradientPaint(
            0, 0, new Color(240, 248, 255),  // 浅蓝色
            width, height, new Color(255, 250, 240)  // 浅黄色
        );
        g2d.setPaint(gradient);
        g2d.fillRect(0, 0, width, height);

        // 绘制边框
        g2d.setColor(new Color(200, 200, 200));
        g2d.setStroke(new BasicStroke(1));
        g2d.drawRect(0, 0, width - 1, height - 1);
    }

    /**
     * 绘制干扰线
     *
     * @param g2d 图形上下文
     * @param width 图片宽度
     * @param height 图片高度
     */
    private static void drawInterferenceLines(Graphics2D g2d, int width, int height) {
        g2d.setStroke(new BasicStroke(1.5f));

        for (int i = 0; i < INTERFERENCE_LINE_COUNT; i++) {
            // 随机颜色
            Color color = new Color(
                RANDOM.nextInt(100) + 100,  // R: 100-199
                RANDOM.nextInt(100) + 100,  // G: 100-199
                RANDOM.nextInt(100) + 100   // B: 100-199
            );
            g2d.setColor(color);

            // 随机起点和终点
            int x1 = RANDOM.nextInt(width);
            int y1 = RANDOM.nextInt(height);
            int x2 = RANDOM.nextInt(width);
            int y2 = RANDOM.nextInt(height);

            g2d.drawLine(x1, y1, x2, y2);
        }
    }

    /**
     * 绘制文本
     *
     * @param g2d 图形上下文
     * @param text 要绘制的文本
     * @param width 图片宽度
     * @param height 图片高度
     */
    private static void drawText(Graphics2D g2d, String text, int width, int height) {
        int charCount = text.length();
        int charWidth = width / (charCount + 1);  // 字符间距

        for (int i = 0; i < charCount; i++) {
            char ch = text.charAt(i);

            // 随机字体
            String fontName = FONT_NAMES[RANDOM.nextInt(FONT_NAMES.length)];
            int fontSize = DEFAULT_FONT_SIZE + RANDOM.nextInt(6) - 3;  // 字体大小变化 ±3
            int fontStyle = RANDOM.nextBoolean() ? Font.BOLD : Font.PLAIN;
            Font font = new Font(fontName, fontStyle, fontSize);
            g2d.setFont(font);

            // 随机颜色
            Color color = CHAR_COLORS[RANDOM.nextInt(CHAR_COLORS.length)];
            g2d.setColor(color);

            // 计算字符位置
            FontMetrics fm = g2d.getFontMetrics();
            int charHeight = fm.getHeight();
            int x = charWidth * (i + 1) - fm.stringWidth(String.valueOf(ch)) / 2;
            int y = (height + charHeight) / 2 - fm.getDescent();

            // 随机旋转角度 (-15° 到 15°)
            double angle = (RANDOM.nextDouble() - 0.5) * Math.PI / 6;
            g2d.rotate(angle, x, y);

            // 绘制字符
            g2d.drawString(String.valueOf(ch), x, y);

            // 恢复旋转
            g2d.rotate(-angle, x, y);
        }
    }

    /**
     * 绘制噪点
     *
     * @param g2d 图形上下文
     * @param width 图片宽度
     * @param height 图片高度
     */
    private static void drawNoisePoints(Graphics2D g2d, int width, int height) {
        for (int i = 0; i < NOISE_POINT_COUNT; i++) {
            // 随机位置
            int x = RANDOM.nextInt(width);
            int y = RANDOM.nextInt(height);

            // 随机颜色（较浅的颜色）
            Color color = new Color(
                RANDOM.nextInt(100) + 150,  // R: 150-249
                RANDOM.nextInt(100) + 150,  // G: 150-249
                RANDOM.nextInt(100) + 150   // B: 150-249
            );
            g2d.setColor(color);

            // 绘制小圆点
            g2d.fillOval(x, y, 2, 2);
        }
    }

    /**
     * 将图片转换为Base64编码
     *
     * @param image 图片对象
     * @return Base64编码的图片数据
     * @throws IOException IO异常
     */
    private static String imageToBase64(BufferedImage image) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, "png", baos);
        byte[] imageBytes = baos.toByteArray();
        String base64 = Base64.getEncoder().encodeToString(imageBytes);
        return "data:image/png;base64," + base64;
    }

    // ==================== 便捷方法 ====================

    /**
     * 生成默认的数字验证码（4位）
     *
     * @return 验证码结果
     */
    public static CaptchaResult generateNumberCaptcha() {
        return generateCaptcha(CaptchaTypeEnum.NUMBER);
    }

    /**
     * 生成指定长度的数字验证码
     *
     * @param length 验证码长度
     * @return 验证码结果
     */
    public static CaptchaResult generateNumberCaptcha(int length) {
        return generateCaptcha(CaptchaTypeEnum.NUMBER, length);
    }

    /**
     * 生成默认的字母验证码（4位）
     *
     * @return 验证码结果
     */
    public static CaptchaResult generateLetterCaptcha() {
        return generateCaptcha(CaptchaTypeEnum.LETTER);
    }

    /**
     * 生成指定长度的字母验证码
     *
     * @param length 验证码长度
     * @return 验证码结果
     */
    public static CaptchaResult generateLetterCaptcha(int length) {
        return generateCaptcha(CaptchaTypeEnum.LETTER, length);
    }

    /**
     * 生成默认的混合验证码（4位）
     *
     * @return 验证码结果
     */
    public static CaptchaResult generateMixedCaptcha() {
        return generateCaptcha(CaptchaTypeEnum.MIXED);
    }

    /**
     * 生成指定长度的混合验证码
     *
     * @param length 验证码长度
     * @return 验证码结果
     */
    public static CaptchaResult generateMixedCaptcha(int length) {
        return generateCaptcha(CaptchaTypeEnum.MIXED, length);
    }

    // ==================== 自定义配置方法 ====================

    /**
     * 生成自定义尺寸的验证码
     *
     * @param type 验证码类型
     * @param length 验证码长度
     * @param width 图片宽度
     * @param height 图片高度
     * @return 验证码结果
     */
    public static CaptchaResult generateCustomCaptcha(CaptchaTypeEnum type, int length, int width, int height) {
        if (type == null) {
            throw new IllegalArgumentException("验证码类型不能为空");
        }

        if (!type.isArithmetic() && length <= 0) {
            throw new IllegalArgumentException("验证码长度必须大于0");
        }

        if (width <= 0 || height <= 0) {
            throw new IllegalArgumentException("图片尺寸必须大于0");
        }

        if (type.isArithmetic()) {
            return generateCustomArithmeticCaptcha(width, height);
        } else {
            return generateCustomCharacterCaptcha(type, length, width, height);
        }
    }

    /**
     * 生成自定义尺寸的字符验证码
     *
     * @param type 验证码类型
     * @param length 验证码长度
     * @param width 图片宽度
     * @param height 图片高度
     * @return 验证码结果
     */
    private static CaptchaResult generateCustomCharacterCaptcha(CaptchaTypeEnum type, int length, int width, int height) {
        String charset = type.getCharset();
        if (charset.isEmpty()) {
            throw new IllegalArgumentException("验证码类型的字符集不能为空");
        }

        // 生成验证码字符串
        StringBuilder codeBuilder = new StringBuilder();
        for (int i = 0; i < length; i++) {
            int index = RANDOM.nextInt(charset.length());
            codeBuilder.append(charset.charAt(index));
        }
        String code = codeBuilder.toString();

        // 生成验证码图片
        String imageBase64 = generateCaptchaImage(code, width, height);

        return CaptchaResult.builder()
                .answer(code)
                .imageBase64(imageBase64)
                .type(type.getCode())
                .length(length)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 生成自定义尺寸的算式验证码
     *
     * @param width 图片宽度
     * @param height 图片高度
     * @return 验证码结果
     */
    private static CaptchaResult generateCustomArithmeticCaptcha(int width, int height) {
        // 生成两个1-20之间的随机数
        int num1 = RANDOM.nextInt(20) + 1;
        int num2 = RANDOM.nextInt(20) + 1;

        // 随机选择运算符
        String[] operators = {"+", "-", "×"};
        String operator = operators[RANDOM.nextInt(operators.length)];

        int result;
        String expression;

        switch (operator) {
            case "+":
                result = num1 + num2;
                expression = num1 + " + " + num2 + " = ?";
                break;
            case "-":
                // 确保结果为正数
                if (num1 < num2) {
                    int temp = num1;
                    num1 = num2;
                    num2 = temp;
                }
                result = num1 - num2;
                expression = num1 + " - " + num2 + " = ?";
                break;
            case "×":
                // 使用较小的数字进行乘法运算
                num1 = RANDOM.nextInt(9) + 1;
                num2 = RANDOM.nextInt(9) + 1;
                result = num1 * num2;
                expression = num1 + " × " + num2 + " = ?";
                break;
            default:
                throw new IllegalStateException("未知的运算符: " + operator);
        }

        // 生成验证码图片
        String imageBase64 = generateCaptchaImage(expression, width, height);

        return CaptchaResult.ofArithmetic(String.valueOf(result), expression, imageBase64);
    }

    /**
     * 验证用户输入的验证码
     *
     * @param userInput 用户输入
     * @param captchaResult 验证码结果
     * @return true:验证通过，false:验证失败
     */
    public static boolean verifyCaptcha(String userInput, CaptchaResult captchaResult) {
        if (userInput == null || captchaResult == null) {
            return false;
        }

        return captchaResult.verify(userInput);
    }

    /**
     * 验证用户输入的验证码（严格模式，区分大小写）
     *
     * @param userInput 用户输入
     * @param captchaResult 验证码结果
     * @return true:验证通过，false:验证失败
     */
    public static boolean verifyCaptchaStrict(String userInput, CaptchaResult captchaResult) {
        if (userInput == null || captchaResult == null) {
            return false;
        }

        return captchaResult.verify(userInput, false);
    }
}
