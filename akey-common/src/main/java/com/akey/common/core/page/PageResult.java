package com.akey.common.core.page;

import lombok.Data;
import java.util.Collections;
import java.util.List;

/**
 * 分页结果封装类
 * 
 * @param <T> 数据类型
 * <AUTHOR>
 * @since 2025-08-02
 */
@Data
public class PageResult<T> {

    /**
     * 数据列表
     */
    private List<T> records;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 当前页码
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 总页数
     */
    private Integer pages;

    /**
     * 是否有上一页
     */
    private Boolean hasPrevious;

    /**
     * 是否有下一页
     */
    private Boolean hasNext;

    /**
     * 是否为第一页
     */
    private Boolean isFirst;

    /**
     * 是否为最后一页
     */
    private Boolean isLast;

    /**
     * 构造函数
     */
    public PageResult() {
    }

    /**
     * 构造函数
     * 
     * @param records 数据列表
     * @param total 总记录数
     * @param pageNum 当前页码
     * @param pageSize 每页大小
     */
    public PageResult(List<T> records, Long total, Integer pageNum, Integer pageSize) {
        this.records = records;
        this.total = total;
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.calculatePages();
    }

    /**
     * 静态工厂方法
     * 
     * @param records 数据列表
     * @param total 总记录数
     * @param pageNum 当前页码
     * @param pageSize 每页大小
     * @param <T> 数据类型
     * @return 分页结果
     */
    public static <T> PageResult<T> of(List<T> records, long total, int pageNum, int pageSize) {
        return new PageResult<>(records, total, pageNum, pageSize);
    }

    /**
     * 静态工厂方法 - 空结果
     * 
     * @param <T> 数据类型
     * @return 空的分页结果
     */
    public static <T> PageResult<T> empty() {
        return new PageResult<>(Collections.emptyList(), 0L, 1, 10);
    }

    /**
     * 静态工厂方法 - 空结果（指定分页参数）
     * 
     * @param pageNum 当前页码
     * @param pageSize 每页大小
     * @param <T> 数据类型
     * @return 空的分页结果
     */
    public static <T> PageResult<T> empty(int pageNum, int pageSize) {
        return new PageResult<>(Collections.emptyList(), 0L, pageNum, pageSize);
    }

    /**
     * 计算分页信息
     */
    private void calculatePages() {
        if (total == null || pageSize == null || pageSize <= 0) {
            this.pages = 0;
            this.hasPrevious = false;
            this.hasNext = false;
            this.isFirst = true;
            this.isLast = true;
            return;
        }

        // 计算总页数
        this.pages = (int) Math.ceil((double) total / pageSize);
        
        // 修正页码
        if (pageNum == null || pageNum < 1) {
            this.pageNum = 1;
        }
        if (this.pages > 0 && this.pageNum > this.pages) {
            this.pageNum = this.pages;
        }

        // 计算分页状态
        this.hasPrevious = this.pageNum > 1;
        this.hasNext = this.pageNum < this.pages;
        this.isFirst = this.pageNum == 1;
        this.isLast = this.pageNum.equals(this.pages) || this.pages == 0;
    }

    /**
     * 设置总记录数（自动重新计算分页信息）
     */
    public void setTotal(Long total) {
        this.total = total;
        this.calculatePages();
    }

    /**
     * 设置每页大小（自动重新计算分页信息）
     */
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
        this.calculatePages();
    }

    /**
     * 设置当前页码（自动重新计算分页信息）
     */
    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
        this.calculatePages();
    }

    /**
     * 获取起始记录索引（从0开始）
     */
    public int getStartIndex() {
        if (pageNum == null || pageSize == null || pageNum < 1) {
            return 0;
        }
        return (pageNum - 1) * pageSize;
    }

    /**
     * 获取结束记录索引（不包含）
     */
    public int getEndIndex() {
        if (total == null) {
            return getStartIndex();
        }
        return Math.min(getStartIndex() + (pageSize == null ? 0 : pageSize), total.intValue());
    }

    /**
     * 是否有数据
     */
    public boolean hasData() {
        return records != null && !records.isEmpty();
    }

    /**
     * 获取数据数量
     */
    public int getSize() {
        return records == null ? 0 : records.size();
    }
}
