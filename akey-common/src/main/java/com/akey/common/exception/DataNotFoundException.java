package com.akey.common.exception;

import com.akey.common.enums.ResultCode;

/**
 * 数据不存在异常类
 * 用于处理查询数据不存在的异常情况
 * 
 * <AUTHOR>
 * @since 2025-07-02
 */
public class DataNotFoundException extends BaseException {
    
    /**
     * 默认构造方法
     */
    public DataNotFoundException() {
        super(ResultCode.NOT_FOUND);
    }
    
    /**
     * 构造方法 - 仅消息
     *
     * @param message 错误消息
     */
    public DataNotFoundException(String message) {
        super(ResultCode.NOT_FOUND.getCode(), message);
    }
    
    /**
     * 构造方法 - 消息和异常
     *
     * @param message 错误消息
     * @param cause   原始异常
     */
    public DataNotFoundException(String message, Throwable cause) {
        super(ResultCode.NOT_FOUND.getCode(), message, cause);
    }
    
    /**
     * 构造方法 - 使用状态码枚举
     *
     * @param resultCode 状态码枚举
     */
    public DataNotFoundException(ResultCode resultCode) {
        super(resultCode);
    }
    
    /**
     * 构造方法 - 使用状态码枚举和自定义消息
     *
     * @param resultCode 状态码枚举
     * @param message    自定义错误消息
     */
    public DataNotFoundException(ResultCode resultCode, String message) {
        super(resultCode, message);
    }
    
    /**
     * 根据实体类型创建数据不存在异常
     *
     * @param entityType 实体类型
     * @return DataNotFoundException
     */
    public static DataNotFoundException byType(String entityType) {
        return new DataNotFoundException(String.format("%s数据不存在", entityType));
    }
    
    /**
     * 根据实体类型和ID创建数据不存在异常
     *
     * @param entityType 实体类型
     * @param id         实体ID
     * @return DataNotFoundException
     */
    public static DataNotFoundException byTypeAndId(String entityType, Object id) {
        return new DataNotFoundException(String.format("%s[ID=%s]数据不存在", entityType, id));
    }
    
    /**
     * 根据条件创建数据不存在异常
     *
     * @param entityType 实体类型
     * @param condition  查询条件
     * @return DataNotFoundException
     */
    public static DataNotFoundException byCondition(String entityType, String condition) {
        return new DataNotFoundException(String.format("根据条件[%s]未找到%s数据", condition, entityType));
    }
} 