package com.akey.common.exception;

import com.akey.common.enums.ResultCode;

/**
 * 业务异常类
 * 用于处理业务逻辑中的各种异常情况
 * 
 * <AUTHOR>
 * @since 2025-07-02
 */
public class BusinessException extends BaseException {
    
    /**
     * 默认构造方法
     */
    public BusinessException() {
        super(ResultCode.BUSINESS_ERROR);
    }
    
    /**
     * 构造方法 - 仅消息
     *
     * @param message 错误消息
     */
    public BusinessException(String message) {
        super(ResultCode.BUSINESS_ERROR.getCode(), message);
    }
    
    /**
     * 构造方法 - 消息和异常
     *
     * @param message 错误消息
     * @param cause   原始异常
     */
    public BusinessException(String message, Throwable cause) {
        super(ResultCode.BUSINESS_ERROR.getCode(), message, cause);
    }
    
    /**
     * 构造方法 - 状态码和消息
     *
     * @param code    错误状态码
     * @param message 错误消息
     */
    public BusinessException(Integer code, String message) {
        super(code, message);
    }
    
    /**
     * 构造方法 - 使用状态码枚举
     *
     * @param resultCode 状态码枚举
     */
    public BusinessException(ResultCode resultCode) {
        super(resultCode);
    }
    
    /**
     * 构造方法 - 使用状态码枚举和自定义消息
     *
     * @param resultCode 状态码枚举
     * @param message    自定义错误消息
     */
    public BusinessException(ResultCode resultCode, String message) {
        super(resultCode, message);
    }
    
    /**
     * 构造方法 - 使用状态码枚举和异常
     *
     * @param resultCode 状态码枚举
     * @param cause      原始异常
     */
    public BusinessException(ResultCode resultCode, Throwable cause) {
        super(resultCode, cause);
    }
} 