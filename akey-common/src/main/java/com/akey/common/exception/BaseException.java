package com.akey.common.exception;

import com.akey.common.enums.ResultCode;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 基础异常类
 * 所有自定义异常的父类，提供统一的异常处理机制
 * 
 * <AUTHOR>
 * @since 2025-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseException extends RuntimeException {
    
    /**
     * 错误状态码
     */
    private Integer code;
    
    /**
     * 错误消息
     */
    private String message;
    
    /**
     * 默认构造方法
     */
    public BaseException() {
        super();
    }
    
    /**
     * 构造方法 - 仅消息
     *
     * @param message 错误消息
     */
    public BaseException(String message) {
        super(message);
        this.message = message;
        this.code = ResultCode.INTERNAL_SERVER_ERROR.getCode();
    }
    
    /**
     * 构造方法 - 消息和异常
     *
     * @param message 错误消息
     * @param cause   原始异常
     */
    public BaseException(String message, Throwable cause) {
        super(message, cause);
        this.message = message;
        this.code = ResultCode.INTERNAL_SERVER_ERROR.getCode();
    }
    
    /**
     * 构造方法 - 状态码和消息
     *
     * @param code    错误状态码
     * @param message 错误消息
     */
    public BaseException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
    
    /**
     * 构造方法 - 状态码、消息和异常
     *
     * @param code    错误状态码
     * @param message 错误消息
     * @param cause   原始异常
     */
    public BaseException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }
    
    /**
     * 构造方法 - 使用状态码枚举
     *
     * @param resultCode 状态码枚举
     */
    public BaseException(ResultCode resultCode) {
        super(resultCode.getMessage());
        this.code = resultCode.getCode();
        this.message = resultCode.getMessage();
    }
    
    /**
     * 构造方法 - 使用状态码枚举和异常
     *
     * @param resultCode 状态码枚举
     * @param cause      原始异常
     */
    public BaseException(ResultCode resultCode, Throwable cause) {
        super(resultCode.getMessage(), cause);
        this.code = resultCode.getCode();
        this.message = resultCode.getMessage();
    }
    
    /**
     * 构造方法 - 使用状态码枚举和自定义消息
     *
     * @param resultCode 状态码枚举
     * @param message    自定义错误消息
     */
    public BaseException(ResultCode resultCode, String message) {
        super(message);
        this.code = resultCode.getCode();
        this.message = message;
    }
} 