package com.akey.common.exception;

import com.akey.common.enums.ResultCode;

/**
 * 参数验证异常类
 * 用于处理参数验证失败的异常情况
 * 
 * <AUTHOR>
 * @since 2025-07-02
 */
public class ValidationException extends BaseException {
    
    /**
     * 默认构造方法
     */
    public ValidationException() {
        super(ResultCode.PARAM_ERROR);
    }
    
    /**
     * 构造方法 - 仅消息
     *
     * @param message 错误消息
     */
    public ValidationException(String message) {
        super(ResultCode.PARAM_ERROR.getCode(), message);
    }
    
    /**
     * 构造方法 - 消息和异常
     *
     * @param message 错误消息
     * @param cause   原始异常
     */
    public ValidationException(String message, Throwable cause) {
        super(ResultCode.PARAM_ERROR.getCode(), message, cause);
    }
    
    /**
     * 构造方法 - 使用状态码枚举
     *
     * @param resultCode 状态码枚举
     */
    public ValidationException(ResultCode resultCode) {
        super(resultCode);
    }
    
    /**
     * 构造方法 - 使用状态码枚举和自定义消息
     *
     * @param resultCode 状态码枚举
     * @param message    自定义错误消息
     */
    public ValidationException(ResultCode resultCode, String message) {
        super(resultCode, message);
    }
    
    /**
     * 创建参数为空异常
     *
     * @param paramName 参数名称
     * @return ValidationException
     */
    public static ValidationException paramNull(String paramName) {
        return new ValidationException(ResultCode.PARAM_NULL, paramName + "不能为空");
    }
    
    /**
     * 创建参数格式错误异常
     *
     * @param paramName 参数名称
     * @return ValidationException
     */
    public static ValidationException paramFormat(String paramName) {
        return new ValidationException(ResultCode.PARAM_FORMAT_ERROR, paramName + "格式不正确");
    }
    
    /**
     * 创建参数值错误异常
     *
     * @param paramName 参数名称
     * @param value     参数值
     * @return ValidationException
     */
    public static ValidationException paramValue(String paramName, Object value) {
        return new ValidationException(ResultCode.PARAM_ERROR, 
                                     String.format("%s值[%s]不正确", paramName, value));
    }
} 