package com.akey.common.exception;

import com.akey.common.enums.ResultCode;

/**
 * 认证授权异常类
 * 用于处理认证和授权相关的各种异常情况
 * 
 * <AUTHOR>
 * @since 2025-07-05
 */
public class AuthException extends BaseException {
    
    /**
     * 默认构造方法
     */
    public AuthException() {
        super(ResultCode.UNAUTHORIZED);
    }
    
    /**
     * 构造方法 - 仅消息
     *
     * @param message 错误消息
     */
    public AuthException(String message) {
        super(ResultCode.UNAUTHORIZED.getCode(), message);
    }
    
    /**
     * 构造方法 - 消息和异常
     *
     * @param message 错误消息
     * @param cause   原始异常
     */
    public AuthException(String message, Throwable cause) {
        super(ResultCode.UNAUTHORIZED.getCode(), message, cause);
    }
    
    /**
     * 构造方法 - 状态码和消息
     *
     * @param code    错误状态码
     * @param message 错误消息
     */
    public AuthException(Integer code, String message) {
        super(code, message);
    }
    
    /**
     * 构造方法 - 使用状态码枚举
     *
     * @param resultCode 状态码枚举
     */
    public AuthException(ResultCode resultCode) {
        super(resultCode);
    }
    
    /**
     * 构造方法 - 使用状态码枚举和自定义消息
     *
     * @param resultCode 状态码枚举
     * @param message    自定义错误消息
     */
    public AuthException(ResultCode resultCode, String message) {
        super(resultCode, message);
    }
    
    /**
     * 构造方法 - 使用状态码枚举和异常
     *
     * @param resultCode 状态码枚举
     * @param cause      原始异常
     */
    public AuthException(ResultCode resultCode, Throwable cause) {
        super(resultCode, cause);
    }
    
    // ==================== 静态工厂方法 ====================
    
    /**
     * 创建登录失败异常
     *
     * @return AuthException
     */
    public static AuthException loginFailed() {
        return new AuthException(ResultCode.UNAUTHORIZED, "用户名或密码错误");
    }
    
    /**
     * 创建登录失败异常 - 自定义消息
     *
     * @param message 自定义错误消息
     * @return AuthException
     */
    public static AuthException loginFailed(String message) {
        return new AuthException(ResultCode.UNAUTHORIZED, message);
    }
    
    /**
     * 创建账户锁定异常
     *
     * @return AuthException
     */
    public static AuthException accountLocked() {
        return new AuthException(ResultCode.FORBIDDEN, "账户已被锁定，请联系管理员");
    }
    
    /**
     * 创建账户锁定异常 - 自定义消息
     *
     * @param message 自定义错误消息
     * @return AuthException
     */
    public static AuthException accountLocked(String message) {
        return new AuthException(ResultCode.FORBIDDEN, message);
    }
    
    /**
     * 创建权限不足异常
     *
     * @return AuthException
     */
    public static AuthException permissionDenied() {
        return new AuthException(ResultCode.FORBIDDEN, "权限不足，无法访问");
    }
    
    /**
     * 创建权限不足异常 - 自定义消息
     *
     * @param message 自定义错误消息
     * @return AuthException
     */
    public static AuthException permissionDenied(String message) {
        return new AuthException(ResultCode.FORBIDDEN, message);
    }
    
    /**
     * 创建角色权限不足异常
     *
     * @return AuthException
     */
    public static AuthException roleDenied() {
        return new AuthException(ResultCode.FORBIDDEN, "角色权限不足，无法访问");
    }
    
    /**
     * 创建角色权限不足异常 - 自定义消息
     *
     * @param message 自定义错误消息
     * @return AuthException
     */
    public static AuthException roleDenied(String message) {
        return new AuthException(ResultCode.FORBIDDEN, message);
    }
    
    /**
     * 创建Token无效异常
     *
     * @return AuthException
     */
    public static AuthException invalidToken() {
        return new AuthException(ResultCode.UNAUTHORIZED, "Token无效");
    }
    
    /**
     * 创建Token过期异常
     *
     * @return AuthException
     */
    public static AuthException tokenExpired() {
        return new AuthException(ResultCode.UNAUTHORIZED, "Token已过期");
    }
    
    /**
     * 创建未登录异常
     *
     * @return AuthException
     */
    public static AuthException notLogin() {
        return new AuthException(ResultCode.UNAUTHORIZED, "当前会话未登录");
    }
    
    /**
     * 创建会话被顶下线异常
     *
     * @return AuthException
     */
    public static AuthException beReplaced() {
        return new AuthException(ResultCode.UNAUTHORIZED, "Token已被顶下线");
    }
    
    /**
     * 创建会话被踢下线异常
     *
     * @return AuthException
     */
    public static AuthException kickOut() {
        return new AuthException(ResultCode.UNAUTHORIZED, "Token已被踢下线");
    }
    
    /**
     * 创建服务封禁异常
     *
     * @return AuthException
     */
    public static AuthException serviceDisabled() {
        return new AuthException(ResultCode.FORBIDDEN, "当前账号已被封禁，无法访问");
    }
    
    /**
     * 创建服务封禁异常 - 自定义消息
     *
     * @param message 自定义错误消息
     * @return AuthException
     */
    public static AuthException serviceDisabled(String message) {
        return new AuthException(ResultCode.FORBIDDEN, message);
    }
    
    /**
     * 创建用户不存在异常
     *
     * @return AuthException
     */
    public static AuthException userNotFound() {
        return new AuthException(ResultCode.NOT_FOUND, "用户不存在");
    }
    
    /**
     * 创建用户不存在异常 - 自定义消息
     *
     * @param message 自定义错误消息
     * @return AuthException
     */
    public static AuthException userNotFound(String message) {
        return new AuthException(ResultCode.NOT_FOUND, message);
    }
    
    /**
     * 创建密码错误异常
     *
     * @return AuthException
     */
    public static AuthException passwordError() {
        return new AuthException(ResultCode.UNAUTHORIZED, "密码错误");
    }
    
    /**
     * 创建密码错误异常 - 自定义消息
     *
     * @param message 自定义错误消息
     * @return AuthException
     */
    public static AuthException passwordError(String message) {
        return new AuthException(ResultCode.UNAUTHORIZED, message);
    }
    
    /**
     * 创建验证码错误异常
     *
     * @return AuthException
     */
    public static AuthException captchaError() {
        return new AuthException(ResultCode.UNAUTHORIZED, "验证码错误");
    }
    
    /**
     * 创建验证码错误异常 - 自定义消息
     *
     * @param message 自定义错误消息
     * @return AuthException
     */
    public static AuthException captchaError(String message) {
        return new AuthException(ResultCode.UNAUTHORIZED, message);
    }
}
