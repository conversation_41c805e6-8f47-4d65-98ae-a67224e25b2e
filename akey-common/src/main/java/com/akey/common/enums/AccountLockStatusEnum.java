package com.akey.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 账户锁定状态枚举
 *
 * 用于标识用户账户的锁定状态
 * 锁定的账户无法正常登录系统
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Getter
@AllArgsConstructor
public enum AccountLockStatusEnum implements BaseEnum<Integer> {
    
    /**
     * 未锁定 - 可以正常登录
     */
    UNLOCKED(0, "未锁定"),
    
    /**
     * 已锁定 - 无法登录
     */
    LOCKED(1, "已锁定");
    
    /**
     * 枚举值
     */
    @EnumValue
    @JsonValue
    private final Integer value;
    
    /**
     * 枚举描述
     */
    private final String description;
    
    /**
     * 根据值获取枚举
     *
     * <p>使用BaseEnum接口提供的通用方法</p>
     *
     * @param value 枚举值
     * @return 对应的枚举，如果不存在则返回null
     */
    @JsonCreator
    public static AccountLockStatusEnum fromValue(Integer value) {
        return BaseEnum.fromValue(AccountLockStatusEnum.class, value);
    }
    
    /**
     * 判断账户是否被锁定
     * 
     * @param value 枚举值
     * @return true:已锁定，false:未锁定
     */
    public static boolean isLocked(Integer value) {
        AccountLockStatusEnum status = fromValue(value);
        return status == LOCKED;
    }
    
    /**
     * 判断账户是否可以登录
     * 
     * @param value 枚举值
     * @return true:可以登录，false:不能登录
     */
    public static boolean canLogin(Integer value) {
        return !isLocked(value);
    }
} 