package com.akey.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 操作类型枚举
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Getter
@AllArgsConstructor
public enum OperationType {

    /**
     * 新增操作
     */
    CREATE("CREATE", "新增"),

    /**
     * 修改操作
     */
    UPDATE("UPDATE", "修改"),

    /**
     * 删除操作
     */
    DELETE("DELETE", "删除"),

    /**
     * 查询操作
     */
    QUERY("QUERY", "查询"),

    /**
     * 登录操作
     */
    LOGIN("LOGIN", "登录"),

    /**
     * 登出操作
     */
    LOGOUT("LOGOUT", "登出"),

    /**
     * 导出操作
     */
    EXPORT("EXPORT", "导出"),

    /**
     * 导入操作
     */
    IMPORT("IMPORT", "导入"),
    /**
     * 启用操作
     */
    ENABLE("ENABLE", "启用"),

    /**
     * 禁用操作
     */
    DISABLE("DISABLE", "禁用"),

    /**
     * 重置操作
     */
    RESET("RESET", "重置"),

    /**
     * 其他操作
     */
    OTHER("OTHER", "其他");

    /**
     * 操作类型代码
     */
    private final String code;

    /**
     * 操作类型描述
     */
    private final String description;

    /**
     * 根据代码获取操作类型
     *
     * @param code 操作类型代码
     * @return 对应的枚举，如果不存在则返回null
     */
    public static OperationType fromCode(String code) {
        if (code == null) {
            return null;
        }

        for (OperationType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }

    /**
     * 判断是否为写操作
     *
     * @param code 操作类型代码
     * @return true-写操作，false-读操作
     */
    public static boolean isWriteOperation(String code) {
        OperationType type = fromCode(code);
        return type != null && type.isWriteOperation();
    }

    /**
     * 判断是否为读操作
     *
     * @param code 操作类型代码
     * @return true-读操作，false-写操作
     */
    public static boolean isReadOperation(String code) {
        OperationType type = fromCode(code);
        return type != null && type.isReadOperation();
    }

    /**
     * 判断是否为认证操作
     *
     * @param code 操作类型代码
     * @return true-认证操作，false-其他操作
     */
    public static boolean isAuthOperation(String code) {
        OperationType type = fromCode(code);
        return type != null && type.isAuthOperation();
    }

    /**
     * 判断是否为写操作
     *
     * @return true-写操作，false-读操作
     */
    public boolean isWriteOperation() {
        return this == CREATE || this == UPDATE || this == DELETE ||
                this == IMPORT ||
                this == ENABLE || this == DISABLE || this == RESET;
    }

    /**
     * 判断是否为读操作
     *
     * @return true-读操作，false-写操作
     */
    public boolean isReadOperation() {
        return this == QUERY || this == EXPORT;
    }

    /**
     * 判断是否为认证操作
     *
     * @return true-认证操作，false-其他操作
     */
    public boolean isAuthOperation() {
        return this == LOGIN || this == LOGOUT;
    }
}
