package com.akey.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 风险等级枚举
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Getter
@AllArgsConstructor
public enum RiskLevelEnum {

    /**
     * 低风险
     */
    LOW("LOW", "低风险", 1),

    /**
     * 中风险
     */
    MEDIUM("MEDIUM", "中风险", 2),

    /**
     * 高风险
     */
    HIGH("HIGH", "高风险", 3);

    /**
     * 风险等级代码
     */
    private final String code;

    /**
     * 风险等级描述
     */
    private final String description;

    /**
     * 风险等级数值（用于比较）
     */
    private final Integer level;

    /**
     * 根据代码获取枚举
     * 
     * @param code 风险等级代码
     * @return 对应的枚举，如果不存在则返回null
     */
    public static RiskLevelEnum fromCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (RiskLevelEnum riskLevel : values()) {
            if (riskLevel.getCode().equals(code)) {
                return riskLevel;
            }
        }
        
        return null;
    }

    /**
     * 判断是否为高风险
     * 
     * @param code 风险等级代码
     * @return 是否为高风险
     */
    public static boolean isHighRisk(String code) {
        return HIGH.getCode().equals(code);
    }

    /**
     * 判断是否为中等及以上风险
     * 
     * @param code 风险等级代码
     * @return 是否为中等及以上风险
     */
    public static boolean isMediumOrHighRisk(String code) {
        RiskLevelEnum riskLevel = fromCode(code);
        return riskLevel != null && riskLevel.getLevel() >= MEDIUM.getLevel();
    }
}
