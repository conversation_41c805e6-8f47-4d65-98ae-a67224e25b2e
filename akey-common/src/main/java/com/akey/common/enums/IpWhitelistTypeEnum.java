package com.akey.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * IP白名单类型枚举
 * 
 * <p>用于区分不同类型的IP白名单：</p>
 * <ul>
 *   <li>系统白名单：用于后台管理系统的访问控制</li>
 *   <li>接口白名单：用于对外开放接口的访问控制</li>
 * </ul>
 * 
 * <AUTHOR>
 * @since 2025-07-31
 */
@Getter
@AllArgsConstructor
public enum IpWhitelistTypeEnum implements BaseEnum<Integer> {
    
    /**
     * 系统白名单 - 用于后台管理系统的访问控制
     * 
     * <p>当用户访问后台管理接口时进行IP验证</p>
     * <p>通常用于保护管理员功能和敏感操作</p>
     */
    SYSTEM(1, "系统白名单"),
    
    /**
     * 接口白名单 - 用于对外开放接口的访问控制
     * 
     * <p>限制特定IP才能调用公开API接口</p>
     * <p>通常用于第三方系统集成和API调用控制</p>
     */
    API(2, "接口白名单");
    
    /**
     * 枚举值
     */
    @EnumValue
    @JsonValue
    private final Integer value;
    
    /**
     * 枚举描述
     */
    private final String description;
    
    /**
     * 根据值获取枚举
     *
     * <p>使用BaseEnum接口提供的通用方法</p>
     *
     * @param value 枚举值
     * @return 对应的枚举，如果不存在则返回null
     */
    @JsonCreator
    public static IpWhitelistTypeEnum fromValue(Integer value) {
        return BaseEnum.fromValue(IpWhitelistTypeEnum.class, value);
    }
    
    /**
     * 判断是否为系统白名单类型
     * 
     * @param value 枚举值
     * @return true:系统白名单，false:非系统白名单
     */
    public static boolean isSystemType(Integer value) {
        IpWhitelistTypeEnum type = fromValue(value);
        return type == SYSTEM;
    }
    
    /**
     * 判断是否为接口白名单类型
     * 
     * @param value 枚举值
     * @return true:接口白名单，false:非接口白名单
     */
    public static boolean isApiType(Integer value) {
        IpWhitelistTypeEnum type = fromValue(value);
        return type == API;
    }
}
