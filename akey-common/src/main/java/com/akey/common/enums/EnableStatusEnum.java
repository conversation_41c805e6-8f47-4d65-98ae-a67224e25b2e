package com.akey.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 菜单状态枚举
 *
 * 用于控制菜单的启用/禁用状态
 * 禁用的菜单不能访问，启用的菜单可以正常访问
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Getter
@AllArgsConstructor
public enum EnableStatusEnum implements BaseEnum<Integer> {
    
    /**
     * 禁用
     */
    DISABLED(0, "禁用"),
    
    /**
     * 启用
     */
    ENABLED(1, "启用");
    
    /**
     * 枚举值
     */
    @EnumValue
    @JsonValue
    private final Integer value;
    
    /**
     * 枚举描述
     */
    private final String description;
    
    /**
     * 根据值获取枚举
     *
     * <p>使用BaseEnum接口提供的通用方法</p>
     *
     * @param value 枚举值
     * @return 对应的枚举，如果不存在则返回null
     */
    @JsonCreator
    public static EnableStatusEnum fromValue(Integer value) {
        return BaseEnum.fromValue(EnableStatusEnum.class, value);
    }
    
    /**
     * 判断是否启用
     * 
     * @param value 枚举值
     * @return true:启用，false:禁用
     */
    public static boolean isEnabled(Integer value) {
        EnableStatusEnum status = fromValue(value);
        return status == ENABLED;
    }
    
    /**
     * 判断是否禁用
     * 
     * @param value 枚举值
     * @return true:禁用，false:启用
     */
    public static boolean isDisabled(Integer value) {
        return !isEnabled(value);
    }
} 