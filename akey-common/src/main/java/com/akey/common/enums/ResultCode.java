package com.akey.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应状态枚举
 * 统一定义系统中所有可能的响应状态码和消息
 * 
 * <AUTHOR>
 * @since 2025-07-02
 */
@Getter
@AllArgsConstructor
public enum ResultCode {
    
    // ========== 成功状态 ==========
    /**
     * 操作成功
     */
    SUCCESS(200, "操作成功"),
    
    // ========== 客户端错误 4xx ==========
    /**
     * 参数错误
     */
    PARAM_ERROR(400, "参数错误"),
    
    /**
     * 参数为空
     */
    PARAM_NULL(400, "参数为空"),
    
    /**
     * 参数格式不正确
     */
    PARAM_FORMAT_ERROR(400, "参数格式不正确"),
    
    /**
     * 未授权访问
     */
    UNAUTHORIZED(401, "未授权访问"),
    
    /**
     * 访问被拒绝
     */
    FORBIDDEN(403, "访问被拒绝"),
    
    /**
     * 资源不存在
     */
    NOT_FOUND(404, "资源不存在"),
    
    /**
     * 请求方法不支持
     */
    METHOD_NOT_ALLOWED(405, "请求方法不支持"),
    
    /**
     * 请求超时
     */
    REQUEST_TIMEOUT(408, "请求超时"),
    
    /**
     * 数据已存在
     */
    DATA_EXISTS(409, "数据已存在"),
    
    /**
     * 请求频率过高
     */
    TOO_MANY_REQUESTS(429, "请求频率过高"),
    
    // ========== 服务端错误 5xx ==========
    /**
     * 系统内部错误
     */
    INTERNAL_SERVER_ERROR(500, "系统内部错误"),
    
    /**
     * 服务不可用
     */
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    
    /**
     * 网关超时
     */
    GATEWAY_TIMEOUT(504, "网关超时"),
    
    // ========== 业务错误 6xxx ==========
    /**
     * 业务处理失败
     */
    BUSINESS_ERROR(6000, "业务处理失败"),
    
    /**
     * 数据操作失败
     */
    DATA_OPERATION_ERROR(6001, "数据操作失败"),
    
    /**
     * 数据验证失败
     */
    DATA_VALIDATION_ERROR(6002, "数据验证失败"),
    
    /**
     * 重复操作
     */
    DUPLICATE_OPERATION(6003, "重复操作"),
    
    /**
     * 操作条件不满足
     */
    CONDITION_NOT_MET(6004, "操作条件不满足"),
    
    // ========== 第三方服务错误 7xxx ==========
    /**
     * 外部服务调用失败
     */
    EXTERNAL_SERVICE_ERROR(7000, "外部服务调用失败"),
    
    /**
     * 数据库连接失败
     */
    DATABASE_CONNECTION_ERROR(7001, "数据库连接失败"),
    
    /**
     * 缓存服务异常
     */
    CACHE_SERVICE_ERROR(7002, "缓存服务异常");
    
    /**
     * 响应状态码
     */
    private final Integer code;
    
    /**
     * 响应消息
     */
    private final String message;
} 