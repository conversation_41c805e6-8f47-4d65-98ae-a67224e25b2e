package com.akey.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 显示、隐藏枚举
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Getter
@AllArgsConstructor
public enum ShowHideEnum implements BaseEnum<Integer> {
    
    /**
     * 隐藏
     */
    HIDDEN(0, "隐藏"),
    
    /**
     * 显示
     */
    VISIBLE(1, "显示");
    
    /**
     * 枚举值
     */
    @EnumValue
    @JsonValue
    private final Integer value;
    
    /**
     * 枚举描述
     */
    private final String description;
    
    /**
     * 根据值获取枚举
     *
     * <p>使用BaseEnum接口提供的通用方法</p>
     *
     * @param value 枚举值
     * @return 对应的枚举，如果不存在则返回null
     */
    @JsonCreator
    public static ShowHideEnum fromValue(Integer value) {
        return BaseEnum.fromValue(ShowHideEnum.class, value);
    }
    
    /**
     * 判断是否可见
     * 
     * @param value 枚举值
     * @return true:可见，false:隐藏
     */
    public static boolean isVisible(Integer value) {
        ShowHideEnum visible = fromValue(value);
        return visible == VISIBLE;
    }
    
    /**
     * 判断是否隐藏
     * 
     * @param value 枚举值
     * @return true:隐藏，false:可见
     */
    public static boolean isHidden(Integer value) {
        return !isVisible(value);
    }
} 