package com.akey.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 逻辑删除状态枚举
 * 用于标识数据的逻辑删除状态
 * 逻辑删除避免物理删除数据，保证数据安全
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Getter
@AllArgsConstructor
public enum DeletedStatusEnum implements BaseEnum<Integer> {
    
    /**
     * 未删除 - 正常数据
     */
    NOT_DELETED(0, "未删除"),
    
    /**
     * 已删除 - 逻辑删除
     */
    DELETED(1, "已删除");
    
    /**
     * 枚举值
     */
    @EnumValue
    @JsonValue
    private final Integer value;
    
    /**
     * 枚举描述
     */
    private final String description;
    
    /**
     * 根据值获取枚举
     *
     * <p>使用BaseEnum接口提供的通用方法</p>
     *
     * @param value 枚举值
     * @return 对应的枚举，如果不存在则返回null
     */
    @JsonCreator
    public static DeletedStatusEnum fromValue(Integer value) {
        return BaseEnum.fromValue(DeletedStatusEnum.class, value);
    }
    
    /**
     * 判断数据是否已删除
     * 
     * @param value 枚举值
     * @return true:已删除，false:未删除
     */
    public static boolean isDeleted(Integer value) {
        DeletedStatusEnum status = fromValue(value);
        return status == DELETED;
    }
    
    /**
     * 判断数据是否为正常状态
     * 
     * @param value 枚举值
     * @return true:正常，false:已删除
     */
    public static boolean isNormal(Integer value) {
        return !isDeleted(value);
    }
} 