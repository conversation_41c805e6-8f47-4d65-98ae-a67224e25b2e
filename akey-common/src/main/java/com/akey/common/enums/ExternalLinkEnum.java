package com.akey.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 外部链接枚举
 *
 * 用于标识菜单是否为外部链接
 * 外部链接将跳转到其他网站或系统
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Getter
@AllArgsConstructor
public enum ExternalLinkEnum implements BaseEnum<Integer> {
    
    /**
     * 否 - 内部链接
     */
    INTERNAL(0, "内部链接"),
    
    /**
     * 是 - 外部链接
     */
    EXTERNAL(1, "外部链接");
    
    /**
     * 枚举值
     */
    @EnumValue
    @JsonValue
    private final Integer value;
    
    /**
     * 枚举描述
     */
    private final String description;
    
    /**
     * 根据值获取枚举
     *
     * <p>使用BaseEnum接口提供的通用方法</p>
     *
     * @param value 枚举值
     * @return 对应的枚举，如果不存在则返回null
     */
    @JsonCreator
    public static ExternalLinkEnum fromValue(Integer value) {
        return BaseEnum.fromValue(ExternalLinkEnum.class, value);
    }
    
    /**
     * 判断是否为外部链接
     * 
     * @param value 枚举值
     * @return true:外部链接，false:内部链接
     */
    public static boolean isExternal(Integer value) {
        ExternalLinkEnum link = fromValue(value);
        return link == EXTERNAL;
    }
    
    /**
     * 判断是否为内部链接
     * 
     * @param value 枚举值
     * @return true:内部链接，false:外部链接
     */
    public static boolean isInternal(Integer value) {
        return !isExternal(value);
    }
} 