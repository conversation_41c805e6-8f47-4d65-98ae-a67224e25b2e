package com.akey.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 打开方式枚举
 *
 * 用于标识菜单链接的打开方式
 * 当前窗口打开或新窗口打开
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Getter
@AllArgsConstructor
public enum OpenModeEnum implements BaseEnum<String> {
    
    /**
     * 当前窗口打开
     */
    CURRENT_WINDOW("_self", "当前窗口"),
    
    /**
     * 新窗口打开
     */
    NEW_WINDOW("_blank", "新窗口");
    
    /**
     * 枚举值
     */
    @EnumValue
    @JsonValue
    private final String value;
    
    /**
     * 枚举描述
     */
    private final String description;
    
    /**
     * 根据值获取枚举
     *
     * <p>使用BaseEnum接口提供的通用方法</p>
     *
     * @param value 枚举值
     * @return 对应的枚举，如果不存在则返回null
     */
    @JsonCreator
    public static OpenModeEnum fromValue(String value) {
        return BaseEnum.fromValue(OpenModeEnum.class, value);
    }
    
    /**
     * 判断是否为当前窗口打开
     * 
     * @param value 枚举值
     * @return true:当前窗口，false:新窗口
     */
    public static boolean isCurrentWindow(String value) {
        OpenModeEnum mode = fromValue(value);
        return mode == CURRENT_WINDOW;
    }
    
    /**
     * 判断是否为新窗口打开
     * 
     * @param value 枚举值
     * @return true:新窗口，false:当前窗口
     */
    public static boolean isNewWindow(String value) {
        OpenModeEnum mode = fromValue(value);
        return mode == NEW_WINDOW;
    }
    
    /**
     * 获取默认打开方式
     * 
     * @return 默认为当前窗口打开
     */
    public static OpenModeEnum getDefault() {
        return CURRENT_WINDOW;
    }
} 