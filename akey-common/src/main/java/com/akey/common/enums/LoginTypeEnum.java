package com.akey.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 登录类型枚举
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Getter
@AllArgsConstructor
public enum LoginTypeEnum {

    /**
     * 密码登录
     */
    PASSWORD("PASSWORD", "密码登录"),

    /**
     * 谷歌验证登录
     */
    GOOGLE_AUTH("GOOGLE_AUTH", "谷歌验证登录"),

    /**
     * 短信验证码登录
     */
    SMS_CODE("SMS_CODE", "短信验证码登录"),

    /**
     * 邮箱验证码登录
     */
    EMAIL_CODE("EMAIL_CODE", "邮箱验证码登录"),

    /**
     * 第三方登录
     */
    THIRD_PARTY("THIRD_PARTY", "第三方登录"),

    /**
     * API密钥登录
     */
    API_KEY("API_KEY", "API密钥登录");

    /**
     * 类型代码
     */
    private final String code;

    /**
     * 类型描述
     */
    private final String description;

    /**
     * 根据代码获取枚举
     * 
     * @param code 类型代码
     * @return 对应的枚举，如果不存在则返回null
     */
    public static LoginTypeEnum fromCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (LoginTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        
        return null;
    }
}
