package com.akey.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 验证码类型枚举
 * 
 * <p>功能说明：</p>
 * <ul>
 *   <li>定义验证码的生成类型</li>
 *   <li>支持纯数字、算式、英文大小写、英文加数字等类型</li>
 *   <li>提供验证码字符集和长度配置</li>
 * </ul>
 * 
 * <p>使用示例：</p>
 * <pre>
 * // 生成纯数字验证码
 * CaptchaTypeEnum type = CaptchaTypeEnum.NUMBER;
 * String charset = type.getCharset();
 * int length = type.getDefaultLength();
 * </pre>
 *
 * <AUTHOR>
 * @since 2025-07-06
 */
@Getter
@AllArgsConstructor
public enum CaptchaTypeEnum {
    
    /**
     * 纯数字验证码
     * 字符集：0-9
     * 默认长度：4位
     */
    NUMBER("NUMBER", "纯数字", "0123456789", 4),
    
    /**
     * 加减乘除算式验证码
     * 生成简单的数学运算表达式
     * 默认长度：不适用（由算式决定）
     */
    ARITHMETIC("ARITHMETIC", "算式", "", 0),
    
    /**
     * 英文大小写验证码
     * 字符集：A-Z, a-z
     * 默认长度：4位
     */
    LETTER("LETTER", "英文大小写", "ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz", 4),
    
    /**
     * 英文加数字验证码
     * 字符集：A-Z, a-z, 0-9
     * 默认长度：4位
     * 注意：排除了容易混淆的字符 I、L、O、0、1
     */
    MIXED("MIXED", "英文加数字", "ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789", 4),

    /**
     * 随机验证码类型
     * 随机从其他类型中选择一种
     * 字符集：不适用（由随机选择的类型决定）
     * 默认长度：不适用（由随机选择的类型决定）
     */
    RANDOM("RANDOM", "随机类型", "", 0);
    
    /**
     * 类型代码
     */
    private final String code;
    
    /**
     * 类型描述
     */
    private final String description;
    
    /**
     * 字符集
     */
    private final String charset;
    
    /**
     * 默认长度
     */
    private final int defaultLength;
    
    /**
     * 根据代码获取枚举
     * 
     * @param code 类型代码
     * @return 对应的枚举，如果不存在则返回null
     */
    public static CaptchaTypeEnum fromCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (CaptchaTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        
        return null;
    }
    
    /**
     * 判断是否为算式类型
     * 
     * @return true:算式类型，false:其他类型
     */
    public boolean isArithmetic() {
        return this == ARITHMETIC;
    }
    
    /**
     * 判断是否为字符类型（非算式）
     * 
     * @return true:字符类型，false:算式类型
     */
    public boolean isCharacterType() {
        return !isArithmetic();
    }
    
    /**
     * 获取有效的字符集长度
     *
     * @return 字符集长度
     */
    public int getCharsetLength() {
        return charset.length();
    }

    /**
     * 判断是否为随机类型
     *
     * @return true:随机类型，false:其他类型
     */
    public boolean isRandom() {
        return this == RANDOM;
    }

    /**
     * 获取随机验证码类型（排除RANDOM本身）
     *
     * @return 随机选择的验证码类型
     */
    public static CaptchaTypeEnum getRandomType() {
        CaptchaTypeEnum[] availableTypes = {NUMBER, ARITHMETIC, LETTER, MIXED};
        int randomIndex = (int) (Math.random() * availableTypes.length);
        return availableTypes[randomIndex];
    }
}
