package com.akey.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 是否内置枚举
 *
 * 用于标识是否为系统内置类型
 * 内置类型不允许删除，确保系统稳定性
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Getter
@AllArgsConstructor
public enum BuiltinEnum implements BaseEnum<Integer> {
    
    /**
     * 内置类型 - 不可删除
     */
    BUILTIN(0, "内置类型"),
    
    /**
     * 非内置类型 - 可以删除
     */
    NOT_BUILTIN(1, "非内置类型");
    
    /**
     * 枚举值
     */
    @EnumValue
    @JsonValue
    private final Integer value;
    
    /**
     * 枚举描述
     */
    private final String description;
    
    /**
     * 根据值获取枚举
     *
     * <p>使用BaseEnum接口提供的通用方法</p>
     *
     * @param value 枚举值
     * @return 对应的枚举，如果不存在则返回null
     */
    @JsonCreator
    public static BuiltinEnum fromValue(Integer value) {
        return BaseEnum.fromValue(BuiltinEnum.class, value);
    }
    
    /**
     * 判断是否为内置类型
     * 
     * @param value 枚举值
     * @return true:内置类型，false:非内置类型
     */
    public static boolean isBuiltin(Integer value) {
        BuiltinEnum builtin = fromValue(value);
        return builtin == BUILTIN;
    }

    /**
     * 判断是否为内置类型
     *
     * @param builtinEnum 枚举实例
     * @return true:内置类型，false:非内置类型
     */
    public static boolean isBuiltin(BuiltinEnum builtinEnum) {
        return builtinEnum == BUILTIN;
    }
    
    /**
     * 判断是否可以删除
     * 
     * @param value 枚举值
     * @return true:可以删除，false:不可删除
     */
    public static boolean canDelete(Integer value) {
        return !isBuiltin(value);
    }
} 