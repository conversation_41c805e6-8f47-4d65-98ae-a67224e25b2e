package com.akey.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 登录状态枚举
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Getter
@AllArgsConstructor
public enum LoginStatusEnum {

    /**
     * 登录失败
     */
    FAILURE(0, "登录失败"),

    /**
     * 登录成功
     */
    SUCCESS(1, "登录成功");

    /**
     * 状态值
     */
    private final Integer value;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据值获取枚举
     * 
     * @param value 状态值
     * @return 对应的枚举，如果不存在则返回null
     */
    public static LoginStatusEnum fromValue(Integer value) {
        if (value == null) {
            return null;
        }
        
        for (LoginStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        
        return null;
    }

    /**
     * 判断是否为成功状态
     * 
     * @param value 状态值
     * @return 是否成功
     */
    public static boolean isSuccess(Integer value) {
        return SUCCESS.getValue().equals(value);
    }

    /**
     * 判断是否为失败状态
     * 
     * @param value 状态值
     * @return 是否失败
     */
    public static boolean isFailure(Integer value) {
        return FAILURE.getValue().equals(value);
    }
}
