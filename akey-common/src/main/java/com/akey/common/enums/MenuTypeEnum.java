package com.akey.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 菜单类型枚举
 *
 * 用于标识菜单的类型分类
 * 目录：可以包含子菜单的分组
 * 菜单：具体的页面路由
 * 按钮：页面内的操作权限
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Getter
@AllArgsConstructor
public enum MenuTypeEnum implements BaseEnum<Integer> {
    
    /**
     * 目录 - 可以包含子菜单的分组
     */
    DIRECTORY(1, "目录"),
    
    /**
     * 菜单 - 具体的页面路由
     */
    MENU(2, "菜单"),
    
    /**
     * 按钮 - 页面内的操作权限
     */
    BUTTON(3, "按钮");
    
    /**
     * 枚举值
     */
    @EnumValue
    @JsonValue
    private final Integer value;
    
    /**
     * 枚举描述
     */
    private final String description;
    
    /**
     * 根据值获取枚举
     *
     * <p>使用BaseEnum接口提供的通用方法</p>
     *
     * @param value 枚举值
     * @return 对应的枚举，如果不存在则返回null
     */
    @JsonCreator
    public static MenuTypeEnum fromValue(Integer value) {
        return BaseEnum.fromValue(MenuTypeEnum.class, value);
    }
    
    /**
     * 判断是否为目录类型
     * 
     * @param value 枚举值
     * @return true:目录类型，false:非目录类型
     */
    public static boolean isDirectory(Integer value) {
        MenuTypeEnum type = fromValue(value);
        return type == DIRECTORY;
    }
    
    /**
     * 判断是否为菜单类型
     * 
     * @param value 枚举值
     * @return true:菜单类型，false:非菜单类型
     */
    public static boolean isMenu(Integer value) {
        MenuTypeEnum type = fromValue(value);
        return type == MENU;
    }
    
    /**
     * 判断是否为按钮类型
     * 
     * @param value 枚举值
     * @return true:按钮类型，false:非按钮类型
     */
    public static boolean isButton(Integer value) {
        MenuTypeEnum type = fromValue(value);
        return type == BUTTON;
    }
} 