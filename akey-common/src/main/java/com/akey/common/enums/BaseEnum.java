package com.akey.common.enums;

import com.fasterxml.jackson.annotation.JsonCreator;

/**
 * 基础枚举接口
 * 
 * <p>所有业务枚举都应该实现此接口，以支持统一的值转换</p>
 * <p>提供了通用的fromValue方法，避免每个枚举都重复实现</p>
 * 
 * @param <T> 枚举值的类型，如Integer、String等
 * <AUTHOR>
 * @since 2025-07-09
 */
public interface BaseEnum<T> {
    
    /**
     * 获取枚举的值
     * 
     * @return 枚举值
     */
    T getValue();
    
    /**
     * 根据值获取对应的枚举实例
     * 
     * <p>通用的枚举值转换方法，支持所有实现BaseEnum接口的枚举类</p>
     * <p>使用泛型确保类型安全，避免类型转换异常</p>
     * 
     * @param <E> 枚举类型
     * @param <T> 枚举值类型
     * @param enumClass 枚举类的Class对象
     * @param value 要转换的值
     * @return 对应的枚举实例，如果找不到则返回null
     */
    static <E extends BaseEnum<T>, T> E fromValue(Class<E> enumClass, T value) {
        if (value == null) {
            return null;
        }
        
        // 获取枚举类的所有常量
        E[] enumConstants = enumClass.getEnumConstants();
        if (enumConstants == null) {
            return null;
        }
        
        // 遍历查找匹配的枚举值
        for (E enumConstant : enumConstants) {
            if (enumConstant.getValue() != null && enumConstant.getValue().equals(value)) {
                return enumConstant;
            }
        }
        
        return null;
    }
    
    /**
     * 根据值获取对应的枚举实例（支持字符串转换）
     * 
     * <p>当传入的是字符串时，会尝试转换为目标类型</p>
     * <p>主要用于处理HTTP请求参数的转换</p>
     * 
     * @param <E> 枚举类型
     * @param enumClass 枚举类的Class对象
     * @param value 要转换的字符串值
     * @return 对应的枚举实例，如果找不到或转换失败则返回null
     */
    @SuppressWarnings("unchecked")
    static <E extends BaseEnum<?>> E fromString(Class<E> enumClass, String value) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        
        // 获取枚举类的所有常量
        E[] enumConstants = enumClass.getEnumConstants();
        if (enumConstants == null || enumConstants.length == 0) {
            return null;
        }
        
        // 获取第一个枚举常量来判断值的类型
        Object firstValue = enumConstants[0].getValue();
        
        try {
            if (firstValue instanceof Integer) {
                // 如果枚举值是Integer类型，尝试将字符串转换为Integer
                Integer intValue = Integer.valueOf(value.trim());
                return (E) fromValue((Class<BaseEnum<Integer>>) enumClass, intValue);
            } else if (firstValue instanceof String) {
                // 如果枚举值是String类型，直接使用字符串值
                return (E) fromValue((Class<BaseEnum<String>>) enumClass, value);
            } else if (firstValue instanceof Long) {
                // 如果枚举值是Long类型，尝试将字符串转换为Long
                Long longValue = Long.valueOf(value.trim());
                return (E) fromValue((Class<BaseEnum<Long>>) enumClass, longValue);
            } else if (firstValue instanceof Double) {
                // 如果枚举值是Double类型，尝试将字符串转换为Double
                Double doubleValue = Double.valueOf(value.trim());
                return (E) fromValue((Class<BaseEnum<Double>>) enumClass, doubleValue);
            }
        } catch (NumberFormatException e) {
            // 数字转换失败，返回null
            return null;
        }
        
        return null;
    }
    
    /**
     * 检查指定的值是否为有效的枚举值
     * 
     * @param <E> 枚举类型
     * @param <T> 枚举值类型
     * @param enumClass 枚举类的Class对象
     * @param value 要检查的值
     * @return 如果是有效的枚举值返回true，否则返回false
     */
    static <E extends BaseEnum<T>, T> boolean isValidValue(Class<E> enumClass, T value) {
        return fromValue(enumClass, value) != null;
    }
    
    /**
     * 获取枚举类的所有可能值
     * 
     * @param <E> 枚举类型
     * @param <T> 枚举值类型
     * @param enumClass 枚举类的Class对象
     * @return 所有枚举值的数组
     */
    @SuppressWarnings("unchecked")
    static <E extends BaseEnum<T>, T> T[] getAllValues(Class<E> enumClass) {
        E[] enumConstants = enumClass.getEnumConstants();
        if (enumConstants == null || enumConstants.length == 0) {
            return (T[]) new Object[0];
        }
        
        @SuppressWarnings("unchecked")
        T[] values = (T[]) java.lang.reflect.Array.newInstance(
            enumConstants[0].getValue().getClass(), 
            enumConstants.length
        );
        
        for (int i = 0; i < enumConstants.length; i++) {
            values[i] = enumConstants[i].getValue();
        }
        
        return values;
    }
}
