<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!-- 继承父项目配- 继承Spring Boot父项目获得版本管理和插件配置 -->
    <parent>
        <groupId>com.akey</groupId>
        <artifactId>akey</artifactId>
        <version>0.0.1-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <!--  -->
    <artifactId>akey-framework-web</artifactId>
    <name>akey-framework-web</name>
    <description>Framework-web 框架核心模块 - 对全局异常、Web等进行配置</description>

    <!-- 依赖配置 -->
    <dependencies>
        <!-- 框架核心模块依赖 - 提供数据层框架支持 -->
        <dependency>
            <groupId>com.akey</groupId>
            <artifactId>akey-framework-core</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- 公共模块依赖 -->
        <dependency>
            <groupId>com.akey</groupId>
            <artifactId>akey-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- Spring Boot Web启动- 提供Web开发支持，包括RESTful API、MVC框架等 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- Spring Boot 参数验证 - 提供Bean Validation支持 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- 配置处理- 提供配置文件处理功能，包括@ConfigurationProperties注解支持 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- Sa-Token 权限认证，在线文档：https://sa-token.cc -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-spring-boot3-starter</artifactId>
        </dependency>

        <!-- Sa-Token 整合 SpringAOP 实现注解鉴权 -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-spring-aop</artifactId>
        </dependency>

        <!-- Spring Boot AOP Starter - 用于支持谷歌验证码拦截注解 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <!-- Jackson JSR310 模块 - 支持 Java 8 时间类型序列化 -->
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>

        <!-- OSHI 系统资源监控库 - 用于获取系统CPU、内存、磁盘等信息 -->
        <dependency>
            <groupId>com.github.oshi</groupId>
            <artifactId>oshi-core</artifactId>
        </dependency>

    </dependencies>

    <!--
       构建配置
       继承父项目的插件配置，无需额外配置
   -->
    <build>
        <plugins>
            <!-- Maven编译插件 - 继承父配- 继承父配 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
