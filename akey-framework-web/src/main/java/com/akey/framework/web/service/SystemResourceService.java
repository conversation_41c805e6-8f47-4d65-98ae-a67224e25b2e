package com.akey.framework.web.service;

import com.akey.framework.web.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import oshi.SystemInfo;
import oshi.hardware.*;
import oshi.software.os.OperatingSystem;

import java.net.Inet4Address;
import java.net.URL;
import java.io.BufferedReader;
import java.io.InputStreamReader;

import java.lang.management.*;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 系统资源监控服务
 *
 * <p>使用OSHI库获取系统资源信息，提供降级方案</p>
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Slf4j
@Service
public class SystemResourceService {

    private static SystemInfo systemInfo;
    private static HardwareAbstractionLayer hardware;
    private static OperatingSystem operatingSystem;
    private static boolean oshiAvailable = false;

    static {
        try {
            systemInfo = new SystemInfo();
            hardware = systemInfo.getHardware();
            operatingSystem = systemInfo.getOperatingSystem();
            oshiAvailable = true;
            log.info("OSHI库初始化成功");
        } catch (Throwable e) {
            log.warn("OSHI库初始化失败，将使用Java标准库提供基本信息: {}", e.getMessage());
            oshiAvailable = false;
            systemInfo = null;
            hardware = null;
            operatingSystem = null;
        }
    }

    /**
     * 获取完整的系统资源信息
     *
     * @return 系统资源信息
     */
    public SystemResourceVO getSystemResource() {
        long startTime = System.currentTimeMillis();
        log.debug("开始收集系统资源信息，使用OSHI: {}", oshiAvailable);

        try {
            SystemResourceVO result = SystemResourceVO.success(oshiAvailable);

            // 收集各模块信息
            result.setCpuInfo(getCpuInfo());
            result.setMemoryInfo(getMemoryInfo());
            result.setServerInfo(getServerInfo());
            result.setJvmInfo(getJvmInfo());
            result.setDiskInfo(getDiskInfo());

            long duration = System.currentTimeMillis() - startTime;
            result.setCollectDuration(duration);

            log.debug("系统资源信息收集完成，耗时: {}ms", duration);
            return result;

        } catch (Exception e) {
            log.error("收集系统资源信息失败", e);
            return SystemResourceVO.error("收集系统资源信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取CPU信息
     *
     * @return CPU信息
     */
    public CpuInfoVO getCpuInfo() {
        log.debug("开始收集CPU信息");
        CpuInfoVO cpuInfo = new CpuInfoVO();

        if (oshiAvailable) {
            try {
                // 使用OSHI获取CPU信息
                getCpuInfoWithOshi(cpuInfo);
            } catch (Exception e) {
                log.error("获取CPU信息失败", e);
                throw new RuntimeException("无法获取CPU信息，OSHI库不可用", e);
            }
        } else {
            throw new RuntimeException("OSHI库未初始化，无法获取CPU信息");
        }

        log.debug("CPU信息收集完成");
        return cpuInfo;
    }

    /**
     * 获取内存信息
     *
     * @return 内存信息
     */
    public MemoryInfoVO getMemoryInfo() {
        log.debug("开始收集内存信息");
        MemoryInfoVO memoryInfo = new MemoryInfoVO();

        try {
            // 系统内存信息
            if (oshiAvailable) {
                getSystemMemoryWithOshi(memoryInfo);
            } else {
                throw new RuntimeException("OSHI库未初始化，无法获取内存信息");
            }

            // JVM内存信息（总是使用Java标准库）
            getJvmMemoryInfo(memoryInfo);

        } catch (Exception e) {
            log.error("获取内存信息失败", e);
            // 至少获取JVM内存信息
            getJvmMemoryInfo(memoryInfo);
        }

        log.debug("内存信息收集完成");
        return memoryInfo;
    }

    /**
     * 获取服务器基本信息
     *
     * @return 服务器信息
     */
    public ServerInfoVO getServerInfo() {
        log.debug("开始收集服务器信息");
        ServerInfoVO serverInfo = new ServerInfoVO();

        try {
            // 基本信息（使用Java标准库）
            getBasicServerInfo(serverInfo);

            // 网络信息
            getNetworkInfo(serverInfo);

            // 系统启动时间
            if (oshiAvailable) {
                getSystemUptimeWithOshi(serverInfo);
                // 获取OSHI特有的系统信息
                getOshiSpecificServerInfo(serverInfo);
            } else {
                throw new RuntimeException("OSHI库未初始化，无法获取服务器信息");
            }

        } catch (Exception e) {
            log.error("获取服务器信息失败", e);
            // 至少获取基本信息
            getBasicServerInfo(serverInfo);
        }

        log.debug("服务器信息收集完成");
        return serverInfo;
    }

    /**
     * 获取JVM信息
     *
     * @return JVM信息
     */
    public JvmInfoVO getJvmInfo() {
        log.debug("开始收集JVM信息");
        JvmInfoVO jvmInfo = new JvmInfoVO();

        try {
            // JVM基本信息
            getBasicJvmInfo(jvmInfo);

            // 系统属性
            getSystemProperties(jvmInfo);

            // 类加载信息
            getClassLoadingInfo(jvmInfo);

            // 线程信息
            getThreadInfo(jvmInfo);

        } catch (Exception e) {
            log.error("获取JVM信息失败", e);
        }

        log.debug("JVM信息收集完成");
        return jvmInfo;
    }

    /**
     * 获取磁盘信息
     *
     * @return 磁盘信息
     */
    public DiskInfoVO getDiskInfo() {
        log.debug("开始收集磁盘信息");
        DiskInfoVO diskInfo = new DiskInfoVO();

        if (oshiAvailable) {
            try {
                getDiskInfoWithOshi(diskInfo);
            } catch (Exception e) {
                log.error("获取磁盘信息失败", e);
                throw new RuntimeException("无法获取磁盘信息，OSHI库不可用", e);
            }
        } else {
            throw new RuntimeException("OSHI库未初始化，无法获取磁盘信息");
        }

        log.debug("磁盘信息收集完成");
        return diskInfo;
    }

    /**
     * 使用OSHI获取CPU信息
     */
    private void getCpuInfoWithOshi(CpuInfoVO cpuInfo) {
        if (hardware == null) {
            throw new RuntimeException("OSHI hardware instance is null");
        }

        CentralProcessor processor = hardware.getProcessor();
        CentralProcessor.ProcessorIdentifier processorId = processor.getProcessorIdentifier();

        // 基本信息
        cpuInfo.setModel(processorId.getModel());
        cpuInfo.setVendor(processorId.getVendor());
        cpuInfo.setFrequency(processorId.getVendorFreq());

        // OSHI特有的详细信息
        cpuInfo.setArchitecture(processorId.getMicroarchitecture());
        cpuInfo.setFamily(processorId.getFamily());
        cpuInfo.setStepping(processorId.getStepping());

        // 核心数
        cpuInfo.setPhysicalCores(processor.getPhysicalProcessorCount());
        cpuInfo.setLogicalCores(processor.getLogicalProcessorCount());

        // CPU使用率 - 需要两次采样来计算使用率
        long[] prevTicks = processor.getSystemCpuLoadTicks();
        try {
            // 等待一小段时间进行第二次采样
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 获取每个CPU核心的负载
        long[] currentTicks = processor.getSystemCpuLoadTicks();
        double systemCpuLoad = processor.getSystemCpuLoadBetweenTicks(prevTicks);
        CpuInfoVO.CpuUsageVO usage = new CpuInfoVO.CpuUsageVO();

        // OSHI 提供系统整体CPU使用率
        if (systemCpuLoad >= 0) {
            usage.setTotalPercent(systemCpuLoad * 100);
        }

        // 设置详细的CPU使用率信息
        long[] ticks = processor.getSystemCpuLoadTicks();
        long totalTicks = 0;
        for (long tick : ticks) {
            totalTicks += tick;
        }

        if (totalTicks > 0) {
            usage.setUserPercent((double) ticks[CentralProcessor.TickType.USER.getIndex()] / totalTicks * 100);
            usage.setSystemPercent((double) ticks[CentralProcessor.TickType.SYSTEM.getIndex()] / totalTicks * 100);
            usage.setIdlePercent((double) ticks[CentralProcessor.TickType.IDLE.getIndex()] / totalTicks * 100);
            usage.setWaitPercent((double) ticks[CentralProcessor.TickType.IOWAIT.getIndex()] / totalTicks * 100);
            usage.setIrqPercent((double) ticks[CentralProcessor.TickType.IRQ.getIndex()] / totalTicks * 100);
            usage.setSoftIrqPercent((double) ticks[CentralProcessor.TickType.SOFTIRQ.getIndex()] / totalTicks * 100);
        }

        cpuInfo.setUsage(usage);

        // 负载平均值
        try {
            double[] loadAvg = processor.getSystemLoadAverage(3);
            if (loadAvg[0] >= 0) {
                CpuInfoVO.LoadAverageVO loadAverage = new CpuInfoVO.LoadAverageVO();
                loadAverage.setOneMinute(loadAvg[0]);
                loadAverage.setFiveMinutes(loadAvg.length > 1 ? loadAvg[1] : -1);
                loadAverage.setFifteenMinutes(loadAvg.length > 2 ? loadAvg[2] : -1);
                cpuInfo.setLoadAverage(loadAverage);
            }
        } catch (Exception e) {
            log.debug("获取负载平均值失败: {}", e.getMessage());
        }
    }


    /**
     * 使用OSHI获取系统内存信息
     */
    private void getSystemMemoryWithOshi(MemoryInfoVO memoryInfo) {
        if (hardware == null) {
            throw new RuntimeException("OSHI hardware instance is null");
        }

        GlobalMemory memory = hardware.getMemory();
        VirtualMemory virtualMemory = memory.getVirtualMemory();

        MemoryInfoVO.SystemMemoryVO systemMemory = new MemoryInfoVO.SystemMemoryVO();

        long totalMemory = memory.getTotal();
        long availableMemory = memory.getAvailable();
        long usedMemory = totalMemory - availableMemory;

        systemMemory.setTotalBytes(totalMemory);
        systemMemory.setUsedBytes(usedMemory);
        systemMemory.setFreeBytes(availableMemory);
        systemMemory.setActualUsedBytes(usedMemory);
        systemMemory.setActualFreeBytes(availableMemory);
        systemMemory.setUsagePercent((double) usedMemory / totalMemory * 100);

        systemMemory.setTotalFormatted(formatBytes(totalMemory));
        systemMemory.setUsedFormatted(formatBytes(usedMemory));
        systemMemory.setFreeFormatted(formatBytes(availableMemory));

        memoryInfo.setSystemMemory(systemMemory);

        // 设置交换内存信息
        MemoryInfoVO.SwapMemoryVO swapMemory = new MemoryInfoVO.SwapMemoryVO();
        long totalSwap = virtualMemory.getSwapTotal();
        long usedSwap = virtualMemory.getSwapUsed();
        long freeSwap = totalSwap - usedSwap;

        swapMemory.setTotalBytes(totalSwap);
        swapMemory.setUsedBytes(usedSwap);
        swapMemory.setFreeBytes(freeSwap);
        if (totalSwap > 0) {
            swapMemory.setUsagePercent((double) usedSwap / totalSwap * 100);
        }

        swapMemory.setTotalFormatted(formatBytes(totalSwap));
        swapMemory.setUsedFormatted(formatBytes(usedSwap));
        swapMemory.setFreeFormatted(formatBytes(freeSwap));

        memoryInfo.setSwapMemory(swapMemory);
    }


    /**
     * 获取JVM内存信息
     */
    private void getJvmMemoryInfo(MemoryInfoVO memoryInfo) {
        MemoryInfoVO.JvmMemoryVO jvmMemory = new MemoryInfoVO.JvmMemoryVO();

        // 堆内存
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();

        MemoryInfoVO.JvmMemoryVO.HeapMemoryVO heapMemory = new MemoryInfoVO.JvmMemoryVO.HeapMemoryVO();
        heapMemory.setMaxBytes(heapUsage.getMax());
        heapMemory.setCommittedBytes(heapUsage.getCommitted());
        heapMemory.setUsedBytes(heapUsage.getUsed());
        heapMemory.setFreeBytes(heapUsage.getMax() - heapUsage.getUsed());
        heapMemory.setUsagePercent((double) heapUsage.getUsed() / heapUsage.getMax() * 100);

        heapMemory.setMaxFormatted(formatBytes(heapUsage.getMax()));
        heapMemory.setUsedFormatted(formatBytes(heapUsage.getUsed()));
        heapMemory.setFreeFormatted(formatBytes(heapUsage.getMax() - heapUsage.getUsed()));

        jvmMemory.setHeapMemory(heapMemory);

        // 非堆内存
        MemoryUsage nonHeapUsage = memoryBean.getNonHeapMemoryUsage();

        MemoryInfoVO.JvmMemoryVO.NonHeapMemoryVO nonHeapMemory = new MemoryInfoVO.JvmMemoryVO.NonHeapMemoryVO();
        nonHeapMemory.setMaxBytes(nonHeapUsage.getMax());
        nonHeapMemory.setCommittedBytes(nonHeapUsage.getCommitted());
        nonHeapMemory.setUsedBytes(nonHeapUsage.getUsed());
        if (nonHeapUsage.getMax() > 0) {
            nonHeapMemory.setUsagePercent((double) nonHeapUsage.getUsed() / nonHeapUsage.getMax() * 100);
        }

        nonHeapMemory.setMaxFormatted(formatBytes(nonHeapUsage.getMax()));
        nonHeapMemory.setUsedFormatted(formatBytes(nonHeapUsage.getUsed()));

        jvmMemory.setNonHeapMemory(nonHeapMemory);

        // GC信息
        getGcInfo(jvmMemory);

        memoryInfo.setJvmMemory(jvmMemory);
    }

    /**
     * 获取垃圾回收信息
     */
    private void getGcInfo(MemoryInfoVO.JvmMemoryVO jvmMemory) {
        List<GarbageCollectorMXBean> gcBeans = ManagementFactory.getGarbageCollectorMXBeans();

        MemoryInfoVO.JvmMemoryVO.GcInfoVO gcInfo = new MemoryInfoVO.JvmMemoryVO.GcInfoVO();
        long totalGcCount = 0;
        long totalGcTime = 0;
        long youngGcCount = 0;
        long youngGcTime = 0;
        long oldGcCount = 0;
        long oldGcTime = 0;

        for (GarbageCollectorMXBean gcBean : gcBeans) {
            long count = gcBean.getCollectionCount();
            long time = gcBean.getCollectionTime();

            totalGcCount += count;
            totalGcTime += time;

            String name = gcBean.getName().toLowerCase();
            if (name.contains("young") || name.contains("copy") || name.contains("parnew")) {
                youngGcCount += count;
                youngGcTime += time;
            } else if (name.contains("old") || name.contains("tenured") || name.contains("cms") || name.contains("g1")) {
                oldGcCount += count;
                oldGcTime += time;
            }
        }

        gcInfo.setTotalGcCount(totalGcCount);
        gcInfo.setTotalGcTime(totalGcTime);
        gcInfo.setYoungGcCount(youngGcCount);
        gcInfo.setYoungGcTime(youngGcTime);
        gcInfo.setOldGcCount(oldGcCount);
        gcInfo.setOldGcTime(oldGcTime);

        jvmMemory.setGcInfo(gcInfo);
    }

    /**
     * 获取服务器基本信息
     */
    private void getBasicServerInfo(ServerInfoVO serverInfo) {
        try {
            serverInfo.setHostname(InetAddress.getLocalHost().getHostName());
        } catch (Exception e) {
            serverInfo.setHostname("Unknown");
        }

        serverInfo.setOsName(System.getProperty("os.name"));
        serverInfo.setOsVersion(System.getProperty("os.version"));
        serverInfo.setOsArch(System.getProperty("os.arch"));
        serverInfo.setSystemArch(System.getProperty("sun.arch.data.model") + "位");

        serverInfo.setCurrentTime(LocalDateTime.now());
        serverInfo.setTimeZone(TimeZone.getDefault().getID());
        serverInfo.setUserName(System.getProperty("user.name"));
        serverInfo.setUserHome(System.getProperty("user.home"));
        serverInfo.setTempDir(System.getProperty("java.io.tmpdir"));
        serverInfo.setFileSeparator(System.getProperty("file.separator"));
        serverInfo.setPathSeparator(System.getProperty("path.separator"));
        serverInfo.setLineSeparator(System.getProperty("line.separator"));
    }

    /**
     * 获取网络信息
     */
    private void getNetworkInfo(ServerInfoVO serverInfo) {
        try {
            List<String> ipAddresses = new ArrayList<>();
            String internalIp = null;
            String externalIp = null;

            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface networkInterface = networkInterfaces.nextElement();
                if (networkInterface.isLoopback() || !networkInterface.isUp()) {
                    continue;
                }

                Enumeration<InetAddress> inetAddresses = networkInterface.getInetAddresses();
                while (inetAddresses.hasMoreElements()) {
                    InetAddress inetAddress = inetAddresses.nextElement();
                    if (inetAddress.isLoopbackAddress()) {
                        continue;
                    }

                    // 只处理IPv4地址
                    if (!(inetAddress instanceof java.net.Inet4Address)) {
                        continue;
                    }

                    String ip = inetAddress.getHostAddress();
                    ipAddresses.add(ip);

                    if (inetAddress.isSiteLocalAddress()) {
                        if (internalIp == null) {
                            internalIp = ip;
                        }
                    } else {
                        if (externalIp == null) {
                            externalIp = ip;
                        }
                    }
                }
            }

            serverInfo.setIpAddresses(ipAddresses);
            serverInfo.setInternalIp(internalIp != null ? internalIp : "N/A");

            // 如果通过网络接口获取不到外网IP，尝试通过第三方服务获取
            if (externalIp == null) {
                log.debug("通过网络接口未获取到外网IP，尝试通过第三方服务获取");
                externalIp = getExternalIp();
            }
            serverInfo.setExternalIp(externalIp);

        } catch (Exception e) {
            log.error("获取网络信息失败", e);
        }
    }

    /**
     * 使用OSHI获取系统启动时间
     */
    private void getSystemUptimeWithOshi(ServerInfoVO serverInfo) {
        if (operatingSystem == null) {
            throw new RuntimeException("OSHI operating system instance is null");
        }

        long bootTime = operatingSystem.getSystemBootTime() * 1000; // OSHI返回秒，转换为毫秒
        long currentTime = System.currentTimeMillis();
        long uptimeSeconds = (currentTime - bootTime) / 1000;

        serverInfo.setSystemUptimeSeconds(uptimeSeconds);
        serverInfo.setSystemUptimeFormatted(formatDuration(uptimeSeconds));
        serverInfo.setSystemBootTime(LocalDateTime.ofInstant(
                Instant.ofEpochMilli(bootTime), ZoneId.systemDefault()));
    }

    /**
     * 获取OSHI特有的服务器信息
     */
    private void getOshiSpecificServerInfo(ServerInfoVO serverInfo) {
        if (operatingSystem == null) {
            return;
        }

        try {
            // 操作系统详细信息
            serverInfo.setOsManufacturer(operatingSystem.getManufacturer());
            // OSHI 6.x 版本中可能没有 getBuildNumber 方法，使用版本信息代替
            serverInfo.setOsBuildNumber(operatingSystem.getVersionInfo().getBuildNumber());

            // 进程和线程统计
            serverInfo.setProcessCount(operatingSystem.getProcessCount());
            serverInfo.setThreadCount(operatingSystem.getThreadCount());

        } catch (Exception e) {
            log.debug("获取OSHI特有服务器信息失败: {}", e.getMessage());
        }
    }


    /**
     * 获取JVM基本信息
     */
    private void getBasicJvmInfo(JvmInfoVO jvmInfo) {
        RuntimeMXBean runtimeBean = ManagementFactory.getRuntimeMXBean();

        jvmInfo.setJvmName(runtimeBean.getVmName());
        jvmInfo.setJvmVersion(runtimeBean.getVmVersion());
        jvmInfo.setJvmVendor(runtimeBean.getVmVendor());
        jvmInfo.setJavaVersion(System.getProperty("java.version"));
        jvmInfo.setJavaHome(System.getProperty("java.home"));
        jvmInfo.setClassPath(System.getProperty("java.class.path"));
        jvmInfo.setLibraryPath(System.getProperty("java.library.path"));

        long startTime = runtimeBean.getStartTime();
        jvmInfo.setJvmStartTime(LocalDateTime.ofInstant(
                Instant.ofEpochMilli(startTime), ZoneId.systemDefault()));

        long uptime = runtimeBean.getUptime() / 1000;
        jvmInfo.setJvmUptimeSeconds(uptime);
        jvmInfo.setJvmUptimeFormatted(formatDuration(uptime));

        jvmInfo.setJvmArguments(runtimeBean.getInputArguments());
    }

    /**
     * 获取系统属性
     */
    private void getSystemProperties(JvmInfoVO jvmInfo) {
        JvmInfoVO.SystemPropertiesVO systemProperties = new JvmInfoVO.SystemPropertiesVO();

        systemProperties.setUserName(System.getProperty("user.name"));
        systemProperties.setUserHome(System.getProperty("user.home"));
        systemProperties.setUserDir(System.getProperty("user.dir"));
        systemProperties.setOsName(System.getProperty("os.name"));
        systemProperties.setOsVersion(System.getProperty("os.version"));
        systemProperties.setOsArch(System.getProperty("os.arch"));
        systemProperties.setFileEncoding(System.getProperty("file.encoding"));
        systemProperties.setTimeZone(System.getProperty("user.timezone"));

        jvmInfo.setSystemProperties(systemProperties);
    }

    /**
     * 获取类加载信息
     */
    private void getClassLoadingInfo(JvmInfoVO jvmInfo) {
        ClassLoadingMXBean classLoadingBean = ManagementFactory.getClassLoadingMXBean();

        JvmInfoVO.ClassLoadingVO classLoading = new JvmInfoVO.ClassLoadingVO();
        classLoading.setTotalLoadedClassCount(classLoadingBean.getTotalLoadedClassCount());
        classLoading.setLoadedClassCount(classLoadingBean.getLoadedClassCount());
        classLoading.setUnloadedClassCount(classLoadingBean.getUnloadedClassCount());
        classLoading.setVerboseEnabled(classLoadingBean.isVerbose());

        jvmInfo.setClassLoading(classLoading);
    }

    /**
     * 获取线程信息
     */
    private void getThreadInfo(JvmInfoVO jvmInfo) {
        ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();

        JvmInfoVO.ThreadInfoVO threadInfo = new JvmInfoVO.ThreadInfoVO();
        threadInfo.setActiveThreadCount(threadBean.getThreadCount());
        threadInfo.setPeakThreadCount(threadBean.getPeakThreadCount());
        threadInfo.setTotalStartedThreadCount(threadBean.getTotalStartedThreadCount());
        threadInfo.setDaemonThreadCount(threadBean.getDaemonThreadCount());

        long[] deadlockedThreads = threadBean.findDeadlockedThreads();
        threadInfo.setDeadlockedThreadCount(deadlockedThreads != null ? deadlockedThreads.length : 0);

        threadInfo.setThreadContentionMonitoringSupported(threadBean.isThreadContentionMonitoringSupported());
        if (threadBean.isThreadContentionMonitoringSupported()) {
            threadInfo.setThreadContentionMonitoringEnabled(threadBean.isThreadContentionMonitoringEnabled());
        }

        threadInfo.setThreadCpuTimeSupported(threadBean.isThreadCpuTimeSupported());
        if (threadBean.isThreadCpuTimeSupported()) {
            threadInfo.setThreadCpuTimeEnabled(threadBean.isThreadCpuTimeEnabled());
        }

        jvmInfo.setThreadInfo(threadInfo);
    }

    /**
     * 使用OSHI获取磁盘信息
     */
    private void getDiskInfoWithOshi(DiskInfoVO diskInfo) {
        if (operatingSystem == null) {
            throw new RuntimeException("OSHI operating system instance is null");
        }

        // 磁盘分区信息 - 只获取实际使用的硬盘
        List<oshi.software.os.OSFileStore> fileStores = operatingSystem.getFileSystem().getFileStores();
        List<DiskInfoVO.DiskPartitionVO> partitions = new ArrayList<>();

        for (oshi.software.os.OSFileStore fs : fileStores) {
            // 过滤掉系统分区和虚拟分区
            if (!isUserDataPartition(fs)) {
                continue;
            }

            DiskInfoVO.DiskPartitionVO partition = new DiskInfoVO.DiskPartitionVO();
            partition.setDeviceName(fs.getName());
            partition.setMountPoint(fs.getMount());
            partition.setFileSystemType(fs.getType());
            partition.setPartitionType("Local Disk");

            long totalBytes = fs.getTotalSpace();
            long freeBytes = fs.getUsableSpace();
            long usedBytes = totalBytes - freeBytes;

            // 只显示有实际容量的分区
            if (totalBytes > 0) {
                partition.setTotalBytes(totalBytes);
                partition.setUsedBytes(usedBytes);
                partition.setFreeBytes(freeBytes);
                partition.setUsagePercent((double) usedBytes / totalBytes * 100);

                partition.setTotalFormatted(formatBytes(totalBytes));
                partition.setUsedFormatted(formatBytes(usedBytes));
                partition.setFreeFormatted(formatBytes(freeBytes));

                partitions.add(partition);
            }
        }

        diskInfo.setPartitions(partitions);
    }


    /**
     * 判断是否为用户数据分区（过滤掉系统分区和虚拟分区）
     */
    private boolean isUserDataPartition(oshi.software.os.OSFileStore fs) {
        String mount = fs.getMount().toLowerCase();
        String name = fs.getName().toLowerCase();
        String type = fs.getType().toLowerCase();

        // 排除系统分区
        if (mount.equals("/system") || mount.equals("/private") ||
                mount.startsWith("/system/") || mount.startsWith("/private/") ||
                mount.equals("/usr") || mount.equals("/var") || mount.equals("/tmp") ||
                mount.equals("/boot") || mount.equals("/proc") || mount.equals("/dev") ||
                mount.equals("/sys") || mount.equals("/run")) {
            return false;
        }

        // 排除虚拟分区和特殊分区
        if (name.contains("xart") || name.contains("preboot") ||
                name.contains("recovery") || name.contains("efi") ||
                name.contains("boot") || name.contains("system") ||
                type.contains("devfs") || type.contains("tmpfs") ||
                type.contains("proc") || type.contains("sysfs")) {
            return false;
        }

        // 排除容量为0或很小的分区（小于100MB）
        if (fs.getTotalSpace() < 100 * 1024 * 1024) {
            return false;
        }

        // 只保留主要的文件系统类型
        return type.contains("apfs") || type.contains("hfs") ||
                type.contains("ntfs") || type.contains("fat") ||
                type.contains("ext") || type.contains("xfs") ||
                type.contains("btrfs");
    }

    /**
     * 通过第三方服务获取外网IP地址
     *
     * @return 外网IP地址，获取失败返回"N/A"
     */
    public static String getExternalIp() {
        // 可用的第三方IP查询服务列表
        String[] ipServices = {
            "https://checkip.amazonaws.com",
            "https://api.ipify.org",
            "https://ipinfo.io/ip",
            "https://ifconfig.me/ip"
        };

        for (String service : ipServices) {
            try {
                URL url = new URL(service);
                try (BufferedReader in = new BufferedReader(
                        new InputStreamReader(url.openStream()))) {
                    String ip = in.readLine();
                    if (ip != null && !ip.isEmpty()) {
                        String trimmedIp = ip.trim();
                        // 验证是否为有效的IPv4地址
                        if (isValidIPv4(trimmedIp)) {
                            return trimmedIp;
                        }
                    }
                }
            } catch (Exception e) {
                // 如果当前服务失败，尝试下一个
                log.debug("获取外网IP失败，服务: {}, 错误: {}", service, e.getMessage());
                continue;
            }
        }
        return "N/A";
    }

    /**
     * 验证是否为有效的IPv4地址
     *
     * @param ip IP地址字符串
     * @return 是否为有效的IPv4地址
     */
    private static boolean isValidIPv4(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }

        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }

        try {
            for (String part : parts) {
                int num = Integer.parseInt(part);
                if (num < 0 || num > 255) {
                    return false;
                }
            }
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 格式化字节数
     */
    private String formatBytes(long bytes) {
        if (bytes < 0) return "Unknown";

        String[] units = {"B", "KB", "MB", "GB", "TB", "PB"};
        int unitIndex = 0;
        double size = bytes;

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        return String.format("%.2f %s", size, units[unitIndex]);
    }

    /**
     * 格式化时间间隔
     */
    private String formatDuration(long seconds) {
        long days = TimeUnit.SECONDS.toDays(seconds);
        long hours = TimeUnit.SECONDS.toHours(seconds) % 24;
        long minutes = TimeUnit.SECONDS.toMinutes(seconds) % 60;
        long secs = seconds % 60;

        StringBuilder sb = new StringBuilder();
        if (days > 0) {
            sb.append(days).append("天");
        }
        if (hours > 0) {
            sb.append(hours).append("小时");
        }
        if (minutes > 0) {
            sb.append(minutes).append("分钟");
        }
        if (secs > 0 || sb.length() == 0) {
            sb.append(secs).append("秒");
        }

        return sb.toString();
    }
}
