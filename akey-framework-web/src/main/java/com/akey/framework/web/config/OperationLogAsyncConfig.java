package com.akey.framework.web.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 操作日志异步配置
 * 
 * <p>配置操作日志异步记录的线程池</p>
 * <p>确保日志记录不影响主业务性能</p>
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Configuration
@EnableAsync
public class OperationLogAsyncConfig {

    /**
     * 操作日志异步执行器
     * 
     * <p>专门用于操作日志异步记录的线程池</p>
     * <p>配置合理的线程数和队列大小，避免影响系统性能</p>
     * 
     * @return 线程池执行器
     */
    @Bean("operationLogExecutor")
    public Executor operationLogExecutor() {
        log.info("初始化操作日志异步线程池");
        
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数：CPU核心数
        int corePoolSize = Runtime.getRuntime().availableProcessors();
        executor.setCorePoolSize(corePoolSize);
        
        // 最大线程数：核心线程数的2倍
        executor.setMaxPoolSize(corePoolSize * 2);
        
        // 队列容量：1000个任务
        executor.setQueueCapacity(1000);
        
        // 线程空闲时间：60秒
        executor.setKeepAliveSeconds(60);
        
        // 线程名前缀
        executor.setThreadNamePrefix("operation-log-");
        
        // 拒绝策略：调用者运行策略（在调用者线程中运行被拒绝的任务）
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待所有任务完成后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间：30秒
        executor.setAwaitTerminationSeconds(30);
        
        // 初始化线程池
        executor.initialize();
        
        log.info("操作日志异步线程池初始化完成, corePoolSize: {}, maxPoolSize: {}, queueCapacity: {}", 
                corePoolSize, corePoolSize * 2, 1000);
        
        return executor;
    }
}
