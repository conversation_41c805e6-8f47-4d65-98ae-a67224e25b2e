package com.akey.framework.web.aspect;

import com.akey.framework.web.annotation.GoogleAuthRequired;
import com.akey.framework.web.config.GoogleAuthProperties;
import com.akey.framework.web.exception.GoogleAuthException;
import com.akey.framework.web.service.GoogleAuthVerificationService;
import com.akey.framework.web.util.GoogleAuthCodeExtractor;
import com.akey.framework.web.vo.GoogleAuthVerificationResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 谷歌验证码AOP切面
 * 
 * <p>拦截带有@GoogleAuthRequired注解的方法，进行谷歌验证码验证</p>
 * <p>支持类级别和方法级别注解，方法级别优先于类级别</p>
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@Aspect
@Component
@Order(100) // 设置切面执行顺序，数值越小优先级越高
@RequiredArgsConstructor
public class GoogleAuthAspect {

    private final GoogleAuthVerificationService verificationService;
    private final GoogleAuthCodeExtractor codeExtractor;
    private final GoogleAuthProperties properties;

    /**
     * 环绕通知：拦截带有@GoogleAuthRequired注解的方法或类
     */
    @Around("@annotation(com.akey.framework.web.annotation.GoogleAuthRequired) || " +
            "@within(com.akey.framework.web.annotation.GoogleAuthRequired)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        
        // 1. 检查是否启用谷歌验证拦截
        if (!properties.isEnabled()) {
            log.debug("谷歌验证拦截已禁用，跳过验证");
            return joinPoint.proceed();
        }

        // 2. 获取有效的注解配置（方法级别优先于类级别）
        GoogleAuthRequired config = getEffectiveAnnotation(joinPoint);
        
        if (config == null) {
            // 理论上不会发生，因为切点表达式已经过滤了
            log.warn("未找到@GoogleAuthRequired注解，跳过验证");
            return joinPoint.proceed();
        }

        // 3. 记录拦截日志
        String methodName = getMethodName(joinPoint);
        log.debug("拦截到需要谷歌验证的方法: {}, required: {}, timeWindow: {}", 
                 methodName, config.required(), config.timeWindow());

        // 4. 执行谷歌验证码验证
        performGoogleAuthVerification(config, methodName);

        // 5. 验证通过，继续执行目标方法
        return joinPoint.proceed();
    }

    /**
     * 获取有效的注解配置（方法级别优先）
     */
    private GoogleAuthRequired getEffectiveAnnotation(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // 1. 优先获取方法级别的注解
        GoogleAuthRequired methodAnnotation = method.getAnnotation(GoogleAuthRequired.class);
        if (methodAnnotation != null) {
            log.debug("使用方法级别的@GoogleAuthRequired注解");
            return methodAnnotation;
        }

        // 2. 获取类级别的注解
        Class<?> targetClass = joinPoint.getTarget().getClass();
        GoogleAuthRequired classAnnotation = targetClass.getAnnotation(GoogleAuthRequired.class);
        if (classAnnotation != null) {
            log.debug("使用类级别的@GoogleAuthRequired注解");
            return classAnnotation;
        }

        return null;
    }

    /**
     * 执行谷歌验证码验证
     */
    private void performGoogleAuthVerification(GoogleAuthRequired config, String methodName) {
        try {
            // 1. 提取验证码
            String code = codeExtractor.extractCode();
            log.debug("提取到的验证码: {}", code != null ? "******" : "null");

            // 2. 执行验证
            GoogleAuthVerificationResult result = verificationService.verifyCurrentUser(
                code, config.required(), config.timeWindow());

            // 3. 处理验证结果
            if (result.shouldThrowException()) {
                // 验证失败，抛出异常
                String errorMessage = config.message();
                if (result.getErrorMessage() != null) {
                    errorMessage = result.getErrorMessage();
                }
                
                log.warn("谷歌验证码验证失败, method: {}, errorCode: {}, errorMessage: {}", 
                        methodName, result.getErrorCode(), result.getErrorMessage());
                
                throw new GoogleAuthException(result.getErrorCode(), errorMessage);
            }

            // 4. 记录验证成功日志
            if (result.isSkipped()) {
                log.debug("跳过谷歌验证码验证, method: {}", methodName);
            } else {
                log.info("谷歌验证码验证成功, method: {}", methodName);
            }

        } catch (GoogleAuthException e) {
            // 重新抛出谷歌验证异常
            throw e;
        } catch (Exception e) {
            // 其他异常转换为谷歌验证异常
            log.error("谷歌验证码验证过程发生异常, method: {}", methodName, e);
            throw new GoogleAuthException("GOOGLE_AUTH_VERIFICATION_ERROR", "验证过程发生异常", e);
        }
    }

    /**
     * 获取方法名称
     */
    private String getMethodName(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        return signature.getDeclaringType().getSimpleName() + "." + signature.getName();
    }
}
