package com.akey.framework.web.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

/**
 * 谷歌验证码工具类
 *
 * <p>提供基于RFC 6238 (TOTP) 和 RFC 4226 (HOTP) 标准的谷歌验证码功能</p>
 * <p>支持生成密钥、生成验证码、验证验证码等功能</p>
 * <p>兼容Google Authenticator、Microsoft Authenticator等主流验证器应用</p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
public class GoogleAuthUtil {

    /**
     * 密钥长度（字节）
     * RFC 4226建议至少128位（16字节），这里使用160位（20字节）提供更好的安全性
     */
    private static final int SECRET_KEY_LENGTH = 20;

    /**
     * 验证码长度（位数）
     * 标准为6位数字
     */
    private static final int CODE_DIGITS = 6;

    /**
     * TOTP时间步长（秒）
     * 标准为30秒
     */
    private static final int TIME_STEP = 30;

    /**
     * HMAC算法
     */
    private static final String HMAC_ALGORITHM = "HmacSHA1";

    /**
     * Base32编码字符集
     */
    private static final String BASE32_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567";

    /**
     * 生成随机密钥
     *
     * <p>生成一个160位的随机密钥，使用Base32编码</p>
     * <p>该密钥可用于TOTP和HOTP验证</p>
     *
     * @return Base32编码的密钥字符串
     */
    public static String generateSecretKey() {
        try {
            SecureRandom random = new SecureRandom();
            byte[] keyBytes = new byte[SECRET_KEY_LENGTH];
            random.nextBytes(keyBytes);

            String secretKey = encodeBase32(keyBytes);
            log.debug("生成谷歌验证密钥成功，长度: {} 字节", SECRET_KEY_LENGTH);
            return secretKey;
        } catch (Exception e) {
            log.error("生成谷歌验证密钥失败", e);
            throw new RuntimeException("生成谷歌验证密钥失败", e);
        }
    }

    /**
     * 生成TOTP验证码（基于时间）
     *
     * <p>基于当前时间生成6位数字验证码</p>
     * <p>验证码每30秒更新一次</p>
     *
     * @param secretKey Base32编码的密钥
     * @return 6位数字验证码
     */
    public static String generateTOTP(String secretKey) {
        if (!StringUtils.hasText(secretKey)) {
            throw new IllegalArgumentException("密钥不能为空");
        }

        try {
            long timeCounter = System.currentTimeMillis() / 1000 / TIME_STEP;
            return generateHOTP(secretKey, timeCounter);
        } catch (Exception e) {
            log.error("生成TOTP验证码失败，secretKey: {}", secretKey, e);
            throw new RuntimeException("生成TOTP验证码失败", e);
        }
    }

    /**
     * 生成HOTP验证码（基于计数器）
     *
     * <p>基于指定计数器值生成6位数字验证码</p>
     * <p>每次使用后计数器应该递增</p>
     *
     * @param secretKey Base32编码的密钥
     * @param counter 计数器值
     * @return 6位数字验证码
     */
    public static String generateHOTP(String secretKey, long counter) {
        if (!StringUtils.hasText(secretKey)) {
            throw new IllegalArgumentException("密钥不能为空");
        }

        try {
            byte[] keyBytes = decodeBase32(secretKey);
            byte[] counterBytes = longToBytes(counter);

            byte[] hash = hmacSha1(keyBytes, counterBytes);
            int offset = hash[hash.length - 1] & 0x0F;

            int code = ((hash[offset] & 0x7F) << 24) |
                      ((hash[offset + 1] & 0xFF) << 16) |
                      ((hash[offset + 2] & 0xFF) << 8) |
                      (hash[offset + 3] & 0xFF);

            code = code % (int) Math.pow(10, CODE_DIGITS);

            return String.format("%0" + CODE_DIGITS + "d", code);
        } catch (Exception e) {
            log.error("生成HOTP验证码失败，secretKey: {}, counter: {}", secretKey, counter, e);
            throw new RuntimeException("生成HOTP验证码失败", e);
        }
    }

    /**
     * 验证TOTP验证码（基于时间）
     *
     * <p>验证用户输入的验证码是否正确</p>
     * <p>支持时间窗口容错，允许前后1个时间步长的验证码</p>
     *
     * @param secretKey Base32编码的密钥
     * @param userCode 用户输入的验证码
     * @return 验证成功返回true，失败返回false
     */
    public static boolean verifyTOTP(String secretKey, String userCode) {
        return verifyTOTP(secretKey, userCode, 1);
    }

    /**
     * 验证TOTP验证码（基于时间，支持自定义窗口）
     *
     * <p>验证用户输入的验证码是否正确</p>
     * <p>支持自定义时间窗口容错</p>
     *
     * @param secretKey Base32编码的密钥
     * @param userCode 用户输入的验证码
     * @param window 时间窗口大小（允许前后多少个时间步长）
     * @return 验证成功返回true，失败返回false
     */
    public static boolean verifyTOTP(String secretKey, String userCode, int window) {
        if (!StringUtils.hasText(secretKey) || !StringUtils.hasText(userCode)) {
            log.warn("验证TOTP失败：密钥或验证码为空");
            return false;
        }

        try {
            long currentTimeCounter = System.currentTimeMillis() / 1000 / TIME_STEP;

            // 在时间窗口内验证
            for (int i = -window; i <= window; i++) {
                long timeCounter = currentTimeCounter + i;
                String expectedCode = generateHOTP(secretKey, timeCounter);

                if (userCode.equals(expectedCode)) {
                    log.debug("TOTP验证成功，时间偏移: {} 步", i);
                    return true;
                }
            }

            log.debug("TOTP验证失败，用户输入: {}", userCode);
            return false;
        } catch (Exception e) {
            log.error("验证TOTP失败，secretKey: {}, userCode: {}", secretKey, userCode, e);
            return false;
        }
    }

    /**
     * 验证HOTP验证码（基于计数器）
     *
     * <p>验证用户输入的验证码是否正确</p>
     *
     * @param secretKey Base32编码的密钥
     * @param userCode 用户输入的验证码
     * @param counter 当前计数器值
     * @return 验证成功返回true，失败返回false
     */
    public static boolean verifyHOTP(String secretKey, String userCode, long counter) {
        if (!StringUtils.hasText(secretKey) || !StringUtils.hasText(userCode)) {
            log.warn("验证HOTP失败：密钥或验证码为空");
            return false;
        }

        try {
            String expectedCode = generateHOTP(secretKey, counter);
            boolean isValid = userCode.equals(expectedCode);

            if (isValid) {
                log.debug("HOTP验证成功，counter: {}", counter);
            } else {
                log.debug("HOTP验证失败，用户输入: {}, 期望: {}, counter: {}", userCode, expectedCode, counter);
            }

            return isValid;
        } catch (Exception e) {
            log.error("验证HOTP失败，secretKey: {}, userCode: {}, counter: {}", secretKey, userCode, counter, e);
            return false;
        }
    }



    /**
     * 获取OTP Auth URL
     *
     * <p>生成标准的OTP Auth URL，可用于手动添加到验证器应用</p>
     *
     * @param secretKey Base32编码的密钥
     * @param accountName 账户名称
     * @param issuer 发行者名称
     * @return OTP Auth URL
     */
    public static String getOtpAuthUrl(String secretKey, String accountName, String issuer) {
        if (!StringUtils.hasText(secretKey) || !StringUtils.hasText(accountName)) {
            throw new IllegalArgumentException("密钥和账户名称不能为空");
        }

        try {
            String encodedAccountName = URLEncoder.encode(accountName, StandardCharsets.UTF_8);
            String encodedIssuer = StringUtils.hasText(issuer) ?
                URLEncoder.encode(issuer, StandardCharsets.UTF_8) : "AKey";

            return String.format(
                "otpauth://totp/%s:%s?secret=%s&issuer=%s&algorithm=SHA1&digits=%d&period=%d",
                encodedIssuer, encodedAccountName, secretKey, encodedIssuer, CODE_DIGITS, TIME_STEP
            );
        } catch (Exception e) {
            log.error("生成OTP Auth URL失败，accountName: {}, issuer: {}", accountName, issuer, e);
            throw new RuntimeException("生成OTP Auth URL失败", e);
        }
    }
    /**
     * HMAC-SHA1加密
     *
     * @param key 密钥
     * @param data 数据
     * @return 加密结果
     * @throws NoSuchAlgorithmException 算法不存在异常
     * @throws InvalidKeyException 无效密钥异常
     */
    private static byte[] hmacSha1(byte[] key, byte[] data)
            throws NoSuchAlgorithmException, InvalidKeyException {
        Mac mac = Mac.getInstance(HMAC_ALGORITHM);
        SecretKeySpec keySpec = new SecretKeySpec(key, HMAC_ALGORITHM);
        mac.init(keySpec);
        return mac.doFinal(data);
    }

    /**
     * 将长整型转换为字节数组
     *
     * @param value 长整型值
     * @return 字节数组
     */
    private static byte[] longToBytes(long value) {
        byte[] result = new byte[8];
        for (int i = 7; i >= 0; i--) {
            result[i] = (byte) (value & 0xFF);
            value >>= 8;
        }
        return result;
    }

    /**
     * Base32编码
     *
     * @param data 待编码的字节数组
     * @return Base32编码字符串
     */
    private static String encodeBase32(byte[] data) {
        if (data == null || data.length == 0) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        int buffer = 0;
        int bufferLength = 0;

        for (byte b : data) {
            buffer = (buffer << 8) | (b & 0xFF);
            bufferLength += 8;

            while (bufferLength >= 5) {
                int index = (buffer >> (bufferLength - 5)) & 0x1F;
                result.append(BASE32_CHARS.charAt(index));
                bufferLength -= 5;
            }
        }

        if (bufferLength > 0) {
            int index = (buffer << (5 - bufferLength)) & 0x1F;
            result.append(BASE32_CHARS.charAt(index));
        }

        // 添加填充字符
        while (result.length() % 8 != 0) {
            result.append('=');
        }

        return result.toString();
    }

    /**
     * Base32解码
     *
     * @param encoded Base32编码字符串
     * @return 解码后的字节数组
     */
    private static byte[] decodeBase32(String encoded) {
        if (!StringUtils.hasText(encoded)) {
            return new byte[0];
        }

        // 移除填充字符和空格
        encoded = encoded.replaceAll("=", "").replaceAll("\\s", "").toUpperCase();

        if (encoded.isEmpty()) {
            return new byte[0];
        }

        int outputLength = encoded.length() * 5 / 8;
        byte[] result = new byte[outputLength];
        int buffer = 0;
        int bufferLength = 0;
        int resultIndex = 0;

        for (char c : encoded.toCharArray()) {
            int value = BASE32_CHARS.indexOf(c);
            if (value < 0) {
                throw new IllegalArgumentException("无效的Base32字符: " + c);
            }

            buffer = (buffer << 5) | value;
            bufferLength += 5;

            if (bufferLength >= 8) {
                result[resultIndex++] = (byte) (buffer >> (bufferLength - 8));
                bufferLength -= 8;
            }
        }

        return result;
    }

    /**
     * 验证密钥格式是否正确
     *
     * @param secretKey Base32编码的密钥
     * @return 格式正确返回true，否则返回false
     */
    public static boolean isValidSecretKey(String secretKey) {
        if (!StringUtils.hasText(secretKey)) {
            return false;
        }

        try {
            // 尝试解码，如果成功说明格式正确
            decodeBase32(secretKey);
            return true;
        } catch (Exception e) {
            log.debug("无效的密钥格式: {}", secretKey);
            return false;
        }
    }

    /**
     * 获取当前时间步长
     *
     * @return 当前时间步长
     */
    public static long getCurrentTimeStep() {
        return System.currentTimeMillis() / 1000 / TIME_STEP;
    }

    /**
     * 获取剩余时间（秒）
     *
     * <p>获取当前验证码还有多少秒过期</p>
     *
     * @return 剩余时间（秒）
     */
    public static int getRemainingTime() {
        long currentTime = System.currentTimeMillis() / 1000;
        return TIME_STEP - (int) (currentTime % TIME_STEP);
    }



}