package com.akey.framework.web.exception;

import com.akey.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 谷歌验证码异常处理器
 * 
 * <p>统一处理谷歌验证码相关的异常</p>
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@RestControllerAdvice
@Order(1) // 设置优先级，确保在全局异常处理器之前执行
public class GoogleAuthExceptionHandler {

    /**
     * 处理谷歌验证码异常
     *
     * <p>根据不同的错误类型返回不同的错误码：</p>
     * <ul>
     *   <li>未提供谷歌验证码：返回-9999，让前端显示谷歌验证码输入框</li>
     *   <li>谷歌验证码无效或验证失败：返回-9998</li>
     *   <li>其他错误：返回500状态码</li>
     * </ul>
     *
     * @param e 谷歌验证码异常
     * @return 错误响应
     */
    @ExceptionHandler(GoogleAuthException.class)
    public Result<?> handleGoogleAuthException(GoogleAuthException e) {
        log.warn("谷歌验证码异常: errorCode={}, message={}", e.getErrorCode(), e.getMessage());

        if (GoogleAuthException.CODE_NOT_PROVIDED.equals(e.getErrorCode())) {
            // 返回特殊错误码-9999，让前端显示谷歌验证码输入框
            log.info("用户需要提供谷歌验证码，返回错误码-9999");
            return Result.error(-9999, e.getMessage());
        } else if (GoogleAuthException.CODE_INVALID.equals(e.getErrorCode()) ||
                   GoogleAuthException.VERIFICATION_FAILED.equals(e.getErrorCode())) {
            // 谷歌验证码无效或验证失败，返回-9998错误码
            log.warn("谷歌验证码无效或验证失败，返回错误码-9998: {}", e.getErrorCode());
            return Result.error(-9998, e.getMessage());
        } else {
            // 其他谷歌验证相关错误都返回500状态码
            log.warn("其他谷歌验证相关错误，返回500状态码: {}", e.getErrorCode());
            return Result.error(500, e.getMessage());
        }
    }
}
