package com.akey.framework.web.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import javax.crypto.Cipher;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * RSA加密解密工具类
 *
 * <p>功能说明：</p>
 * <ul>
 *   <li>提供RSA加密和解密功能</li>
 *   <li>支持公钥加密、私钥解密</li>
 *   <li>支持Base64编码处理</li>
 *   <li>支持分段加密解密，突破RSA数据长度限制</li>
 *   <li>提供密钥格式化和验证功能</li>
 * </ul>
 *
 * <p>分段加密说明：</p>
 * <ul>
 *   <li>对于2048位RSA密钥，单次最大加密数据为245字节</li>
 *   <li>统一使用分段加密，无论数据长度大小</li>
 *   <li>将数据按245字节分段加密，合并所有加密字节后统一Base64编码</li>
 *   <li>解密时按256字节（解密块大小）分段解密</li>
 *   <li>完全兼容传统单段加密的数据格式</li>
 * </ul>
 *
 * <p>使用示例：</p>
 * <pre>
 * // 加密数据（自动支持分段）
 * String encryptedData = RsaUtil.encrypt(data, publicKey);
 *
 * // 解密数据（自动识别分段）
 * String decryptedData = RsaUtil.decrypt(encryptedData, privateKey);
 * </pre>
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Slf4j
public class RsaUtil {

    /**
     * RSA算法名称
     */
    private static final String RSA_ALGORITHM = "RSA";

    /**
     * RSA加密填充方式
     */
    private static final String RSA_TRANSFORMATION = "RSA/ECB/PKCS1Padding";

    /**
     * RSA密钥长度（位）
     */
    private static final int RSA_KEY_SIZE = 2048;

    /**
     * RSA最大加密块大小（字节）
     * 对于2048位密钥，使用PKCS1Padding时最大加密块为245字节
     */
    private static final int MAX_ENCRYPT_BLOCK = RSA_KEY_SIZE / 8 - 11;

    /**
     * RSA最大解密块大小（字节）
     * 对于2048位密钥，解密块大小为256字节
     */
    private static final int MAX_DECRYPT_BLOCK = RSA_KEY_SIZE / 8;

    /**
     * 使用公钥加密数据（支持分段加密）
     *
     * @param data 待加密的数据
     * @param publicKeyStr 公钥字符串
     * @return 加密后的Base64编码字符串
     * @throws Exception 加密失败时抛出异常
     */
    public static String encrypt(String data, String publicKeyStr) throws Exception {
        if (!StringUtils.hasText(data) || !StringUtils.hasText(publicKeyStr)) {
            throw new IllegalArgumentException("数据和公钥不能为空");
        }

        try {
            // 解析公钥
            PublicKey publicKey = parsePublicKey(publicKeyStr);

            // 创建加密器
            Cipher cipher = Cipher.getInstance(RSA_TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);

            // 获取数据字节
            byte[] dataBytes = data.getBytes(StandardCharsets.UTF_8);

            // 统一使用分段加密
            return encryptBySegments(dataBytes, cipher);

        } catch (Exception e) {
            log.error("RSA加密失败", e);
            throw new Exception("RSA加密失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用私钥解密数据（支持分段解密）
     *
     * @param encryptedData 加密的Base64编码数据
     * @param privateKeyStr 私钥字符串
     * @return 解密后的原始数据
     * @throws Exception 解密失败时抛出异常
     */
    public static String decrypt(String encryptedData, String privateKeyStr) throws Exception {
        if (!StringUtils.hasText(encryptedData) || !StringUtils.hasText(privateKeyStr)) {
            throw new IllegalArgumentException("加密数据和私钥不能为空");
        }

        try {
            // 解析私钥
            PrivateKey privateKey = parsePrivateKey(privateKeyStr);

            // 创建解密器
            Cipher cipher = Cipher.getInstance(RSA_TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, privateKey);

            // Base64解码后按固定块大小分段解密
            byte[] encryptedBytes = Base64.getDecoder().decode(encryptedData);
            return decryptByFixedBlocks(encryptedBytes, cipher);

        } catch (Exception e) {
            log.error("RSA解密失败", e);
            throw new Exception("RSA解密失败: " + e.getMessage(), e);
        }
    }

    /**
     * 分段加密数据
     *
     * @param dataBytes 待加密的数据字节数组
     * @param cipher 已初始化的加密器
     * @return 加密后的Base64编码字符串
     * @throws Exception 加密失败时抛出异常
     */
    private static String encryptBySegments(byte[] dataBytes, Cipher cipher) throws Exception {
        int dataLength = dataBytes.length;
        byte[] allEncryptedBytes = new byte[0];

        // 分段加密
        for (int offset = 0; offset < dataLength; offset += MAX_ENCRYPT_BLOCK) {
            int blockSize = Math.min(MAX_ENCRYPT_BLOCK, dataLength - offset);
            byte[] block = new byte[blockSize];
            System.arraycopy(dataBytes, offset, block, 0, blockSize);

            // 加密当前块
            byte[] encryptedBlock = cipher.doFinal(block);

            // 将加密结果合并到总的字节数组中
            byte[] newAllEncryptedBytes = new byte[allEncryptedBytes.length + encryptedBlock.length];
            System.arraycopy(allEncryptedBytes, 0, newAllEncryptedBytes, 0, allEncryptedBytes.length);
            System.arraycopy(encryptedBlock, 0, newAllEncryptedBytes, allEncryptedBytes.length, encryptedBlock.length);
            allEncryptedBytes = newAllEncryptedBytes;
        }

        // 最后统一进行Base64编码
        return Base64.getEncoder().encodeToString(allEncryptedBytes);
    }



    /**
     * 按固定块大小分段解密数据
     *
     * @param encryptedBytes 加密的数据字节数组
     * @param cipher 已初始化的解密器
     * @return 解密后的原始字符串
     * @throws Exception 解密失败时抛出异常
     */
    private static String decryptByFixedBlocks(byte[] encryptedBytes, Cipher cipher) throws Exception {
        int dataLength = encryptedBytes.length;
        StringBuilder result = new StringBuilder();

        // 分段解密
        for (int offset = 0; offset < dataLength; offset += MAX_DECRYPT_BLOCK) {
            int blockSize = Math.min(MAX_DECRYPT_BLOCK, dataLength - offset);
            byte[] block = new byte[blockSize];
            System.arraycopy(encryptedBytes, offset, block, 0, blockSize);

            // 解密当前块
            byte[] decryptedBlock = cipher.doFinal(block);

            // 将解密结果添加到结果中
            result.append(new String(decryptedBlock, StandardCharsets.UTF_8));
        }

        return result.toString();
    }

    /**
     * 解析公钥字符串为PublicKey对象
     * 支持PEM格式和传统Base64格式
     *
     * @param publicKeyStr 公钥字符串
     * @return PublicKey对象
     * @throws Exception 解析失败时抛出异常
     */
    public static PublicKey parsePublicKey(String publicKeyStr) throws Exception {
        try {
            // 清理公钥字符串
            String cleanKey = cleanKeyString(publicKeyStr);

            // 如果包含PEM格式标识，则移除
            if (cleanKey.contains("-----BEGIN PUBLIC KEY-----")) {
                cleanKey = cleanKey
                        .replace("-----BEGIN PUBLIC KEY-----", "")
                        .replace("-----END PUBLIC KEY-----", "")
                        .replaceAll("\\s", "");
            } else {
                // 传统Base64格式，只移除空白字符
                cleanKey = cleanKey.replaceAll("\\s", "");
            }

            // Base64解码
            byte[] keyBytes = Base64.getDecoder().decode(cleanKey);

            // 创建公钥
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);

            return keyFactory.generatePublic(keySpec);

        } catch (Exception e) {
            log.error("解析公钥失败", e);
            throw new Exception("解析公钥失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析私钥字符串为PrivateKey对象
     * 支持PEM格式和传统Base64格式
     *
     * @param privateKeyStr 私钥字符串
     * @return PrivateKey对象
     * @throws Exception 解析失败时抛出异常
     */
    public static PrivateKey parsePrivateKey(String privateKeyStr) throws Exception {
        try {
            // 清理私钥字符串
            String cleanKey = cleanKeyString(privateKeyStr);

            // 如果包含PEM格式标识，则移除
            if (cleanKey.contains("-----BEGIN PRIVATE KEY-----")) {
                cleanKey = cleanKey
                        .replace("-----BEGIN PRIVATE KEY-----", "")
                        .replace("-----END PRIVATE KEY-----", "")
                        .replaceAll("\\s", "");
            } else {
                // 传统Base64格式，只移除空白字符
                cleanKey = cleanKey.replaceAll("\\s", "");
            }

            // Base64解码
            byte[] keyBytes = Base64.getDecoder().decode(cleanKey);

            // 创建私钥
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);

            return keyFactory.generatePrivate(keySpec);

        } catch (Exception e) {
            log.error("解析私钥失败", e);
            throw new Exception("解析私钥失败: " + e.getMessage(), e);
        }
    }

    /**
     * 清理密钥字符串
     * 移除多余的空白字符和换行符
     * 
     * @param keyStr 原始密钥字符串
     * @return 清理后的密钥字符串
     */
    private static String cleanKeyString(String keyStr) {
        if (!StringUtils.hasText(keyStr)) {
            return "";
        }
        
        return keyStr.trim()
                .replaceAll("\\r\\n", "\n")
                .replaceAll("\\r", "\n");
    }

    /**
     * 验证公钥格式是否正确
     * 
     * @param publicKeyStr 公钥字符串
     * @return 格式正确返回true，否则返回false
     */
    public static boolean isValidPublicKey(String publicKeyStr) {
        try {
            parsePublicKey(publicKeyStr);
            return true;
        } catch (Exception e) {
            log.debug("公钥格式验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 验证私钥格式是否正确
     * 
     * @param privateKeyStr 私钥字符串
     * @return 格式正确返回true，否则返回false
     */
    public static boolean isValidPrivateKey(String privateKeyStr) {
        try {
            parsePrivateKey(privateKeyStr);
            return true;
        } catch (Exception e) {
            log.debug("私钥格式验证失败: {}", e.getMessage());
            return false;
        }
    }



}
