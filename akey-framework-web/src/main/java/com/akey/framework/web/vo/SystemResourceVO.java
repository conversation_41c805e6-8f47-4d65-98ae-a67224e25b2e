package com.akey.framework.web.vo;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 系统资源信息VO
 * 
 * <p>封装完整的系统资源监控信息</p>
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
public class SystemResourceVO {

    /**
     * 数据采集时间
     */
    private LocalDateTime collectTime;

    /**
     * 数据采集耗时（毫秒）
     */
    private Long collectDuration;

    /**
     * CPU信息
     */
    private CpuInfoVO cpuInfo;

    /**
     * 内存信息
     */
    private MemoryInfoVO memoryInfo;

    /**
     * 服务器基本信息
     */
    private ServerInfoVO serverInfo;

    /**
     * JVM信息
     */
    private JvmInfoVO jvmInfo;

    /**
     * 磁盘信息
     */
    private DiskInfoVO diskInfo;

    /**
     * 错误信息（如果有）
     */
    private String errorMessage;

    /**
     * 警告信息（如果有）
     */
    private String warningMessage;

    /**
     * 是否使用OSHI库
     */
    private Boolean usingOshi;

    /**
     * 系统信息库版本
     */
    private String libraryVersion;

    /**
     * 创建成功的系统资源信息
     *
     * @param usingOshi 是否使用OSHI库
     * @return 系统资源信息VO
     */
    public static SystemResourceVO success(boolean usingOshi) {
        SystemResourceVO vo = new SystemResourceVO();
        vo.setCollectTime(LocalDateTime.now());
        vo.setUsingOshi(usingOshi);
        vo.setLibraryVersion(usingOshi ? "OSHI 6.6.5" : "Java Standard Library");
        return vo;
    }

    /**
     * 创建带错误信息的系统资源信息
     *
     * @param errorMessage 错误信息
     * @return 系统资源信息VO
     */
    public static SystemResourceVO error(String errorMessage) {
        SystemResourceVO vo = new SystemResourceVO();
        vo.setCollectTime(LocalDateTime.now());
        vo.setErrorMessage(errorMessage);
        return vo;
    }

    /**
     * 创建带警告信息的系统资源信息
     *
     * @param usingOshi 是否使用OSHI库
     * @param warningMessage 警告信息
     * @return 系统资源信息VO
     */
    public static SystemResourceVO warning(boolean usingOshi, String warningMessage) {
        SystemResourceVO vo = new SystemResourceVO();
        vo.setCollectTime(LocalDateTime.now());
        vo.setUsingOshi(usingOshi);
        vo.setLibraryVersion(usingOshi ? "OSHI 6.6.5" : "Java Standard Library");
        vo.setWarningMessage(warningMessage);
        return vo;
    }
}
