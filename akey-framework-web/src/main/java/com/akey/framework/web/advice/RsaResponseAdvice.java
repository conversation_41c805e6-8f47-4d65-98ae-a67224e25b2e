package com.akey.framework.web.advice;

import com.akey.common.constant.SystemConstant;
import com.akey.framework.web.config.RsaProperties;
import com.akey.framework.web.util.RsaUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * RSA响应加密切面
 * 
 * <p>功能说明：</p>
 * <ul>
 *   <li>拦截Controller的响应数据，对响应进行RSA加密</li>
 *   <li>只有在RSA功能启用且请求包含加密标识时才会生效</li>
 *   <li>支持JSON格式的响应数据加密</li>
 *   <li>加密失败时记录日志并返回原始响应</li>
 * </ul>
 *
 * <p>工作流程：</p>
 * <ol>
 *   <li>检查响应加密功能是否启用</li>
 *   <li>将响应对象序列化为JSON字符串</li>
 *   <li>使用RSA公钥加密JSON数据</li>
 *   <li>直接返回纯Base64加密字符串（不封装为JSON格式）</li>
 * </ol>
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Slf4j
@RestControllerAdvice
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "akey.security.rsa", name = "enabled", havingValue = "true")
public class RsaResponseAdvice implements ResponseBodyAdvice<Object> {

    private final RsaProperties rsaProperties;
    private final ObjectMapper objectMapper;

    

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        // 检查RSA功能是否启用
        return rsaProperties.getEnabled();
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
                                  Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  ServerHttpRequest request, ServerHttpResponse response) {

        // 检查是否需要加密响应
        if (!needEncryption()) {
            return body;
        }

        try {
            // 序列化响应对象为JSON
            String jsonData = objectMapper.writeValueAsString(body);
            
            // 获取公钥
            String publicKey = rsaProperties.getFormattedPublicKey();
            if (!StringUtils.hasText(publicKey)) {
                log.warn("RSA公钥未配置，返回原始响应");
                return body;
            }

            // 加密响应数据
            String encryptedData = RsaUtil.encrypt(jsonData, publicKey);

            // 设置加密响应标识头
            response.getHeaders().add(SystemConstant.ENCRYPTED_RESPONSE_HEADER, "true");

            // 直接返回纯Base64加密字符串
            return encryptedData;

        } catch (Exception e) {
            log.error("响应数据加密失败，返回原始响应", e);
            // 设置加密响应标识头
            response.getHeaders().add(SystemConstant.ENCRYPTED_RESPONSE_HEADER, "false");
            return body;
        }
    }

    /**
     * 检查是否需要加密响应
     *
     * @return 需要加密返回true，否则返回false
     */
    private boolean needEncryption() {
        // 只检查响应加密功能是否启用
        return rsaProperties.isResponseEncryptionEnabled();
    }
}
