package com.akey.framework.web.service;

import com.akey.framework.web.vo.GoogleAuthVerificationResult;

/**
 * 谷歌验证码验证服务接口
 * 
 * <p>提供谷歌验证码的验证功能</p>
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
public interface GoogleAuthVerificationService {

    /**
     * 验证谷歌验证码
     * 
     * @param userId 用户ID
     * @param code 验证码
     * @param required 是否强制验证
     * @param timeWindow 时间窗口
     * @return 验证结果
     */
    GoogleAuthVerificationResult verify(String userId, String code, boolean required, int timeWindow);

    /**
     * 验证当前登录用户的谷歌验证码
     * 
     * @param code 验证码
     * @param required 是否强制验证
     * @param timeWindow 时间窗口
     * @return 验证结果
     */
    GoogleAuthVerificationResult verifyCurrentUser(String code, boolean required, int timeWindow);
}
