package com.akey.framework.web.config;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * Jackson 配置类
 * 
 * <p>功能说明：</p>
 * <ul>
 *   <li>配置 Jackson ObjectMapper 支持 Java 8 时间类型</li>
 *   <li>统一时间格式的序列化和反序列化</li>
 *   <li>解决 LocalDateTime 等时间类型的序列化问题</li>
 * </ul>
 *
 * <p>时间格式：</p>
 * <ul>
 *   <li>LocalDateTime: yyyy-MM-dd HH:mm:ss</li>
 *   <li>LocalDate: yyyy-MM-dd</li>
 *   <li>LocalTime: HH:mm:ss</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Slf4j
@Configuration
public class JacksonConfig {

    /**
     * 日期时间格式
     */
    private static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    
    /**
     * 日期格式
     */
    private static final String DATE_FORMAT = "yyyy-MM-dd";
    
    /**
     * 时间格式
     */
    private static final String TIME_FORMAT = "HH:mm:ss";

    /**
     * 配置 ObjectMapper Bean
     * 
     * @return 配置好的 ObjectMapper 实例
     */
    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        log.info("配置 Jackson ObjectMapper");
        
        ObjectMapper mapper = new ObjectMapper();
        
        // 创建 JavaTimeModule
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        
        // 配置 LocalDateTime 序列化和反序列化
        javaTimeModule.addSerializer(LocalDateTime.class, 
                new LocalDateTimeSerializer(DateTimeFormatter.ofPattern(DATE_TIME_FORMAT)));
        javaTimeModule.addDeserializer(LocalDateTime.class, 
                new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern(DATE_TIME_FORMAT)));
        
        // 配置 LocalDate 序列化和反序列化
        javaTimeModule.addSerializer(LocalDate.class, 
                new LocalDateSerializer(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        javaTimeModule.addDeserializer(LocalDate.class, 
                new LocalDateDeserializer(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        
        // 配置 LocalTime 序列化和反序列化
        javaTimeModule.addSerializer(LocalTime.class, 
                new LocalTimeSerializer(DateTimeFormatter.ofPattern(TIME_FORMAT)));
        javaTimeModule.addDeserializer(LocalTime.class, 
                new LocalTimeDeserializer(DateTimeFormatter.ofPattern(TIME_FORMAT)));
        
        // 注册模块
        mapper.registerModule(javaTimeModule);

        // 禁用将日期写为时间戳的功能
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        // 应用 YAML 配置文件中的设置
        // 序列化配置
        mapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);

        // 反序列化配置 - 忽略未知属性
        mapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);

        log.info("Jackson ObjectMapper 配置完成");
        log.info("时间格式配置 - LocalDateTime: {}, LocalDate: {}, LocalTime: {}",
                DATE_TIME_FORMAT, DATE_FORMAT, TIME_FORMAT);
        log.info("反序列化配置 - 忽略未知属性: {}",
                !mapper.isEnabled(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES));

        return mapper;
    }
}
