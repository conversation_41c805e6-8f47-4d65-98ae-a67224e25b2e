package com.akey.framework.web.service;

import cn.dev33.satoken.stp.StpUtil;
import com.akey.common.service.GoogleAuthUserService;
import com.akey.framework.web.util.GoogleAuthUtil;
import com.akey.framework.web.vo.GoogleAuthVerificationResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * 谷歌验证码验证服务默认实现
 * 
 * <p>通过依赖注入GoogleAuthUserService来获取用户信息</p>
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@RequiredArgsConstructor
public class DefaultGoogleAuthVerificationService implements GoogleAuthVerificationService {

    private final GoogleAuthUserService googleAuthUserService;

    @Override
    public GoogleAuthVerificationResult verify(String userId, String code, boolean required, int timeWindow) {
        log.debug("开始验证谷歌验证码, userId: {}, required: {}, timeWindow: {}", userId, required, timeWindow);

        try {
            // 1. 检查用户是否存在
            if (!googleAuthUserService.userExists(userId)) {
                log.warn("用户不存在, userId: {}", userId);
                return GoogleAuthVerificationResult.failed("USER_NOT_FOUND", "用户不存在");
            }

            // 2. 获取用户的谷歌验证器密钥
            String googleAuthKey = googleAuthUserService.getUserGoogleAuthKey(userId);

            // 3. 检查用户是否设置了谷歌验证器
            boolean hasGoogleAuth = StringUtils.hasText(googleAuthKey);
            log.debug("用户谷歌验证器设置状态, userId: {}, hasGoogleAuth: {}", userId, hasGoogleAuth);

            // 4. 根据required参数决定验证逻辑
            if (!required && !hasGoogleAuth) {
                // 非强制验证且用户未设置谷歌验证器，跳过验证
                log.debug("跳过谷歌验证码验证, userId: {}", userId);
                return GoogleAuthVerificationResult.skipped();
            }

            // 5. 需要验证的情况
            if (required && !hasGoogleAuth) {
                // 强制验证但用户未设置谷歌验证器
                log.warn("强制验证但用户未设置谷歌验证器, userId: {}", userId);
                return GoogleAuthVerificationResult.failed("GOOGLE_AUTH_NOT_SETUP", "请先设置谷歌验证器");
            }

            // 6. 检查验证码是否提供
            if (!StringUtils.hasText(code)) {
                log.warn("验证码未提供, userId: {}", userId);
                return GoogleAuthVerificationResult.failed("GOOGLE_AUTH_CODE_NOT_PROVIDED", "请提供谷歌验证码");
            }

            // 7. 验证谷歌验证码
            boolean isValid = GoogleAuthUtil.verifyTOTP(googleAuthKey, code, timeWindow);
            
            if (isValid) {
                log.info("谷歌验证码验证成功, userId: {}", userId);
                return GoogleAuthVerificationResult.success();
            } else {
                log.warn("谷歌验证码验证失败, userId: {}, code: {}", userId, code);
                return GoogleAuthVerificationResult.failed("GOOGLE_AUTH_CODE_INVALID", "谷歌验证码无效");
            }

        } catch (Exception e) {
            log.error("谷歌验证码验证异常, userId: {}", userId, e);
            return GoogleAuthVerificationResult.failed("GOOGLE_AUTH_VERIFICATION_ERROR", "验证过程发生异常");
        }
    }

    @Override
    public GoogleAuthVerificationResult verifyCurrentUser(String code, boolean required, int timeWindow) {
        // 1. 检查用户是否已登录
        if (!StpUtil.isLogin()) {
            log.warn("用户未登录，无法进行谷歌验证码验证");
            return GoogleAuthVerificationResult.failed("USER_NOT_AUTHENTICATED", "用户未登录");
        }

        // 2. 获取当前用户ID
        String currentUserId = StpUtil.getLoginIdAsString();
        log.debug("当前登录用户ID: {}", currentUserId);

        // 3. 调用通用验证方法
        return verify(currentUserId, code, required, timeWindow);
    }
}
