package com.akey.framework.web.vo;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 服务器基本信息VO
 * 
 * <p>封装服务器基本信息</p>
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
public class ServerInfoVO {

    /**
     * 服务器主机名
     */
    private String hostname;

    /**
     * 操作系统名称
     */
    private String osName;

    /**
     * 操作系统版本
     */
    private String osVersion;

    /**
     * 操作系统架构
     */
    private String osArch;

    /**
     * 系统架构（32位/64位）
     */
    private String systemArch;

    /**
     * 服务器IP地址列表
     */
    private List<String> ipAddresses;

    /**
     * 内网IP地址
     */
    private String internalIp;

    /**
     * 外网IP地址
     */
    private String externalIp;

    /**
     * 系统启动时间
     */
    private LocalDateTime systemBootTime;

    /**
     * 系统运行时长（秒）
     */
    private Long systemUptimeSeconds;

    /**
     * 系统运行时长（格式化字符串）
     */
    private String systemUptimeFormatted;

    /**
     * 当前系统时间
     */
    private LocalDateTime currentTime;

    /**
     * 时区信息
     */
    private String timeZone;

    /**
     * 系统用户名
     */
    private String userName;

    /**
     * 用户主目录
     */
    private String userHome;

    /**
     * 系统临时目录
     */
    private String tempDir;

    /**
     * 文件分隔符
     */
    private String fileSeparator;

    /**
     * 路径分隔符
     */
    private String pathSeparator;

    /**
     * 行分隔符
     */
    private String lineSeparator;

    /**
     * 操作系统制造商
     */
    private String osManufacturer;

    /**
     * 操作系统构建号
     */
    private String osBuildNumber;

    /**
     * 系统进程数
     */
    private Integer processCount;

    /**
     * 系统线程数
     */
    private Integer threadCount;
}
