package com.akey.framework.web.util;

import com.akey.common.entity.DeviceInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import jakarta.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.regex.Pattern;

/**
 * 设备信息工具类
 * 
 * <p>用于获取客户端设备的详细信息</p>
 * <p>包含IP地址获取、User-Agent解析、设备识别等功能</p>
 * <p>特别注意防止IP地址伪造和安全检查</p>
 * 
 * <AUTHOR>
 * @since 2025-07-05
 */
@Slf4j
@Component
public class DeviceInfoUtil {

    /**
     * 未知标识
     */
    private static final String UNKNOWN = "unknown";

    /**
     * 本地IP地址
     */
    private static final String LOCALHOST_IPV4 = "127.0.0.1";
    private static final String LOCALHOST_IPV6 = "0:0:0:0:0:0:0:1";

    /**
     * IP地址正则表达式
     */
    private static final Pattern IPV4_PATTERN = Pattern.compile(
            "^(([01]?\\d\\d?|2[0-4]\\d|25[0-5])\\.){3}([01]?\\d\\d?|2[0-4]\\d|25[0-5])$");

    /**
     * 内网IP地址正则表达式
     */
    private static final Pattern PRIVATE_IP_PATTERN = Pattern.compile(
            "^(10\\.|172\\.(1[6-9]|2\\d|3[01])\\.|192\\.168\\.).*");

    /**
     * 可信的IP请求头（由nginx设置，已经过安全验证）
     */
    private static final String TRUSTED_IP_HEADER = "X-Real-IP";

    /**
     * 获取基础设备信息（快速版本，用于登录等性能敏感场景）
     *
     * <p>只获取必要的设备信息，避免耗时的操作</p>
     * <p>包括：IP地址、User-Agent、语言、是否代理</p>
     *
     * @param request HTTP请求对象
     * @return 基础设备信息对象
     */
    public DeviceInfo getBasicDeviceInfo(HttpServletRequest request) {
        if (request == null) {
            log.warn("HttpServletRequest为空，无法获取设备信息");
            return DeviceInfo.builder().build();
        }

        try {
            long startTime = System.currentTimeMillis();

            String clientIp = getRealIpAddress(request);
            String userAgent = getUserAgent(request);

            DeviceInfo deviceInfo = DeviceInfo.builder()
                    .clientIp(clientIp)
                    .userAgent(userAgent)
                    .language(getLanguage(request))
                    .isProxy(isProxyRequest(request))
                    .build();

            long endTime = System.currentTimeMillis();
            log.debug("获取基础设备信息耗时：{}ms", endTime - startTime);

            return deviceInfo;

        } catch (Exception e) {
            log.error("获取基础设备信息时发生异常", e);
            return DeviceInfo.builder()
                    .clientIp(getRemoteAddr(request))
                    .userAgent(getUserAgent(request))
                    .build();
        }
    }

    /**
     * 获取完整的设备信息（包含详细解析和风险评估）
     *
     * <p>包含完整的User-Agent解析、设备指纹生成、风险评估等</p>
     * <p>适用于安全要求较高但对性能要求不严格的场景</p>
     *
     * @param request HTTP请求对象
     * @return 完整设备信息对象
     */
    public DeviceInfo getDeviceInfo(HttpServletRequest request) {
        if (request == null) {
            log.warn("HttpServletRequest为空，无法获取设备信息");
            return DeviceInfo.builder().build();
        }

        try {
            long startTime = System.currentTimeMillis();

            String clientIp = getRealIpAddress(request);
            String userAgent = getUserAgent(request);

            DeviceInfo.DeviceInfoBuilder builder = DeviceInfo.builder()
                    .clientIp(clientIp)
                    .userAgent(userAgent)
                    .language(getLanguage(request))
                    .isProxy(isProxyRequest(request));

            // 解析User-Agent信息
            parseUserAgent(userAgent, builder);
            
            // 设置代理IP（如果存在）
            String proxyIp = getProxyIp(request);
            if (StringUtils.hasText(proxyIp) && !proxyIp.equals(clientIp)) {
                builder.proxyIp(proxyIp);
            }

            // 构建设备信息
            DeviceInfo deviceInfo = builder.build();
            
            // 生成设备指纹
            String fingerprint = generateDeviceFingerprint(deviceInfo);
            deviceInfo.setDeviceFingerprint(fingerprint);
            
            // 评估风险等级
            String riskLevel = assessRiskLevel(deviceInfo);
            deviceInfo.setRiskLevel(riskLevel);

            long endTime = System.currentTimeMillis();
            log.debug("获取完整设备信息耗时：{}ms", endTime - startTime);

            return deviceInfo;
            
        } catch (Exception e) {
            log.error("获取设备信息时发生异常", e);
            return DeviceInfo.builder()
                    .clientIp(getRemoteAddr(request))
                    .userAgent(getUserAgent(request))
                    .build();
        }
    }

    /**
     * 获取客户端真实IP地址（完善版本）
     *
     * <p>获取策略（按优先级）：</p>
     * <ul>
     *   <li>1. X-Real-IP（nginx设置的可信IP）</li>
     *   <li>2. X-Forwarded-For（代理链中的第一个IP）</li>
     *   <li>3. Proxy-Client-IP（Apache代理）</li>
     *   <li>4. WL-Proxy-Client-IP（WebLogic代理）</li>
     *   <li>5. HTTP_CLIENT_IP（客户端IP）</li>
     *   <li>6. HTTP_X_FORWARDED_FOR（转发IP）</li>
     *   <li>7. request.getRemoteAddr()（直连IP）</li>
     * </ul>
     *
     * @param request HTTP请求对象
     * @return 客户端IP地址
     */
    public static String getRealIpAddress(HttpServletRequest request) {
        if (request == null) {
            log.warn("HttpServletRequest为空，返回unknown");
            return UNKNOWN;
        }

        String ip = null;

        // 1. 优先从nginx设置的可信头部获取IP
        ip = getIpFromHeader(request, TRUSTED_IP_HEADER);
        if (isValidIp(ip)) {
            log.debug("从{}头部获取到IP: {}", TRUSTED_IP_HEADER, ip);
            return ip;
        }

        // 2. X-Forwarded-For（可能包含多个IP，取第一个）
        ip = getIpFromHeader(request, "X-Forwarded-For");
        if (isValidIp(ip)) {
            // X-Forwarded-For可能包含多个IP，格式：client, proxy1, proxy2
            if (ip.contains(",")) {
                String[] ips = ip.split(",");
                for (String singleIp : ips) {
                    singleIp = singleIp.trim();
                    if (isValidIp(singleIp)) {
                        log.debug("从X-Forwarded-For头部获取到IP: {}", singleIp);
                        return singleIp;
                    }
                }
            } else {
                log.debug("从X-Forwarded-For头部获取到IP: {}", ip);
                return ip;
            }
        }

        // 3. Proxy-Client-IP
        ip = getIpFromHeader(request, "Proxy-Client-IP");
        if (isValidIp(ip)) {
            log.debug("从Proxy-Client-IP头部获取到IP: {}", ip);
            return ip;
        }

        // 4. WL-Proxy-Client-IP
        ip = getIpFromHeader(request, "WL-Proxy-Client-IP");
        if (isValidIp(ip)) {
            log.debug("从WL-Proxy-Client-IP头部获取到IP: {}", ip);
            return ip;
        }

        // 5. HTTP_CLIENT_IP
        ip = getIpFromHeader(request, "HTTP_CLIENT_IP");
        if (isValidIp(ip)) {
            log.debug("从HTTP_CLIENT_IP头部获取到IP: {}", ip);
            return ip;
        }

        // 6. HTTP_X_FORWARDED_FOR
        ip = getIpFromHeader(request, "HTTP_X_FORWARDED_FOR");
        if (isValidIp(ip)) {
            log.debug("从HTTP_X_FORWARDED_FOR头部获取到IP: {}", ip);
            return ip;
        }

        // 7. 使用直连IP（REMOTE_ADDR）
        ip = request.getRemoteAddr();
        if (StringUtils.hasText(ip)) {
            // 处理IPv6本地地址
            if (LOCALHOST_IPV6.equals(ip)) {
                ip = LOCALHOST_IPV4;
            }

            if (isValidIp(ip)) {
                log.debug("从RemoteAddr获取到IP: {}", ip);
                return ip;
            }
        }

        // 8. 最终验证失败，返回默认值
        log.warn("无法获取有效的IP地址，返回本地IP");
        return LOCALHOST_IPV4;
    }

    /**
     * 从请求头获取IP地址
     *
     * @param request HTTP请求对象
     * @param headerName 头部名称
     * @return IP地址，如果无效返回null
     */
    private static String getIpFromHeader(HttpServletRequest request, String headerName) {
        if (request == null || !StringUtils.hasText(headerName)) {
            return null;
        }

        String ip = request.getHeader(headerName);
        if (StringUtils.hasText(ip) && !UNKNOWN.equalsIgnoreCase(ip)) {
            return ip.trim();
        }
        return null;
    }

    /**
     * 验证IP地址是否有效（增强版本）
     *
     * <p>验证规则：</p>
     * <ul>
     *   <li>1. 非空且不为"unknown"</li>
     *   <li>2. 符合IPv4格式</li>
     *   <li>3. 排除无效的IP段</li>
     *   <li>4. 排除保留IP段</li>
     *   <li>5. 使用InetAddress进一步验证</li>
     * </ul>
     *
     * @param ip IP地址
     * @return 是否有效
     */
    private static boolean isValidIp(String ip) {
        if (!StringUtils.hasText(ip) || UNKNOWN.equalsIgnoreCase(ip)) {
            return false;
        }

        // 去除首尾空格
        ip = ip.trim();

        // 验证IPv4格式
        if (!IPV4_PATTERN.matcher(ip).matches()) {
            return false;
        }

        // 排除无效的IP段
        if (ip.startsWith("0.") || ip.startsWith("255.")) {
            return false;
        }

        // 排除保留IP段
        if (isReservedIp(ip)) {
            return false;
        }

        // 使用InetAddress进一步验证IP地址的有效性
        try {
            java.net.InetAddress.getByName(ip);
            return true;
        } catch (java.net.UnknownHostException e) {
            log.debug("IP地址无效: {}, 错误: {}", ip, e.getMessage());
            return false;
        }
    }

    /**
     * 检查是否为保留IP段
     *
     * @param ip IP地址
     * @return 是否为保留IP
     */
    private static boolean isReservedIp(String ip) {
        if (!StringUtils.hasText(ip)) {
            return true;
        }

        // 检查保留IP段
        String[] reservedPrefixes = {
            "224.", "225.", "226.", "227.", "228.", "229.", "230.", "231.",
            "232.", "233.", "234.", "235.", "236.", "237.", "238.", "239.",  // 组播地址
            "240.", "241.", "242.", "243.", "244.", "245.", "246.", "247.",
            "248.", "249.", "250.", "251.", "252.", "253.", "254."          // 保留地址
        };

        for (String prefix : reservedPrefixes) {
            if (ip.startsWith(prefix)) {
                return true;
            }
        }

        // 检查特殊IP地址
        return "0.0.0.0".equals(ip) || "***************".equals(ip);
    }

    /**
     * 判断是否为内网IP
     * 
     * @param ip IP地址
     * @return 是否为内网IP
     */
    public static boolean isPrivateIp(String ip) {
        if (!isValidIp(ip)) {
            return false;
        }
        return PRIVATE_IP_PATTERN.matcher(ip).matches() || 
               LOCALHOST_IPV4.equals(ip);
    }

    /**
     * 获取User-Agent
     * 
     * @param request HTTP请求对象
     * @return User-Agent字符串
     */
    public static String getUserAgent(HttpServletRequest request) {
        if (request == null) {
            return UNKNOWN;
        }
        
        String userAgent = request.getHeader("User-Agent");
        return StringUtils.hasText(userAgent) ? userAgent : UNKNOWN;
    }

    /**
     * 获取语言设置
     * 
     * @param request HTTP请求对象
     * @return 语言设置
     */
    public static String getLanguage(HttpServletRequest request) {
        if (request == null) {
            return UNKNOWN;
        }
        
        String language = request.getHeader("Accept-Language");
        if (StringUtils.hasText(language)) {
            // 取第一个语言设置
            if (language.contains(",")) {
                language = language.split(",")[0];
            }
            if (language.contains(";")) {
                language = language.split(";")[0];
            }
            return language.trim();
        }
        
        return UNKNOWN;
    }

    /**
     * 判断是否为代理请求
     * 
     * @param request HTTP请求对象
     * @return 是否为代理请求
     */
    public static boolean isProxyRequest(HttpServletRequest request) {
        if (request == null) {
            return false;
        }

        // 检查常见的代理头部
        String[] proxyHeaders = {
                "X-Forwarded-For", "X-Real-IP", "Proxy-Client-IP",
                "WL-Proxy-Client-IP", "HTTP_VIA", "HTTP_X_FORWARDED_FOR"
        };

        for (String header : proxyHeaders) {
            String value = request.getHeader(header);
            if (StringUtils.hasText(value) && !UNKNOWN.equalsIgnoreCase(value)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取代理IP
     * 
     * @param request HTTP请求对象
     * @return 代理IP地址
     */
    private static String getProxyIp(HttpServletRequest request) {
        String via = request.getHeader("Via");
        if (StringUtils.hasText(via)) {
            return via;
        }
        
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.hasText(xForwardedFor) && xForwardedFor.contains(",")) {
            // 返回最后一个IP（通常是最近的代理）
            String[] ips = xForwardedFor.split(",");
            return ips[ips.length - 1].trim();
        }
        
        return null;
    }

    /**
     * 获取远程地址
     *
     * @param request HTTP请求对象
     * @return 远程地址
     */
    private static String getRemoteAddr(HttpServletRequest request) {
        if (request == null) {
            return UNKNOWN;
        }
        return request.getRemoteAddr();
    }

    /**
     * 解析User-Agent信息
     *
     * @param userAgent User-Agent字符串
     * @param builder DeviceInfo构建器
     */
    private static void parseUserAgent(String userAgent, DeviceInfo.DeviceInfoBuilder builder) {
        if (!StringUtils.hasText(userAgent) || UNKNOWN.equals(userAgent)) {
            return;
        }

        String lowerUserAgent = userAgent.toLowerCase();

        // 解析操作系统
        parseOperatingSystem(lowerUserAgent, builder);

        // 解析浏览器
        parseBrowser(lowerUserAgent, builder);

        // 解析设备类型
        parseDeviceType(lowerUserAgent, builder);

        // 检查是否为机器人
        parseBot(lowerUserAgent, builder);
    }

    /**
     * 解析操作系统信息
     */
    private static void parseOperatingSystem(String userAgent, DeviceInfo.DeviceInfoBuilder builder) {
        if (userAgent.contains("windows nt 10.0")) {
            builder.operatingSystem("Windows").osVersion("10");
        } else if (userAgent.contains("windows nt 6.3")) {
            builder.operatingSystem("Windows").osVersion("8.1");
        } else if (userAgent.contains("windows nt 6.2")) {
            builder.operatingSystem("Windows").osVersion("8");
        } else if (userAgent.contains("windows nt 6.1")) {
            builder.operatingSystem("Windows").osVersion("7");
        } else if (userAgent.contains("windows")) {
            builder.operatingSystem("Windows");
        } else if (userAgent.contains("mac os x")) {
            builder.operatingSystem("macOS");
            // 提取版本号
            if (userAgent.contains("mac os x 10_")) {
                int start = userAgent.indexOf("mac os x 10_") + 13;
                int end = userAgent.indexOf(")", start);
                if (end > start) {
                    String version = userAgent.substring(start, end).replace("_", ".");
                    builder.osVersion("10." + version);
                }
            }
        } else if (userAgent.contains("android")) {
            builder.operatingSystem("Android");
            // 提取Android版本
            if (userAgent.contains("android ")) {
                int start = userAgent.indexOf("android ") + 8;
                int end = userAgent.indexOf(";", start);
                if (end > start) {
                    builder.osVersion(userAgent.substring(start, end).trim());
                }
            }
        } else if (userAgent.contains("iphone") || userAgent.contains("ipad")) {
            builder.operatingSystem("iOS");
            // 提取iOS版本
            if (userAgent.contains("os ")) {
                int start = userAgent.indexOf("os ") + 3;
                int end = userAgent.indexOf(" ", start);
                if (end > start) {
                    String version = userAgent.substring(start, end).replace("_", ".");
                    builder.osVersion(version);
                }
            }
        } else if (userAgent.contains("linux")) {
            builder.operatingSystem("Linux");
        }
    }

    /**
     * 解析浏览器信息
     */
    private static void parseBrowser(String userAgent, DeviceInfo.DeviceInfoBuilder builder) {
        if (userAgent.contains("edg/")) {
            builder.browserName("Microsoft Edge");
            extractVersion(userAgent, "edg/", builder);
        } else if (userAgent.contains("chrome/")) {
            builder.browserName("Google Chrome");
            extractVersion(userAgent, "chrome/", builder);
        } else if (userAgent.contains("firefox/")) {
            builder.browserName("Mozilla Firefox");
            extractVersion(userAgent, "firefox/", builder);
        } else if (userAgent.contains("safari/") && !userAgent.contains("chrome")) {
            builder.browserName("Safari");
            if (userAgent.contains("version/")) {
                extractVersion(userAgent, "version/", builder);
            }
        } else if (userAgent.contains("opera/")) {
            builder.browserName("Opera");
            extractVersion(userAgent, "opera/", builder);
        }
    }

    /**
     * 提取浏览器版本号
     */
    private static void extractVersion(String userAgent, String prefix, DeviceInfo.DeviceInfoBuilder builder) {
        int start = userAgent.indexOf(prefix) + prefix.length();
        int end = userAgent.indexOf(" ", start);
        if (end == -1) {
            end = userAgent.indexOf(";", start);
        }
        if (end == -1) {
            end = userAgent.indexOf(")", start);
        }
        if (end > start) {
            String version = userAgent.substring(start, end);
            // 只取主版本号
            if (version.contains(".")) {
                version = version.substring(0, version.indexOf("."));
            }
            builder.browserVersion(version);
        }
    }

    /**
     * 解析设备类型（精确识别）
     *
     * <p>通过User-Agent字符串精确识别设备类型，支持以下分类：</p>
     * <ul>
     *   <li>iPhone - 苹果手机</li>
     *   <li>iPad - 苹果平板</li>
     *   <li>Android - 安卓设备（手机和平板）</li>
     *   <li>Windows Phone - 微软手机</li>
     *   <li>BlackBerry - 黑莓设备</li>
     *   <li>Tablet - 其他平板设备</li>
     *   <li>Mobile - 其他移动设备</li>
     *   <li>Desktop - 桌面设备</li>
     *   <li>Bot - 爬虫机器人</li>
     * </ul>
     */
    private static void parseDeviceType(String userAgent, DeviceInfo.DeviceInfoBuilder builder) {
        // 1. 优先检查机器人
        if (isBot(userAgent)) {
            builder.deviceType("Bot").isMobile(false);
            return;
        }

        // 2. 检查iPhone（必须在iPad之前检查，因为iPad的UA也包含iPhone）
        if (userAgent.contains("iphone")) {
            builder.deviceType("iPhone").isMobile(true);
            return;
        }

        // 3. 检查iPad
        if (userAgent.contains("ipad")) {
            builder.deviceType("iPad").isMobile(true);
            return;
        }

        // 4. 检查Android设备
        if (userAgent.contains("android")) {
            // 进一步区分Android手机和平板
            if (userAgent.contains("mobile")) {
                builder.deviceType("Android").isMobile(true);
            } else {
                // Android平板通常不包含"mobile"关键字
                builder.deviceType("Android Tablet").isMobile(true);
            }
            return;
        }

        // 5. 检查Windows Phone
        if (userAgent.contains("windows phone") || userAgent.contains("iemobile")) {
            builder.deviceType("Windows Phone").isMobile(true);
            return;
        }

        // 6. 检查BlackBerry
        if (userAgent.contains("blackberry") || userAgent.contains("bb10")) {
            builder.deviceType("BlackBerry").isMobile(true);
            return;
        }

        // 7. 检查其他平板设备
        if (isTabletDevice(userAgent)) {
            builder.deviceType("Tablet").isMobile(true);
            return;
        }

        // 8. 检查其他移动设备
        if (isMobileDevice(userAgent)) {
            builder.deviceType("Mobile").isMobile(true);
            return;
        }

        // 9. 默认为桌面设备
        builder.deviceType("Desktop").isMobile(false);
    }

    /**
     * 检查是否为机器人
     */
    private static boolean isBot(String userAgent) {
        String[] botKeywords = {
                "bot", "crawler", "spider", "scraper", "curl", "wget",
                "googlebot", "bingbot", "baiduspider", "yandexbot",
                "facebookexternalhit", "twitterbot", "linkedinbot",
                "whatsapp", "telegram", "slackbot", "discordbot"
        };

        for (String keyword : botKeywords) {
            if (userAgent.contains(keyword)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查是否为平板设备
     */
    private static boolean isTabletDevice(String userAgent) {
        String[] tabletKeywords = {
                "tablet", "kindle", "silk", "playbook", "gt-p",
                "sm-t", "nexus 7", "nexus 9", "nexus 10",
                "xoom", "sch-i800", "playstation portable",
                "hpwos"
        };

        for (String keyword : tabletKeywords) {
            if (userAgent.contains(keyword)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查是否为移动设备
     */
    private static boolean isMobileDevice(String userAgent) {
        String[] mobileKeywords = {
                "mobile", "phone", "mobi", "opera mini", "opera mobi",
                "fennec", "minimo", "symbian", "psp", "nintendo",
                "portalmmm", "blazer", "avantgo", "danger", "palm",
                "series60", "palmsource", "pocketpc", "smartphone",
                "rover", "ipaq", "au-mic", "alcatel", "ericy",
                "vodafone", "wap", "up.browser", "up.link"
        };

        for (String keyword : mobileKeywords) {
            if (userAgent.contains(keyword)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查是否为机器人（兼容原有方法）
     */
    private static void parseBot(String userAgent, DeviceInfo.DeviceInfoBuilder builder) {
        if (isBot(userAgent)) {
            builder.isBot(true);
        }
    }

    /**
     * 生成设备指纹
     *
     * @param deviceInfo 设备信息
     * @return 设备指纹（MD5哈希值）
     */
    public static String generateDeviceFingerprint(DeviceInfo deviceInfo) {
        if (deviceInfo == null) {
            return UNKNOWN;
        }

        StringBuilder fingerprint = new StringBuilder();

        // 组合关键设备特征
        appendIfNotNull(fingerprint, deviceInfo.getUserAgent());
        appendIfNotNull(fingerprint, deviceInfo.getLanguage());
        appendIfNotNull(fingerprint, deviceInfo.getOperatingSystem());
        appendIfNotNull(fingerprint, deviceInfo.getBrowserName());
        appendIfNotNull(fingerprint, deviceInfo.getDeviceType());
        appendIfNotNull(fingerprint, deviceInfo.getScreenResolution());
        appendIfNotNull(fingerprint, deviceInfo.getTimezone());

        // 生成MD5哈希
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(fingerprint.toString().getBytes("UTF-8"));

            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString();

        } catch (Exception e) {
            log.error("生成设备指纹时发生异常", e);
            return String.valueOf(fingerprint.toString().hashCode());
        }
    }

    /**
     * 评估风险等级
     *
     * @param deviceInfo 设备信息
     * @return 风险等级：LOW、MEDIUM、HIGH
     */
    public static String assessRiskLevel(DeviceInfo deviceInfo) {
        if (deviceInfo == null) {
            return "HIGH";
        }

        int riskScore = 0;

        // 1. 检查是否为机器人
        if (Boolean.TRUE.equals(deviceInfo.getIsBot())) {
            riskScore += 30;
        }

        // 2. 检查是否使用代理
        if (Boolean.TRUE.equals(deviceInfo.getIsProxy())) {
            riskScore += 20;
        }

        // 3. 检查IP地址类型
        if (isPrivateIp(deviceInfo.getClientIp())) {
            riskScore += 10;
        }

        // 4. 检查User-Agent
        String userAgent = deviceInfo.getUserAgent();
        if (UNKNOWN.equals(userAgent) || !StringUtils.hasText(userAgent)) {
            riskScore += 25;
        } else if (userAgent.length() < 50) {
            riskScore += 15; // 过短的User-Agent可能是伪造的
        }

        // 5. 检查浏览器信息
        if (!StringUtils.hasText(deviceInfo.getBrowserName())) {
            riskScore += 15;
        }

        // 6. 检查操作系统信息
        if (!StringUtils.hasText(deviceInfo.getOperatingSystem())) {
            riskScore += 10;
        }

        // 7. 检查语言设置
        if (UNKNOWN.equals(deviceInfo.getLanguage())) {
            riskScore += 5;
        }

        // 根据分数确定风险等级
        if (riskScore >= 50) {
            return "HIGH";
        } else if (riskScore >= 25) {
            return "MEDIUM";
        } else {
            return "LOW";
        }
    }

    /**
     * 辅助方法：向StringBuilder添加非空值
     */
    private static void appendIfNotNull(StringBuilder sb, String value) {
        if (StringUtils.hasText(value) && !UNKNOWN.equals(value)) {
            sb.append(value).append("|");
        }
    }

    /**
     * 获取客户端IP地址
     *
     * @param request HTTP请求对象
     * @return IP地址
     */
    public static String getClientIp(HttpServletRequest request) {
        return getRealIpAddress(request);
    }

    /**
     * 检查IP地址是否可疑
     *
     * @param ip IP地址
     * @return 是否可疑
     */
    public static boolean isSuspiciousIp(String ip) {
        if (!isValidIp(ip)) {
            return true;
        }

        // 检查是否为内网IP（在公网环境中可能是可疑的）
        if (isPrivateIp(ip)) {
            return true;
        }

        // 可以添加更多的IP黑名单检查
        // 例如：已知的恶意IP段、代理服务器IP等

        return false;
    }

    /**
     * 验证设备信息的完整性
     *
     * @param deviceInfo 设备信息
     * @return 是否完整
     */
    public static boolean isDeviceInfoComplete(DeviceInfo deviceInfo) {
        if (deviceInfo == null) {
            return false;
        }

        return StringUtils.hasText(deviceInfo.getClientIp()) &&
               StringUtils.hasText(deviceInfo.getUserAgent()) &&
               StringUtils.hasText(deviceInfo.getOperatingSystem()) &&
               StringUtils.hasText(deviceInfo.getBrowserName());
    }

    /**
     * 获取客户端IP地址的详细信息（用于调试）
     *
     * @param request HTTP请求对象
     * @return IP获取详情字符串
     */
    public static String getIpDetails(HttpServletRequest request) {
        if (request == null) {
            return "HttpServletRequest为空";
        }

        StringBuilder details = new StringBuilder();
        details.append("=== IP获取详情 ===\n");

        // 显示所有可能的IP头部
        details.append("X-Real-IP: ").append(getHeaderValue(request, "X-Real-IP")).append("\n");
        details.append("X-Forwarded-For: ").append(getHeaderValue(request, "X-Forwarded-For")).append("\n");
        details.append("Proxy-Client-IP: ").append(getHeaderValue(request, "Proxy-Client-IP")).append("\n");
        details.append("WL-Proxy-Client-IP: ").append(getHeaderValue(request, "WL-Proxy-Client-IP")).append("\n");
        details.append("HTTP_CLIENT_IP: ").append(getHeaderValue(request, "HTTP_CLIENT_IP")).append("\n");
        details.append("HTTP_X_FORWARDED_FOR: ").append(getHeaderValue(request, "HTTP_X_FORWARDED_FOR")).append("\n");
        details.append("RemoteAddr: ").append(request.getRemoteAddr()).append("\n");

        // 显示最终获取的IP
        String finalIp = getRealIpAddress(request);
        details.append("最终IP: ").append(finalIp).append("\n");

        // 显示IP验证结果
        details.append("IP有效性: ").append(isValidIp(finalIp) ? "有效" : "无效").append("\n");
        details.append("是否内网IP: ").append(isPrivateIp(finalIp) ? "是" : "否").append("\n");
        details.append("是否本地IP: ").append(isLocalIp(finalIp) ? "是" : "否").append("\n");
        details.append("是否可疑IP: ").append(isSuspiciousIp(finalIp) ? "是" : "否").append("\n");

        return details.toString();
    }

    /**
     * 获取请求头的值（用于调试）
     *
     * @param request HTTP请求对象
     * @param headerName 头部名称
     * @return 头部值，如果为空返回"null"
     */
    private static String getHeaderValue(HttpServletRequest request, String headerName) {
        String value = request.getHeader(headerName);
        return StringUtils.hasText(value) ? value : "null";
    }

    /**
     * 判断是否为本地IP
     *
     * @param ip IP地址
     * @return 是否为本地IP
     */
    public static boolean isLocalIp(String ip) {
        return LOCALHOST_IPV4.equals(ip) || LOCALHOST_IPV6.equals(ip) || "localhost".equalsIgnoreCase(ip);
    }

    /**
     * 获取IP地址类型描述
     *
     * @param ip IP地址
     * @return IP类型描述
     */
    public static String getIpTypeDescription(String ip) {
        if (!isValidIp(ip)) {
            return "无效IP";
        }

        if (isLocalIp(ip)) {
            return "本地IP";
        }

        if (isPrivateIp(ip)) {
            return "内网IP";
        }

        if (isSuspiciousIp(ip)) {
            return "可疑IP";
        }

        return "公网IP";
    }
}
