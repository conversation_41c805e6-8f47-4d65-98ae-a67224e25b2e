package com.akey.framework.web.exception;

import com.akey.common.Result;
import com.akey.common.enums.ResultCode;

import cn.dev33.satoken.exception.DisableServiceException;
import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import cn.dev33.satoken.exception.NotRoleException;
import cn.dev33.satoken.exception.SaTokenException;
import com.akey.common.exception.BaseException;
import com.akey.common.exception.BusinessException;
import com.akey.common.exception.DataNotFoundException;
import com.akey.common.exception.ValidationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * 统一处理系统中的各种异常，并返回统一的响应格式
 * 
 * <AUTHOR>
 * @since 2025-07-02
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    /**
     * 处理自定义基础异常
     *
     * @param e       异常对象
     * @param request HTTP请求
     * @return 统一响应结果
     */
    @ExceptionHandler(BaseException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<Void> handleBaseException(BaseException e, HttpServletRequest request) {
        log.warn("业务异常 - 请求路径: {}, 错误码: {}, 错误信息: {}", 
                request.getRequestURI(), e.getCode(), e.getMessage());
        return Result.error(e.getCode(), e.getMessage());
    }
    
    /**
     * 处理业务异常
     *
     * @param e       异常对象
     * @param request HTTP请求
     * @return 统一响应结果
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<Void> handleBusinessException(BusinessException e, HttpServletRequest request) {
        log.warn("业务逻辑异常 - 请求路径: {}, 错误码: {}, 错误信息: {}", 
                request.getRequestURI(), e.getCode(), e.getMessage());
        return Result.error(e.getCode(), e.getMessage());
    }
    
    /**
     * 处理数据不存在异常
     *
     * @param e       异常对象
     * @param request HTTP请求
     * @return 统一响应结果
     */
    @ExceptionHandler(DataNotFoundException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<Void> handleDataNotFoundException(DataNotFoundException e, HttpServletRequest request) {
        log.warn("数据不存在异常 - 请求路径: {}, 错误信息: {}", request.getRequestURI(), e.getMessage());
        return Result.error(ResultCode.NOT_FOUND, e.getMessage());
    }
    
    /**
     * 处理参数验证异常
     *
     * @param e       异常对象
     * @param request HTTP请求
     * @return 统一响应结果
     */
    @ExceptionHandler(ValidationException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<Void> handleValidationException(ValidationException e, HttpServletRequest request) {
        log.warn("参数验证异常 - 请求路径: {}, 错误信息: {}", request.getRequestURI(), e.getMessage());
        return Result.error(e.getCode(), e.getMessage());
    }
    
    /**
     * 处理请求方法不支持异常
     *
     * @param e       异常对象
     * @param request HTTP请求
     * @return 统一响应结果
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    public Result<Void> handleMethodNotSupportedException(HttpRequestMethodNotSupportedException e, 
                                                         HttpServletRequest request) {
        log.warn("请求方法不支持异常 - 请求路径: {}, 请求方法: {}, 支持方法: {}", 
                request.getRequestURI(), request.getMethod(), e.getSupportedHttpMethods());
        return Result.error(ResultCode.METHOD_NOT_ALLOWED, 
                          String.format("请求方法[%s]不支持，支持的方法: %s", 
                                      request.getMethod(), e.getSupportedHttpMethods()));
    }
    
    /**
     * 处理媒体类型不支持异常
     *
     * @param e       异常对象
     * @param request HTTP请求
     * @return 统一响应结果
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    @ResponseStatus(HttpStatus.UNSUPPORTED_MEDIA_TYPE)
    public Result<Void> handleMediaTypeNotSupportedException(HttpMediaTypeNotSupportedException e, 
                                                            HttpServletRequest request) {
        log.warn("媒体类型不支持异常 - 请求路径: {}, Content-Type: {}", 
                request.getRequestURI(), request.getContentType());
        return Result.error(ResultCode.PARAM_FORMAT_ERROR, "不支持的媒体类型: " + request.getContentType());
    }
    
    /**
     * 处理缺少请求参数异常
     *
     * @param e       异常对象
     * @param request HTTP请求
     * @return 统一响应结果
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleMissingParameterException(MissingServletRequestParameterException e, 
                                                       HttpServletRequest request) {
        log.warn("缺少请求参数异常 - 请求路径: {}, 缺少参数: {}", request.getRequestURI(), e.getParameterName());
        return Result.error(ResultCode.PARAM_NULL, "缺少必需参数: " + e.getParameterName());
    }
    
    /**
     * 处理请求参数类型不匹配异常
     *
     * @param e       异常对象
     * @param request HTTP请求
     * @return 统一响应结果
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleTypeMismatchException(MethodArgumentTypeMismatchException e, 
                                                   HttpServletRequest request) {
        log.warn("参数类型不匹配异常 - 请求路径: {}, 参数名: {}, 参数值: {}, 期望类型: {}", 
                request.getRequestURI(), e.getName(), e.getValue(), 
                e.getRequiredType() != null ? e.getRequiredType().getSimpleName() : "unknown");
        return Result.error(ResultCode.PARAM_FORMAT_ERROR, 
                          String.format("参数[%s]类型不正确，期望类型: %s", 
                                      e.getName(), 
                                      e.getRequiredType() != null ? e.getRequiredType().getSimpleName() : "unknown"));
    }
    
    /**
     * 处理请求体读取异常
     *
     * @param e       异常对象
     * @param request HTTP请求
     * @return 统一响应结果
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleMessageNotReadableException(HttpMessageNotReadableException e, 
                                                         HttpServletRequest request) {
        log.warn("请求体读取异常 - 请求路径: {}, 错误信息: {}", request.getRequestURI(), e.getMessage());
        return Result.error(ResultCode.PARAM_FORMAT_ERROR, "请求体格式错误");
    }
    
    /**
     * 处理Bean Validation异常 (方法参数验证)
     *
     * @param e       异常对象
     * @param request HTTP请求
     * @return 统一响应结果
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleValidationException(MethodArgumentNotValidException e, 
                                                 HttpServletRequest request) {
        log.warn("参数验证失败 - 请求路径: {}", request.getRequestURI(),e);
        String errorMessage = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        return Result.error(ResultCode.PARAM_ERROR, errorMessage);
    }
    
    /**
     * 处理Bean Validation异常 (表单绑定验证)
     *
     * @param e       异常对象
     * @param request HTTP请求
     * @return 统一响应结果
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleBindException(BindException e, HttpServletRequest request) {
        log.warn("表单绑定验证失败 - 请求路径: {}", request.getRequestURI(),e);
        String errorMessage = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        return Result.error(ResultCode.PARAM_ERROR, errorMessage);
    }
    
    /**
     * 处理Bean Validation异常 (约束验证)
     *
     * @param e       异常对象
     * @param request HTTP请求
     * @return 统一响应结果
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleConstraintViolationException(ConstraintViolationException e, 
                                                          HttpServletRequest request) {
        log.warn("约束验证失败 - 请求路径: {}", request.getRequestURI(),e);
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        String errorMessage = violations.stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(", "));
        return Result.error(ResultCode.PARAM_ERROR, errorMessage);
    }
    
    /**
     * 处理404异常
     *
     * @param e       异常对象
     * @param request HTTP请求
     * @return 统一响应结果
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public Result<Void> handleNotFoundException(NoHandlerFoundException e, HttpServletRequest request) {
        log.warn("请求路径不存在 - 请求路径: {}, 请求方法: {}", request.getRequestURI(), request.getMethod(),e);
        return Result.error(ResultCode.NOT_FOUND, "请求路径不存在: " + request.getRequestURI());
    }

    /**
     * 处理静态资源不存在异常（如favicon.ico）
     *
     * @param e       异常对象
     * @param request HTTP请求
     * @return 统一响应结果
     */
    @ExceptionHandler(NoResourceFoundException.class)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public Result<Void> handleNoResourceFoundException(NoResourceFoundException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();

        // 对于favicon.ico等静态资源请求，只记录debug级别日志，避免日志污染
        if (requestURI.endsWith("/favicon.ico") ||
            requestURI.endsWith("/robots.txt") ||
            requestURI.endsWith("/sitemap.xml")) {
            log.debug("静态资源请求 - 请求路径: {}", requestURI);
        } else {
            log.warn("静态资源不存在 - 请求路径: {}", requestURI);
        }

        // 对于favicon.ico等请求，返回204 No Content，不返回错误信息
        if (requestURI.endsWith("/favicon.ico")) {
            return null; // 返回null，Spring会自动处理为204状态码
        }

        return Result.error(ResultCode.NOT_FOUND, "资源不存在: " + requestURI);
    }
    
    // ==================== Sa-Token 异常处理 ====================

    /**
     * 处理Sa-Token未登录异常
     *
     * @param e       异常对象
     * @param request HTTP请求
     * @return 统一响应结果
     */
    @ExceptionHandler(NotLoginException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public Result<Void> handleNotLoginException(NotLoginException e, HttpServletRequest request) {
        String message = "";
        switch (e.getType()) {
            case NotLoginException.NOT_TOKEN:
                message = "当前会话未提供Token";
                break;
            case NotLoginException.INVALID_TOKEN:
                message = "当前会话Token无效";
                break;
            case NotLoginException.TOKEN_TIMEOUT:
                message = "当前会话已过期";
                break;
            case NotLoginException.BE_REPLACED:
                message = "当前会话已被顶下线";
                break;
            case NotLoginException.KICK_OUT:
                message = "当前会话已被踢下线";
                break;
            default:
                message = "当前会话未登录";
                break;
        }
        log.warn("Sa-Token未登录异常 - 请求路径: {}, 异常类型: {}, 错误信息: {}",
                request.getRequestURI(), e.getType(), message);
        return Result.error(ResultCode.UNAUTHORIZED, message);
    }

    /**
     * 处理Sa-Token权限不足异常
     *
     * @param e       异常对象
     * @param request HTTP请求
     * @return 统一响应结果
     */
    @ExceptionHandler(NotPermissionException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public Result<Void> handleNotPermissionException(NotPermissionException e, HttpServletRequest request) {
        log.warn("Sa-Token权限不足异常 - 请求路径: {}, 缺少权限: {}",
                request.getRequestURI(), e.getPermission());
        return Result.error(ResultCode.FORBIDDEN, "权限不足，无法访问");
    }

    /**
     * 处理Sa-Token角色不足异常
     *
     * @param e       异常对象
     * @param request HTTP请求
     * @return 统一响应结果
     */
    @ExceptionHandler(NotRoleException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public Result<Void> handleNotRoleException(NotRoleException e, HttpServletRequest request) {
        log.warn("Sa-Token角色不足异常 - 请求路径: {}, 缺少角色: {}",
                request.getRequestURI(), e.getRole());
        return Result.error(ResultCode.FORBIDDEN, "角色权限不足，无法访问");
    }

    /**
     * 处理Sa-Token服务封禁异常
     *
     * @param e       异常对象
     * @param request HTTP请求
     * @return 统一响应结果
     */
    @ExceptionHandler(DisableServiceException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public Result<Void> handleDisableServiceException(DisableServiceException e, HttpServletRequest request) {
        log.warn("Sa-Token服务封禁异常 - 请求路径: {}, 封禁服务: {}, 封禁时间: {}秒",
                request.getRequestURI(), e.getService(), e.getDisableTime());
        return Result.error(ResultCode.FORBIDDEN, "当前账号已被封禁，无法访问");
    }

    /**
     * 处理Sa-Token其他异常
     *
     * @param e       异常对象
     * @param request HTTP请求
     * @return 统一响应结果
     */
    @ExceptionHandler(SaTokenException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Void> handleSaTokenException(SaTokenException e, HttpServletRequest request) {
        log.error("Sa-Token异常 - 请求路径: {}, 错误信息: {}", request.getRequestURI(), e.getMessage(), e);
        return Result.error(ResultCode.INTERNAL_SERVER_ERROR, "认证服务异常");
    }

    /**
     * 处理其他未知异常
     *
     * @param e       异常对象
     * @param request HTTP请求
     * @return 统一响应结果
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Void> handleException(Exception e, HttpServletRequest request) {
        log.error("系统内部异常 - 请求路径: {}, 错误信息: {}", request.getRequestURI(), e.getMessage(), e);
        return Result.error(ResultCode.INTERNAL_SERVER_ERROR, "请求异常，请稍后重试");
    }
} 