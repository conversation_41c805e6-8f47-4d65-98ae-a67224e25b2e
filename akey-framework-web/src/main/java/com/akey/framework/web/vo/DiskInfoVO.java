package com.akey.framework.web.vo;

import lombok.Data;
import java.util.List;

/**
 * 磁盘信息VO
 * 
 * <p>封装磁盘存储相关信息</p>
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
public class DiskInfoVO {

    /**
     * 磁盘分区列表
     */
    private List<DiskPartitionVO> partitions;



    /**
     * 磁盘分区信息
     */
    @Data
    public static class DiskPartitionVO {
        
        /**
         * 设备名称
         */
        private String deviceName;

        /**
         * 挂载点
         */
        private String mountPoint;

        /**
         * 文件系统类型
         */
        private String fileSystemType;

        /**
         * 分区类型
         */
        private String partitionType;

        /**
         * 总容量（字节）
         */
        private Long totalBytes;

        /**
         * 已用空间（字节）
         */
        private Long usedBytes;

        /**
         * 可用空间（字节）
         */
        private Long freeBytes;

        /**
         * 使用率（%）
         */
        private Double usagePercent;

        /**
         * 总容量（格式化字符串）
         */
        private String totalFormatted;

        /**
         * 已用空间（格式化字符串）
         */
        private String usedFormatted;

        /**
         * 可用空间（格式化字符串）
         */
        private String freeFormatted;

        /**
         * 是否可读
         */
        private Boolean readable;

        /**
         * 是否可写
         */
        private Boolean writable;
    }


}
