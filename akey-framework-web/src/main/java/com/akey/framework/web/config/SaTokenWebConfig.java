package com.akey.framework.web.config;

import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.context.SaTokenContext;
import cn.dev33.satoken.context.model.SaRequest;
import cn.dev33.satoken.context.model.SaResponse;
import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import com.akey.common.enums.IpWhitelistTypeEnum;
import com.akey.common.exception.BusinessException;
import com.akey.common.security.IpWhitelistValidator;
import com.akey.framework.web.converter.BaseEnumConverterFactory;
import com.akey.framework.web.util.DeviceInfoUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Sa-Token Web配置类
 *
 * <p>配置Sa-Token的Web拦截器和路由规则</p>
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Slf4j
@Configuration
public class SaTokenWebConfig implements WebMvcConfigurer {

    @Autowired
    private IpWhitelistValidator ipWhitelistValidator;

    /**
     * 注册Sa-Token拦截器
     *
     * @param registry 拦截器注册器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        log.info("注册 Sa-Token 拦截器...");

        // 注册Sa-Token拦截器，校验规则为StpUtil.checkLogin()登录校验
        registry.addInterceptor(new SaInterceptor(handle -> {
                    SaRouter
                            // 拦截所有路径
                            .match("/**")
                            // 排除登录相关接口
                            .notMatch("/auth/login", "/auth/captcha")
                            // 排除测试接口
                            .notMatch("/test/**")
                            // 排除Redis测试接口
                            .notMatch("/redis/**")
                            // 排除静态资源
                            .notMatch("/static/**", "/favicon.ico", "/error")
                            // 排除Swagger文档
                            .notMatch("/swagger-ui/**", "/v3/api-docs/**", "/swagger-resources/**")
                            // 排除Druid监控
                            .notMatch("/druid/**")
                            // 排除健康检查
                            .notMatch("/actuator/**")
                            // 执行登录校验
                            .check(r -> {
                                // 验证是否登录
                                StpUtil.checkLogin();

                                // IP白名单校验
                                try {
                                    // 获取HttpServletRequest对象
                                    HttpServletRequest request = ((ServletRequestAttributes)
                                            RequestContextHolder.getRequestAttributes()).getRequest();
                                    String clientIp = null;
                                    if (request != null) {
                                        clientIp = DeviceInfoUtil.getClientIp(request);
                                    }
                                    String userId = StpUtil.getLoginIdAsString();
                                    if (!ipWhitelistValidator.isIpAllowed(IpWhitelistTypeEnum.SYSTEM, userId, clientIp)) {
                                        throw new BusinessException("IP白名单校验失败，拒绝访问");
                                    }
                                }catch (Exception e){
                                    log.error("IP白名单校验失败", e);
                                    throw new BusinessException("IP白名单校验失败，拒绝访问");
                                }

                            });
                }))
                .addPathPatterns("/**");

        log.info("Sa-Token 拦截器注册完成");
    }

    /**
     * 注册自定义转换器
     *
     * @param registry 格式化器注册器
     */
    @Override
    public void addFormatters(FormatterRegistry registry) {
        log.info("注册枚举转换器...");

        // 注册BaseEnum转换器工厂
        registry.addConverterFactory(new BaseEnumConverterFactory());

        log.info("枚举转换器注册完成");
    }
}
