package com.akey.framework.web.annotation;

import java.lang.annotation.*;

/**
 * 谷歌验证码拦截注解
 * 
 * <p>用于标注需要谷歌验证码二次验证的Controller方法或类</p>
 * <p>支持类级别和方法级别注解，方法级别优先于类级别</p>
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface GoogleAuthRequired {

    /**
     * 是否强制验证谷歌验证码
     * 
     * <p>true: 无论用户是否设置了谷歌验证器，都必须提供有效验证码</p>
     * <p>false: 仅当用户已设置谷歌验证器时才需要验证码，未设置则跳过验证</p>
     * 
     * @return 是否强制验证
     */
    boolean required() default true;

    /**
     * 验证失败时的错误消息
     * 
     * @return 错误消息
     */
    String message() default "谷歌验证失败";

    /**
     * 验证码时间窗口
     * 
     * <p>用于控制TOTP验证码的时间容错窗口：</p>
     * <ul>
     *   <li>0: 只接受当前时间步长验证码（30秒，最严格）</li>
     *   <li>1: 接受前后1个时间步长验证码（90秒，默认）</li>
     *   <li>2: 接受前后2个时间步长验证码（150秒）</li>
     * </ul>
     * 
     * <p>数值越小安全性越高但用户体验越差，数值越大用户体验越好但安全性相对降低</p>
     * 
     * @return 时间窗口大小
     */
    int timeWindow() default 0;
}
