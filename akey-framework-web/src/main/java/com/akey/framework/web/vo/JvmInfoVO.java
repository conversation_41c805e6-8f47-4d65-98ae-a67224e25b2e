package com.akey.framework.web.vo;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * JVM信息VO
 * 
 * <p>封装Java虚拟机相关信息</p>
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
public class JvmInfoVO {

    /**
     * JVM名称
     */
    private String jvmName;

    /**
     * JVM版本
     */
    private String jvmVersion;

    /**
     * JVM供应商
     */
    private String jvmVendor;

    /**
     * Java版本
     */
    private String javaVersion;

    /**
     * Java主目录
     */
    private String javaHome;

    /**
     * Java类路径
     */
    private String classPath;

    /**
     * Java库路径
     */
    private String libraryPath;

    /**
     * JVM启动时间
     */
    private LocalDateTime jvmStartTime;

    /**
     * JVM运行时长（秒）
     */
    private Long jvmUptimeSeconds;

    /**
     * JVM运行时长（格式化字符串）
     */
    private String jvmUptimeFormatted;

    /**
     * JVM启动参数
     */
    private List<String> jvmArguments;

    /**
     * 系统属性
     */
    private SystemPropertiesVO systemProperties;

    /**
     * 类加载信息
     */
    private ClassLoadingVO classLoading;

    /**
     * 线程信息
     */
    private ThreadInfoVO threadInfo;

    /**
     * 系统属性信息
     */
    @Data
    public static class SystemPropertiesVO {
        
        /**
         * 用户名
         */
        private String userName;

        /**
         * 用户主目录
         */
        private String userHome;

        /**
         * 用户工作目录
         */
        private String userDir;

        /**
         * 操作系统名称
         */
        private String osName;

        /**
         * 操作系统版本
         */
        private String osVersion;

        /**
         * 操作系统架构
         */
        private String osArch;

        /**
         * 文件编码
         */
        private String fileEncoding;

        /**
         * 时区
         */
        private String timeZone;
    }

    /**
     * 类加载信息
     */
    @Data
    public static class ClassLoadingVO {
        
        /**
         * 已加载类总数
         */
        private Long totalLoadedClassCount;

        /**
         * 当前已加载类数量
         */
        private Integer loadedClassCount;

        /**
         * 已卸载类数量
         */
        private Long unloadedClassCount;

        /**
         * 是否启用详细类加载信息
         */
        private Boolean verboseEnabled;
    }

    /**
     * 线程信息
     */
    @Data
    public static class ThreadInfoVO {
        
        /**
         * 当前活跃线程数
         */
        private Integer activeThreadCount;

        /**
         * 峰值线程数
         */
        private Integer peakThreadCount;

        /**
         * 总启动线程数
         */
        private Long totalStartedThreadCount;

        /**
         * 守护线程数
         */
        private Integer daemonThreadCount;

        /**
         * 死锁线程数
         */
        private Integer deadlockedThreadCount;

        /**
         * 是否支持线程争用监控
         */
        private Boolean threadContentionMonitoringSupported;

        /**
         * 是否启用线程争用监控
         */
        private Boolean threadContentionMonitoringEnabled;

        /**
         * 是否支持线程CPU时间
         */
        private Boolean threadCpuTimeSupported;

        /**
         * 是否启用线程CPU时间
         */
        private Boolean threadCpuTimeEnabled;
    }
}
