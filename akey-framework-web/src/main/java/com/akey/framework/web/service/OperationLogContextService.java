package com.akey.framework.web.service;

import cn.dev33.satoken.stp.StpUtil;
import com.akey.common.enums.OperationStatus;
import com.akey.common.enums.OperationType;

import com.akey.common.service.OperationLogRecordService.OperationLogInfo;

import com.akey.framework.web.util.DeviceInfoUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 操作日志上下文信息收集服务
 * 
 * <p>负责收集操作日志相关的上下文信息</p>
 * <p>包括用户信息、请求信息、响应信息、系统信息等</p>
 * <p>将收集的信息封装为OperationLogInfo对象</p>
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Service
public class OperationLogContextService {

    @Autowired(required = false)
    private ObjectMapper objectMapper;

    /**
     * 收集操作日志信息
     * 
     * @param module 操作模块
     * @param type 操作类型
     * @param description 操作描述
     * @param joinPoint 连接点
     * @param result 方法执行结果
     * @param exception 异常信息
     * @param executionTime 执行时间
     * @param recordParams 是否记录请求参数
     * @param recordResult 是否记录响应结果
     * @param ignoreParams 忽略的参数
     * @param ignoreResult 忽略的响应字段
     * @return 操作日志信息
     */
    public OperationLogInfo collectOperationLogInfo(String module, OperationType type, String description,
                                                   ProceedingJoinPoint joinPoint, Object result, Throwable exception,
                                                   long executionTime, boolean recordParams, boolean recordResult,
                                                   String[] ignoreParams, String[] ignoreResult) {
        OperationLogInfo logInfo = new OperationLogInfo();
        
        try {
            // 设置基本信息
            setBasicInfo(logInfo, module, type, description, exception, executionTime);
            
            // 设置用户信息
            setUserInfo(logInfo);
            
            // 设置请求信息
            setRequestInfo(logInfo, joinPoint, recordParams, ignoreParams);
            
            // 设置响应信息
            setResponseInfo(logInfo, result, recordResult, ignoreResult);
            
            // 设置系统信息
            setSystemInfo(logInfo);

            // 注意：地理位置信息将在异步记录时获取，避免影响接口性能
            
        } catch (Exception e) {
            log.error("收集操作日志信息失败", e);
        }
        
        return logInfo;
    }

    /**
     * 设置基本信息
     */
    private void setBasicInfo(OperationLogInfo logInfo, String module, OperationType type, String description,
                             Throwable exception, long executionTime) {
        logInfo.setModule(module);
        logInfo.setType(type);
        logInfo.setDescription(description);
        logInfo.setOperationTime(LocalDateTime.now());
        logInfo.setExecutionTime(executionTime);
        
        // 设置操作状态
        if (exception == null) {
            logInfo.setStatus(OperationStatus.SUCCESS);
        } else {
            logInfo.setStatus(OperationStatus.FAILURE);
            String errorMessage = exception.getMessage();
            if (errorMessage != null && errorMessage.length() > 1000) {
                errorMessage = errorMessage.substring(0, 1000);
            }
            logInfo.setErrorMessage(errorMessage);
        }
    }

    /**
     * 设置用户信息
     */
    private void setUserInfo(OperationLogInfo logInfo) {
        try {
            if (StpUtil.isLogin()) {
                String userId = StpUtil.getLoginIdAsString();
                logInfo.setUserId(userId);
                
                // 尝试获取用户名（如果有的话）
                Object username = StpUtil.getSession().get("username");
                if (username != null) {
                    logInfo.setUsername(username.toString());
                }
            }
        } catch (Exception e) {
            log.debug("获取用户信息失败", e);
        }
    }

    /**
     * 设置请求信息
     */
    private void setRequestInfo(OperationLogInfo logInfo, ProceedingJoinPoint joinPoint, 
                               boolean recordParams, String[] ignoreParams) {
        try {
            HttpServletRequest request = getHttpServletRequest();
            if (request != null) {
                logInfo.setRequestMethod(request.getMethod());
                logInfo.setRequestUrl(request.getRequestURI());
            }
            
            // 设置请求参数
            if (recordParams) {
                Object params = getRequestParams(joinPoint, request);
                if (params != null) {
                    Object sanitizedParams = sanitizeData(params, ignoreParams);
                    logInfo.setRequestParams(sanitizedParams);
                }
            }
        } catch (Exception e) {
            log.debug("设置请求信息失败", e);
        }
    }

    /**
     * 设置响应信息
     */
    private void setResponseInfo(OperationLogInfo logInfo, Object result, boolean recordResult, String[] ignoreResult) {
        if (recordResult && result != null) {
            try {
                // 脱敏处理
                Object sanitizedResult = sanitizeData(result, ignoreResult);
                logInfo.setResponseResult(sanitizedResult);
                
                // 设置响应状态码（如果是Result类型）
                if (result.getClass().getSimpleName().contains("Result")) {
                    try {
                        Object code = result.getClass().getMethod("getCode").invoke(result);
                        if (code instanceof Integer) {
                            logInfo.setResponseStatus((Integer) code);
                        }
                        
                        Object message = result.getClass().getMethod("getMessage").invoke(result);
                        if (message != null) {
                            logInfo.setResponseMessage(message.toString());
                        }
                    } catch (Exception e) {
                        log.debug("获取响应状态信息失败", e);
                    }
                }
            } catch (Exception e) {
                log.debug("处理响应结果失败", e);
                logInfo.setResponseResult("结果处理失败");
            }
        }
    }

    /**
     * 设置系统信息
     */
    private void setSystemInfo(OperationLogInfo logInfo) {
        try {
            HttpServletRequest request = getHttpServletRequest();
            if (request != null) {
                // 设置IP地址
                String clientIp = DeviceInfoUtil.getRealIpAddress(request);
                logInfo.setClientIp(clientIp);
                
                // 设置用户代理
                String userAgent = request.getHeader("User-Agent");
                if (userAgent != null && userAgent.length() > 1000) {
                    userAgent = userAgent.substring(0, 1000);
                }
                logInfo.setUserAgent(userAgent);
            }
        } catch (Exception e) {
            log.debug("获取系统信息失败", e);
        }
    }



    /**
     * 获取HttpServletRequest对象
     */
    private HttpServletRequest getHttpServletRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            return attributes != null ? attributes.getRequest() : null;
        } catch (Exception e) {
            log.debug("获取HttpServletRequest失败", e);
            return null;
        }
    }

    /**
     * 获取请求参数
     */
    private Object getRequestParams(ProceedingJoinPoint joinPoint, HttpServletRequest request) {
        try {
            Map<String, Object> params = new HashMap<>();
            
            // 获取方法参数
            Object[] args = joinPoint.getArgs();
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            String[] paramNames = signature.getParameterNames();
            
            if (args != null && paramNames != null) {
                for (int i = 0; i < args.length && i < paramNames.length; i++) {
                    if (args[i] != null) {
                        // 过滤掉HttpServletRequest、HttpServletResponse等对象
                        if (!isSystemObject(args[i])) {
                            params.put(paramNames[i], args[i]);
                        }
                    }
                }
            }
            
            // 获取URL参数
            if (request != null && request.getParameterMap() != null) {
                request.getParameterMap().forEach((key, values) -> {
                    if (values != null && values.length > 0) {
                        params.put(key, values.length == 1 ? values[0] : values);
                    }
                });
            }
            
            return params.isEmpty() ? null : params;
        } catch (Exception e) {
            log.debug("获取请求参数失败", e);
            return null;
        }
    }

    /**
     * 判断是否为系统对象（不需要记录的对象）
     */
    private boolean isSystemObject(Object obj) {
        if (obj == null) {
            return true;
        }
        
        String className = obj.getClass().getName();
        return className.startsWith("jakarta.servlet.") ||
               className.startsWith("javax.servlet.") ||
               className.startsWith("org.springframework.") ||
               className.startsWith("org.apache.catalina.");
    }

    /**
     * 数据脱敏处理
     */
    private Object sanitizeData(Object data, String[] ignoreFields) {
        if (data == null || ignoreFields == null || ignoreFields.length == 0) {
            return data;
        }

        // 如果ObjectMapper不可用，跳过脱敏处理
        if (objectMapper == null) {
            log.debug("ObjectMapper不可用，跳过数据脱敏处理");
            return data;
        }

        try {
            // 转换为Map进行处理
            String jsonStr = objectMapper.writeValueAsString(data);
            @SuppressWarnings("unchecked")
            Map<String, Object> dataMap = objectMapper.readValue(jsonStr, Map.class);

            // 脱敏处理
            Map<String, Object> sanitizedMap = new HashMap<>(dataMap);
            Arrays.stream(ignoreFields).forEach(field -> {
                if (sanitizedMap.containsKey(field)) {
                    sanitizedMap.put(field, "******");
                }
            });

            return sanitizedMap;
        } catch (Exception e) {
            log.debug("数据脱敏处理失败", e);
            return data;
        }
    }
}
