package com.akey.framework.web.config;

import com.akey.framework.web.filter.RsaRequestFilter;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;

/**
 * RSA加密配置类
 * 
 * <p>用于配置RSA加密相关的Bean和过滤器</p>
 * <p>只有在RSA加密启用时才会生效</p>
 * 
 * <AUTHOR>
 * @since 2025-07-05
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(RsaProperties.class)
@RequiredArgsConstructor
public class RsaConfig {
    
    private final RsaProperties rsaProperties;
    
    /**
     * 注册RSA请求解密过滤器
     *
     * @param rsaRequestFilter RSA请求解密过滤器
     * @return 过滤器注册Bean
     */
    @Bean
    @ConditionalOnProperty(prefix = "akey.security.rsa", name = "enabled", havingValue = "true")
    public FilterRegistrationBean<RsaRequestFilter> rsaRequestFilterRegistration(RsaRequestFilter rsaRequestFilter) {
        log.info("注册RSA请求解密过滤器...");

        FilterRegistrationBean<RsaRequestFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(rsaRequestFilter);
        registration.addUrlPatterns("/*");
        registration.setName("rsaRequestFilter");
        registration.setOrder(Ordered.HIGHEST_PRECEDENCE + 1);

        log.info("RSA请求解密过滤器注册完成");
        return registration;
    }


    
    /**
     * 初始化RSA配置
     */
    @Bean
    public Object initRsaConfig() {
        if (rsaProperties.getEnabled()) {
            log.info("RSA加密功能已启用");
            
            if (!rsaProperties.isValidConfig()) {
                log.warn("RSA配置无效，请检查密钥配置");
            } else {
                log.info("RSA配置验证通过");
            }
        } else {
            log.info("RSA加密功能已禁用");
        }
        
        return new Object();
    }
}
