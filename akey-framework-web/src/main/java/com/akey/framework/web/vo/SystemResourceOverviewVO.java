package com.akey.framework.web.vo;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 系统资源概览信息VO
 * 
 * <p>提供系统资源的关键指标概览，适用于仪表板显示</p>
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
public class SystemResourceOverviewVO {

    /**
     * 数据采集时间
     */
    private LocalDateTime collectTime;

    /**
     * 是否使用Sigar库
     */
    private Boolean usingSigar;

    /**
     * CPU概览信息
     */
    private CpuOverviewVO cpuOverview;

    /**
     * 内存概览信息
     */
    private MemoryOverviewVO memoryOverview;

    /**
     * 磁盘概览信息
     */
    private DiskOverviewVO diskOverview;

    /**
     * 服务器概览信息
     */
    private ServerOverviewVO serverOverview;

    /**
     * CPU概览信息
     */
    @Data
    public static class CpuOverviewVO {
        
        /**
         * 逻辑CPU核心数
         */
        private Integer logicalCores;

        /**
         * CPU总使用率（%）
         */
        private Double usagePercent;

        /**
         * CPU使用率状态
         * LOW: < 30%, MEDIUM: 30-70%, HIGH: > 70%
         */
        public String getUsageStatus() {
            if (usagePercent == null) return "UNKNOWN";
            if (usagePercent < 30) return "LOW";
            if (usagePercent < 70) return "MEDIUM";
            return "HIGH";
        }
    }

    /**
     * 内存概览信息
     */
    @Data
    public static class MemoryOverviewVO {
        
        /**
         * 系统内存使用率（%）
         */
        private Double systemUsagePercent;

        /**
         * 系统总内存（格式化字符串）
         */
        private String systemTotalFormatted;

        /**
         * JVM堆内存使用率（%）
         */
        private Double jvmUsagePercent;

        /**
         * JVM最大堆内存（格式化字符串）
         */
        private String jvmMaxFormatted;

        /**
         * 系统内存使用率状态
         */
        public String getSystemUsageStatus() {
            if (systemUsagePercent == null) return "UNKNOWN";
            if (systemUsagePercent < 50) return "LOW";
            if (systemUsagePercent < 80) return "MEDIUM";
            return "HIGH";
        }

        /**
         * JVM内存使用率状态
         */
        public String getJvmUsageStatus() {
            if (jvmUsagePercent == null) return "UNKNOWN";
            if (jvmUsagePercent < 60) return "LOW";
            if (jvmUsagePercent < 85) return "MEDIUM";
            return "HIGH";
        }
    }

    /**
     * 磁盘概览信息
     */
    @Data
    public static class DiskOverviewVO {
        
        /**
         * 磁盘分区数量
         */
        private Integer partitionCount;

        /**
         * 整体磁盘使用率（%）
         */
        private Double overallUsagePercent;

        /**
         * 磁盘使用率状态
         */
        public String getUsageStatus() {
            if (overallUsagePercent == null) return "UNKNOWN";
            if (overallUsagePercent < 60) return "LOW";
            if (overallUsagePercent < 85) return "MEDIUM";
            return "HIGH";
        }
    }

    /**
     * 服务器概览信息
     */
    @Data
    public static class ServerOverviewVO {
        
        /**
         * 服务器主机名
         */
        private String hostname;

        /**
         * 操作系统名称
         */
        private String osName;

        /**
         * 系统运行时长（格式化字符串）
         */
        private String systemUptimeFormatted;
    }

    /**
     * 获取整体系统健康状态
     * 
     * @return 健康状态：HEALTHY, WARNING, CRITICAL
     */
    public String getOverallHealthStatus() {
        int criticalCount = 0;
        int warningCount = 0;

        // 检查CPU状态
        if (cpuOverview != null) {
            String cpuStatus = cpuOverview.getUsageStatus();
            if ("HIGH".equals(cpuStatus)) {
                criticalCount++;
            } else if ("MEDIUM".equals(cpuStatus)) {
                warningCount++;
            }
        }

        // 检查内存状态
        if (memoryOverview != null) {
            String systemMemStatus = memoryOverview.getSystemUsageStatus();
            String jvmMemStatus = memoryOverview.getJvmUsageStatus();
            
            if ("HIGH".equals(systemMemStatus) || "HIGH".equals(jvmMemStatus)) {
                criticalCount++;
            } else if ("MEDIUM".equals(systemMemStatus) || "MEDIUM".equals(jvmMemStatus)) {
                warningCount++;
            }
        }

        // 检查磁盘状态
        if (diskOverview != null) {
            String diskStatus = diskOverview.getUsageStatus();
            if ("HIGH".equals(diskStatus)) {
                criticalCount++;
            } else if ("MEDIUM".equals(diskStatus)) {
                warningCount++;
            }
        }

        // 根据问题数量确定整体状态
        if (criticalCount > 0) {
            return "CRITICAL";
        } else if (warningCount > 0) {
            return "WARNING";
        } else {
            return "HEALTHY";
        }
    }
}
