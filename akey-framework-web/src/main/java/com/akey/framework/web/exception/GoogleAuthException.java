package com.akey.framework.web.exception;

import lombok.Getter;

/**
 * 谷歌验证码相关异常
 * 
 * <p>用于处理谷歌验证码验证过程中的各种异常情况</p>
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Getter
public class GoogleAuthException extends RuntimeException {

    /**
     * 错误代码
     */
    private final String errorCode;

    /**
     * 错误代码常量
     */

    /**
     * 谷歌验证码未提供
     *
     * <p>当用户已设置谷歌验证器但在登录或验证时未提供验证码时使用</p>
     * <p>适用场景：登录验证、敏感操作验证等需要谷歌验证码的场合</p>
     */
    public static final String CODE_NOT_PROVIDED = "GOOGLE_AUTH_CODE_NOT_PROVIDED";

    /**
     * 谷歌验证码无效
     *
     * <p>当用户提供的谷歌验证码格式错误或验证失败时使用</p>
     * <p>包括以下情况：</p>
     * <ul>
     *   <li>验证码格式不正确（非6位数字）</li>
     *   <li>验证码已过期</li>
     *   <li>验证码与用户密钥不匹配</li>
     * </ul>
     */
    public static final String CODE_INVALID = "GOOGLE_AUTH_CODE_INVALID";

    /**
     * 用户未设置谷歌验证器
     *
     * <p>当尝试对未设置谷歌验证器的用户进行谷歌验证相关操作时使用</p>
     * <p>适用场景：验证谷歌验证码、禁用谷歌验证器等需要用户已设置验证器的操作</p>
     */
    public static final String USER_NOT_SETUP = "GOOGLE_AUTH_NOT_SETUP";

    /**
     * 用户未认证
     *
     * <p>当执行需要用户登录的谷歌验证相关操作时，用户未登录或登录状态已失效</p>
     * <p>适用场景：设置谷歌验证器、验证谷歌验证码等需要用户身份的操作</p>
     */
    public static final String USER_NOT_AUTHENTICATED = "USER_NOT_AUTHENTICATED";

    /**
     * 谷歌验证失败
     *
     * <p>通用的谷歌验证失败错误码，用于其他具体错误码无法覆盖的验证失败场景</p>
     * <p>包括以下情况：</p>
     * <ul>
     *   <li>系统内部验证异常</li>
     *   <li>网络连接问题导致的验证失败</li>
     *   <li>其他未分类的验证失败情况</li>
     * </ul>
     */
    public static final String VERIFICATION_FAILED = "GOOGLE_AUTH_VERIFICATION_FAILED";

    /**
     * 构造函数
     * 
     * @param errorCode 错误代码
     * @param message 错误消息
     */
    public GoogleAuthException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    /**
     * 构造函数
     * 
     * @param errorCode 错误代码
     * @param message 错误消息
     * @param cause 原因异常
     */
    public GoogleAuthException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    /**
     * 创建验证码未提供异常
     * 
     * @return GoogleAuthException
     */
    public static GoogleAuthException codeNotProvided() {
        return new GoogleAuthException(CODE_NOT_PROVIDED, "请提供谷歌验证码");
    }

    /**
     * 创建验证码无效异常
     * 
     * @return GoogleAuthException
     */
    public static GoogleAuthException codeInvalid() {
        return new GoogleAuthException(CODE_INVALID, "谷歌验证码无效");
    }

    /**
     * 创建用户未设置谷歌验证器异常
     * 
     * @return GoogleAuthException
     */
    public static GoogleAuthException userNotSetup() {
        return new GoogleAuthException(USER_NOT_SETUP, "用户未设置谷歌验证器");
    }

    /**
     * 创建用户未认证异常
     * 
     * @return GoogleAuthException
     */
    public static GoogleAuthException userNotAuthenticated() {
        return new GoogleAuthException(USER_NOT_AUTHENTICATED, "用户未登录");
    }

    /**
     * 创建验证失败异常
     * 
     * @param message 错误消息
     * @return GoogleAuthException
     */
    public static GoogleAuthException verificationFailed(String message) {
        return new GoogleAuthException(VERIFICATION_FAILED, message);
    }

    /**
     * 创建自定义异常
     * 
     * @param errorCode 错误代码
     * @param message 错误消息
     * @return GoogleAuthException
     */
    public static GoogleAuthException custom(String errorCode, String message) {
        return new GoogleAuthException(errorCode, message);
    }
}
