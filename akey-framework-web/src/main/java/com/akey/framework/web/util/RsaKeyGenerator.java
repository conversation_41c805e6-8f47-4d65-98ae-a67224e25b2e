package com.akey.framework.web.util;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.Base64;

/**
 * RSA密钥生成工具类
 * 
 * <p>功能说明：</p>
 * <ul>
 *   <li>生成RSA密钥对（公钥和私钥）</li>
 *   <li>支持不同的密钥长度（1024、2048、4096位）</li>
 *   <li>提供传统Base64格式的密钥输出</li>
 *   <li>用于开发和测试环境的密钥生成</li>
 * </ul>
 *
 * <p>使用示例：</p>
 * <pre>
 * // 生成2048位密钥对
 * RsaKeyPair keyPair = RsaKeyGenerator.generateKeyPair(2048);
 * System.out.println("公钥: " + keyPair.getPublicKey());
 * System.out.println("私钥: " + keyPair.getPrivateKey());
 * </pre>
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Slf4j
public class RsaKeyGenerator {

    /**
     * RSA算法名称
     */
    private static final String RSA_ALGORITHM = "RSA";

    /**
     * 默认密钥长度（2048位）
     */
    private static final int DEFAULT_KEY_SIZE = 2048;

    /**
     * 生成RSA密钥对（使用默认长度2048位）
     * 
     * @return RSA密钥对
     */
    public static RsaKeyPair generateKeyPair() {
        return generateKeyPair(DEFAULT_KEY_SIZE);
    }

    /**
     * 生成指定长度的RSA密钥对
     * 
     * @param keySize 密钥长度（推荐2048或4096位）
     * @return RSA密钥对
     */
    public static RsaKeyPair generateKeyPair(int keySize) {
        try {
            log.info("开始生成{}位RSA密钥对...", keySize);
            
            // 创建密钥对生成器
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(RSA_ALGORITHM);
            keyPairGenerator.initialize(keySize);
            
            // 生成密钥对
            KeyPair keyPair = keyPairGenerator.generateKeyPair();
            PublicKey publicKey = keyPair.getPublic();
            PrivateKey privateKey = keyPair.getPrivate();
            
            // 转换为Base64格式
            String publicKeyBase64 = formatPublicKey(publicKey);
            String privateKeyBase64 = formatPrivateKey(privateKey);
            
            log.info("RSA密钥对生成完成");
            
            return new RsaKeyPair(publicKeyBase64, privateKeyBase64, keySize);
            
        } catch (Exception e) {
            log.error("生成RSA密钥对失败", e);
            throw new RuntimeException("生成RSA密钥对失败: " + e.getMessage(), e);
        }
    }

    /**
     * 格式化公钥为传统Base64格式
     *
     * @param publicKey 公钥对象
     * @return Base64格式的公钥字符串
     */
    private static String formatPublicKey(PublicKey publicKey) {
        byte[] encoded = publicKey.getEncoded();
        return Base64.getEncoder().encodeToString(encoded);
    }

    /**
     * 格式化私钥为传统Base64格式
     *
     * @param privateKey 私钥对象
     * @return Base64格式的私钥字符串
     */
    private static String formatPrivateKey(PrivateKey privateKey) {
        byte[] encoded = privateKey.getEncoded();
        return Base64.getEncoder().encodeToString(encoded);
    }

    /**
     * RSA密钥对数据类
     */
    @Data
    public static class RsaKeyPair {
        
        /**
         * Base64格式的公钥
         */
        private final String publicKey;

        /**
         * Base64格式的私钥
         */
        private final String privateKey;
        
        /**
         * 密钥长度（位）
         */
        private final int keySize;

        /**
         * 构造函数
         *
         * @param publicKey Base64格式的公钥
         * @param privateKey Base64格式的私钥
         * @param keySize 密钥长度
         */
        public RsaKeyPair(String publicKey, String privateKey, int keySize) {
            this.publicKey = publicKey;
            this.privateKey = privateKey;
            this.keySize = keySize;
        }

        /**
         * 打印密钥对信息
         */
        public void printKeyPair() {
            System.out.println("=== RSA密钥对 (" + keySize + "位) ===");
            System.out.println();

            System.out.println("公钥:");
            System.out.println(publicKey);
            System.out.println();
            System.out.println("私钥:");
            System.out.println(privateKey);
            System.out.println();


        }
    }

    /**
     * 主方法，用于生成密钥对
     * 
     * @param args 命令行参数，可选密钥长度
     */
    public static void main(String[] args) {
        int keySize = DEFAULT_KEY_SIZE;
        
        if (args.length > 0) {
            try {
                keySize = Integer.parseInt(args[0]);
                if (keySize < 1024 || keySize > 4096) {
                    System.err.println("密钥长度应在1024-4096位之间，使用默认值2048位");
                    keySize = DEFAULT_KEY_SIZE;
                }
            } catch (NumberFormatException e) {
                System.err.println("无效的密钥长度参数，使用默认值2048位");
            }
        }
        
        RsaKeyPair keyPair = generateKeyPair(keySize);
        keyPair.printKeyPair();

        keyPair = generateKeyPair(keySize);
        keyPair.printKeyPair();
    }
}
