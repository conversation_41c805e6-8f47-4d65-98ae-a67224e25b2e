package com.akey.framework.web.filter;

import com.akey.common.constant.SystemConstant;
import com.akey.framework.web.config.RsaProperties;
import com.akey.framework.web.util.RsaUtil;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Enumeration;

/**
 * RSA请求解密过滤器
 * 
 * <p>功能说明：</p>
 * <ul>
 *   <li>拦截HTTP请求，对加密的请求体进行解密</li>
 *   <li>支持POST、PUT、PATCH等包含请求体的HTTP方法</li>
 *   <li>只有在RSA功能启用时才会生效</li>
 *   <li>解密失败时记录日志并继续处理原始请求</li>
 * </ul>
 *
 * <p>工作流程：</p>
 * <ol>
 *   <li>检查请求解密功能是否启用</li>
 *   <li>检查请求方法是否支持（POST、PUT、PATCH）</li>
 *   <li>检查请求头是否包含加密标识（X-Encrypted-Request: true）</li>
 *   <li>读取纯Base64请求体并尝试解密</li>
 *   <li>将解密后的数据包装为新的请求对象</li>
 *   <li>继续后续的过滤器链处理</li>
 * </ol>
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "akey.security.rsa", name = "enabled", havingValue = "true")
public class RsaRequestFilter implements Filter {

    private final RsaProperties rsaProperties;



    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        HttpServletRequest httpRequest = (HttpServletRequest) request;

        // 检查是否需要解密
        if (!needDecryption(httpRequest)) {
            chain.doFilter(request, response);
            return;
        }

        try {
            // 读取并解密请求体
            String decryptedBody = decryptRequestBody(httpRequest);
            
            if (StringUtils.hasText(decryptedBody)) {
                // 创建包装的请求对象
                DecryptedRequestWrapper wrappedRequest = new DecryptedRequestWrapper(httpRequest, decryptedBody);
                log.debug("请求解密成功，继续处理解密后的请求");
                chain.doFilter(wrappedRequest, response);
            } else {
                log.warn("请求解密后内容为空，使用原始请求继续处理");
                chain.doFilter(request, response);
            }
            
        } catch (Exception e) {
            log.error("请求解密失败，使用原始请求继续处理", e);
            chain.doFilter(request, response);
        }
    }

    /**
     * 检查请求是否需要解密
     *
     * @param request HTTP请求
     * @return 需要解密返回true，否则返回false
     */
    private boolean needDecryption(HttpServletRequest request) {
        // 检查请求解密功能是否启用
        if (!rsaProperties.isRequestDecryptionEnabled()) {
            return false;
        }

        // 检查是否是包含请求体的HTTP方法
        String method = request.getMethod();
        if (!("POST".equalsIgnoreCase(method) ||
              "PUT".equalsIgnoreCase(method) ||
              "PATCH".equalsIgnoreCase(method))) {
            return false;
        }

        // 检查请求头是否包含加密标识
        String encryptedHeader = request.getHeader(SystemConstant.DECRYPTED_REQUEST_HEADER);
        return "true".equalsIgnoreCase(encryptedHeader);
    }

    /**
     * 解密请求体
     * 
     * @param request HTTP请求
     * @return 解密后的请求体内容
     * @throws Exception 解密失败时抛出异常
     */
    private String decryptRequestBody(HttpServletRequest request) throws Exception {
        // 读取请求体
        StringBuilder requestBody = new StringBuilder();
        try (BufferedReader reader = request.getReader()) {
            String line;
            while ((line = reader.readLine()) != null) {
                requestBody.append(line);
            }
        }

        String encryptedData = requestBody.toString();
        if (!StringUtils.hasText(encryptedData)) {
            log.debug("请求体为空，无需解密");
            return "";
        }

        // 获取私钥
        String privateKey = rsaProperties.getFormattedPrivateKey();
        if (!StringUtils.hasText(privateKey)) {
            throw new IllegalStateException("RSA私钥未配置");
        }

        // 解密数据
        log.debug("开始解密请求体，数据长度: {}", encryptedData.length());
        String decryptedData = RsaUtil.decrypt(encryptedData, privateKey);
        log.debug("请求体解密完成，解密后数据长度: {}", decryptedData.length());

        return decryptedData;
    }

    /**
     * 解密后的请求包装器
     * 用于包装解密后的请求体数据
     */
    private static class DecryptedRequestWrapper extends HttpServletRequestWrapper {

        private final String decryptedBody;
        private final byte[] bodyBytes;

        public DecryptedRequestWrapper(HttpServletRequest request, String decryptedBody) {
            super(request);
            this.decryptedBody = decryptedBody;
            this.bodyBytes = decryptedBody.getBytes(StandardCharsets.UTF_8);
        }

        @Override
        public ServletInputStream getInputStream() throws IOException {
            return new DecryptedServletInputStream(new ByteArrayInputStream(bodyBytes));
        }

        @Override
        public BufferedReader getReader() throws IOException {
            return new BufferedReader(new InputStreamReader(getInputStream(), StandardCharsets.UTF_8));
        }

        @Override
        public String getContentType() {
            // 解密后的数据通常是JSON格式，设置正确的Content-Type
            // 这样Spring的消息转换器就能正确处理数据绑定
            return "application/json;charset=UTF-8";
        }

        @Override
        public String getHeader(String name) {
            // 如果请求Content-Type头，返回JSON类型
            if ("Content-Type".equalsIgnoreCase(name) || "content-type".equalsIgnoreCase(name)) {
                return getContentType();
            }
            return super.getHeader(name);
        }

        @Override
        public Enumeration<String> getHeaders(String name) {
            // 如果请求Content-Type头，返回JSON类型
            if ("Content-Type".equalsIgnoreCase(name) || "content-type".equalsIgnoreCase(name)) {
                return Collections.enumeration(Collections.singletonList(getContentType()));
            }
            return super.getHeaders(name);
        }

        @Override
        public int getContentLength() {
            // 返回解密后数据的实际长度
            return bodyBytes.length;
        }

        @Override
        public long getContentLengthLong() {
            // 返回解密后数据的实际长度
            return bodyBytes.length;
        }
    }

    /**
     * 解密后的ServletInputStream实现
     */
    private static class DecryptedServletInputStream extends ServletInputStream {

        private final ByteArrayInputStream inputStream;

        public DecryptedServletInputStream(ByteArrayInputStream inputStream) {
            this.inputStream = inputStream;
        }

        @Override
        public boolean isFinished() {
            return inputStream.available() == 0;
        }

        @Override
        public boolean isReady() {
            return true;
        }

        @Override
        public void setReadListener(ReadListener readListener) {
            // 不需要实现
        }

        @Override
        public int read() throws IOException {
            return inputStream.read();
        }
    }
}
