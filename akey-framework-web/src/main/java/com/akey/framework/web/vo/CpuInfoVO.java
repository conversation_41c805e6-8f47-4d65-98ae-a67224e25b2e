package com.akey.framework.web.vo;

import lombok.Data;

/**
 * CPU信息VO
 * 
 * <p>封装CPU相关的系统信息</p>
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
public class CpuInfoVO {

    /**
     * CPU型号
     */
    private String model;

    /**
     * CPU供应商
     */
    private String vendor;

    /**
     * CPU频率（MHz）
     */
    private Long frequency;

    /**
     * 物理CPU核心数
     */
    private Integer physicalCores;

    /**
     * 逻辑CPU核心数（包含超线程）
     */
    private Integer logicalCores;

    /**
     * CPU缓存大小（KB）
     */
    private Long cacheSize;

    /**
     * CPU使用率信息
     */
    private CpuUsageVO usage;

    /**
     * 系统负载平均值
     */
    private LoadAverageVO loadAverage;

    /**
     * CPU架构
     */
    private String architecture;

    /**
     * CPU系列
     */
    private String family;

    /**
     * CPU步进
     */
    private String stepping;

    /**
     * CPU使用率详细信息
     */
    @Data
    public static class CpuUsageVO {
        
        /**
         * 用户态使用率（%）
         */
        private Double userPercent;

        /**
         * 系统态使用率（%）
         */
        private Double systemPercent;

        /**
         * 空闲率（%）
         */
        private Double idlePercent;

        /**
         * 等待I/O使用率（%）
         */
        private Double waitPercent;

        /**
         * 中断使用率（%）
         */
        private Double irqPercent;

        /**
         * 软中断使用率（%）
         */
        private Double softIrqPercent;

        /**
         * 总使用率（%）
         */
        private Double totalPercent;
    }

    /**
     * 系统负载平均值
     */
    @Data
    public static class LoadAverageVO {
        
        /**
         * 1分钟负载平均值
         */
        private Double oneMinute;

        /**
         * 5分钟负载平均值
         */
        private Double fiveMinutes;

        /**
         * 15分钟负载平均值
         */
        private Double fifteenMinutes;
    }
}
