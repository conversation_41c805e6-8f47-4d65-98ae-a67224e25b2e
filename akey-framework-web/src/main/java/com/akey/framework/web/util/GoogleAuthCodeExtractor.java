package com.akey.framework.web.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * 谷歌验证码提取器
 * 
 * <p>支持从多种来源提取谷歌验证码：</p>
 * <ul>
 *   <li>请求头：X-Google-Auth-Code（推荐，RESTful风格）</li>
 *   <li>请求参数：googleAuthCode（GET请求友好）</li>
 *   <li>请求体：JSON中的googleAuthCode字段（POST请求）</li>
 * </ul>
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GoogleAuthCodeExtractor {

    private final ObjectMapper objectMapper;

    /**
     * 默认请求头名称
     */
    private static final String DEFAULT_HEADER_NAME = "X-Google-Auth-Code";

    /**
     * 默认请求参数名称
     */
    private static final String DEFAULT_PARAM_NAME = "googleAuthCode";

    /**
     * 获取方式优先级
     */
    private static final List<String> DEFAULT_SOURCE_PRIORITY = Arrays.asList("header", "param", "body");

    /**
     * 提取谷歌验证码
     * 
     * <p>按照优先级从不同来源提取验证码</p>
     * 
     * @return 验证码，如果未找到则返回null
     */
    public String extractCode() {
        HttpServletRequest request = getCurrentRequest();
        if (request == null) {
            log.warn("无法获取当前HTTP请求");
            return null;
        }

        return extractCode(request);
    }

    /**
     * 从指定请求中提取谷歌验证码
     * 
     * @param request HTTP请求
     * @return 验证码，如果未找到则返回null
     */
    public String extractCode(HttpServletRequest request) {
        for (String source : DEFAULT_SOURCE_PRIORITY) {
            String code = null;
            
            switch (source) {
                case "header":
                    code = extractFromHeader(request);
                    break;
                case "param":
                    code = extractFromParameter(request);
                    break;
                case "body":
                    code = extractFromRequestBody(request);
                    break;
                default:
                    log.warn("未知的验证码获取方式: {}", source);
            }

            if (StringUtils.hasText(code)) {
                log.debug("从{}中提取到谷歌验证码", source);
                return code.trim();
            }
        }

        log.debug("未找到谷歌验证码");
        return null;
    }

    /**
     * 从请求头中提取验证码
     */
    private String extractFromHeader(HttpServletRequest request) {
        return request.getHeader(DEFAULT_HEADER_NAME);
    }

    /**
     * 从请求参数中提取验证码
     */
    private String extractFromParameter(HttpServletRequest request) {
        return request.getParameter(DEFAULT_PARAM_NAME);
    }

    /**
     * 从请求体中提取验证码
     * 
     * <p>注意：由于HttpServletRequest的输入流只能读取一次，</p>
     * <p>在实际项目中建议使用HttpServletRequestWrapper来缓存请求体内容</p>
     */
    private String extractFromRequestBody(HttpServletRequest request) {
        // 只处理JSON格式的请求体
        String contentType = request.getContentType();
        if (contentType == null || !contentType.toLowerCase().contains("application/json")) {
            return null;
        }

        try {
            // 读取请求体内容
            String requestBody = getRequestBody(request);
            if (!StringUtils.hasText(requestBody)) {
                return null;
            }

            // 解析JSON并提取验证码
            JsonNode jsonNode = objectMapper.readTree(requestBody);
            JsonNode codeNode = jsonNode.get(DEFAULT_PARAM_NAME);
            
            return codeNode != null ? codeNode.asText() : null;
            
        } catch (Exception e) {
            log.debug("从请求体中提取谷歌验证码失败，可能是请求体已被读取: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 读取请求体内容
     */
    private String getRequestBody(HttpServletRequest request) throws IOException {
        StringBuilder stringBuilder = new StringBuilder();
        
        try (BufferedReader reader = request.getReader()) {
            String line;
            while ((line = reader.readLine()) != null) {
                stringBuilder.append(line);
            }
        }
        
        return stringBuilder.toString();
    }

    /**
     * 获取当前HTTP请求
     */
    private HttpServletRequest getCurrentRequest() {
        ServletRequestAttributes attributes = 
            (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return attributes != null ? attributes.getRequest() : null;
    }
}
