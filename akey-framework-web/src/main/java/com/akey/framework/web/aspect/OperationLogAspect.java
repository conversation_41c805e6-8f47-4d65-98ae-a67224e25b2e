package com.akey.framework.web.aspect;

import cn.dev33.satoken.stp.StpUtil;
import com.akey.common.annotation.OperationLog;
import com.akey.common.annotation.OperationModule;
import com.akey.common.enums.OperationType;
import com.akey.common.service.OperationLogRecordService;
import com.akey.common.service.OperationLogRecordService.OperationLogInfo;
import com.akey.framework.web.service.OperationLogContextService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;

/**
 * 操作日志AOP切面
 * 
 * <p>拦截带有@OperationLog注解的方法，自动记录操作日志</p>
 * <p>支持异步记录，不影响主业务性能</p>
 * <p>通过接口调用dao层进行数据持久化，实现解耦</p>
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Aspect
@Component
@Order(100)
@ConditionalOnBean(OperationLogRecordService.class)
public class OperationLogAspect {

    @Autowired
    private OperationLogContextService contextService;

    @Autowired
    private OperationLogRecordService recordService;

    /**
     * 定义切点：拦截所有标注了@OperationLog注解的方法
     */
    @Pointcut("@annotation(com.akey.common.annotation.OperationLog)")
    public void operationLogPointcut() {
    }

    /**
     * 环绕通知：记录操作日志
     * 
     * @param joinPoint 连接点
     * @return 方法执行结果
     * @throws Throwable 方法执行异常
     */
    @Around("operationLogPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        Object result = null;
        Throwable exception = null;

        try {
            // 执行目标方法
            result = joinPoint.proceed();
            return result;
        } catch (Throwable e) {
            exception = e;
            throw e;
        } finally {
            try {
                // 记录操作日志
                recordOperationLog(joinPoint, result, exception, startTime);
            } catch (Exception e) {
                log.error("记录操作日志失败", e);
            }
        }
    }

    /**
     * 记录操作日志
     * 
     * @param joinPoint 连接点
     * @param result 方法执行结果
     * @param exception 异常信息
     * @param startTime 开始时间
     */
    private void recordOperationLog(ProceedingJoinPoint joinPoint, Object result, Throwable exception, long startTime) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            
            // 获取@OperationLog注解
            OperationLog operationLog = method.getAnnotation(OperationLog.class);
            if (operationLog == null) {
                return;
            }

            // 检查是否需要记录失败日志
            if (exception != null && !operationLog.recordOnFailure()) {
                log.debug("操作失败且配置不记录失败日志，跳过记录");
                return;
            }

            // 获取操作模块
            String module = getOperationModule(joinPoint, operationLog);
            
            // 获取操作类型
            OperationType type = operationLog.type();
            
            // 获取操作描述
            String description = getOperationDescription(operationLog);
            
            // 计算执行时间
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 收集操作日志信息
            OperationLogInfo logInfo = contextService.collectOperationLogInfo(
                module, type, description, joinPoint, result, exception, executionTime,
                operationLog.recordParams(), operationLog.recordResult(),
                operationLog.ignoreParams(), operationLog.ignoreResult()
            );
            
            // 记录操作日志
            if (operationLog.async()) {
                // 异步记录

                // 获取token，用于异步获取上下文
                logInfo.setToken(StpUtil.getTokenValue());
                recordService.recordOperationLogAsync(logInfo);
            } else {
                // 同步记录
                recordService.recordOperationLogSync(logInfo);
            }

        } catch (Exception e) {
            log.error("记录操作日志处理失败", e);
        }
    }

    /**
     * 获取操作模块
     */
    private String getOperationModule(ProceedingJoinPoint joinPoint, OperationLog operationLog) {
        // 优先使用方法级别的自定义模块
        if (StringUtils.hasText(operationLog.module())) {
            return operationLog.module();
        }
        
        // 获取类级别的@OperationModule注解
        Class<?> targetClass = joinPoint.getTarget().getClass();
        OperationModule operationModule = targetClass.getAnnotation(OperationModule.class);
        if (operationModule != null) {
            return operationModule.value();
        }
        
        // 默认使用类名作为模块名
        return targetClass.getSimpleName().replace("Controller", "");
    }

    /**
     * 获取操作描述
     */
    private String getOperationDescription(OperationLog operationLog) {
        if (StringUtils.hasText(operationLog.description())) {
            return operationLog.description();
        }
        return operationLog.value();
    }
}
