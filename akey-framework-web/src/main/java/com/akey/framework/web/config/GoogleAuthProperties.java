package com.akey.framework.web.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Arrays;
import java.util.List;

/**
 * 谷歌验证码配置属性
 * 
 * <p>用于配置谷歌验证码拦截器的相关参数</p>
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@ConfigurationProperties(prefix = "app.google-auth")
public class GoogleAuthProperties {

    /**
     * 是否启用谷歌验证拦截
     */
    private boolean enabled = true;

    /**
     * 默认时间窗口
     */
    private int defaultTimeWindow = 1;

    /**
     * 验证码获取方式优先级
     * 
     * <p>支持的方式：header, param, body</p>
     */
    private List<String> codeSourcePriority = Arrays.asList("header", "param", "body");

    /**
     * 请求头名称
     */
    private String headerName = "X-Google-Auth-Code";

    /**
     * 请求参数名称
     */
    private String paramName = "googleAuthCode";

    /**
     * 是否启用审计日志
     */
    private boolean auditLogEnabled = true;

    /**
     * 验证失败时是否记录详细日志
     */
    private boolean logFailureDetails = true;

    /**
     * 安全级别常量
     */
    public static class SecurityLevel {
        /**
         * 高安全：30秒窗口，用于敏感操作
         */
        public static final int STRICT = 0;
        
        /**
         * 标准：90秒窗口，用于一般操作
         */
        public static final int NORMAL = 1;
        
        /**
         * 宽松：150秒窗口，用于查询操作
         */
        public static final int RELAXED = 2;
    }
}
