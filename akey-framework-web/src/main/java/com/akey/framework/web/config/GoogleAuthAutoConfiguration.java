package com.akey.framework.web.config;

import com.akey.common.service.GoogleAuthUserService;
import com.akey.framework.web.aspect.GoogleAuthAspect;
import com.akey.framework.web.service.DefaultGoogleAuthVerificationService;
import com.akey.framework.web.service.GoogleAuthVerificationService;
import com.akey.framework.web.util.GoogleAuthCodeExtractor;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 谷歌验证码自动配置类
 * 
 * <p>自动配置谷歌验证码拦截相关的组件</p>
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@Configuration
@ConditionalOnClass({GoogleAuthVerificationService.class, GoogleAuthUserService.class})
@ConditionalOnProperty(prefix = "app.google-auth", name = "enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties(GoogleAuthProperties.class)
public class GoogleAuthAutoConfiguration {

    /**
     * 配置谷歌验证码提取器
     */
    @Bean
    @ConditionalOnMissingBean
    public GoogleAuthCodeExtractor googleAuthCodeExtractor(ObjectMapper objectMapper) {
        log.info("配置谷歌验证码提取器");
        return new GoogleAuthCodeExtractor(objectMapper);
    }

    /**
     * 配置谷歌验证码验证服务
     */
    @Bean
    @ConditionalOnMissingBean
    public GoogleAuthVerificationService googleAuthVerificationService(GoogleAuthUserService googleAuthUserService) {
        log.info("配置谷歌验证码验证服务");
        return new DefaultGoogleAuthVerificationService(googleAuthUserService);
    }

    /**
     * 配置谷歌验证码AOP切面
     */
    @Bean
    @ConditionalOnMissingBean
    public GoogleAuthAspect googleAuthAspect(GoogleAuthVerificationService verificationService,
                                           GoogleAuthCodeExtractor codeExtractor,
                                           GoogleAuthProperties properties) {
        log.info("配置谷歌验证码AOP切面");
        return new GoogleAuthAspect(verificationService, codeExtractor, properties);
    }
}
