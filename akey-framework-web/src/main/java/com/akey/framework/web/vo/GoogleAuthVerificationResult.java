package com.akey.framework.web.vo;

import lombok.Data;

/**
 * 谷歌验证码验证结果
 * 
 * <p>封装谷歌验证码验证的结果信息</p>
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
public class GoogleAuthVerificationResult {

    /**
     * 验证是否成功
     */
    private boolean success;

    /**
     * 是否跳过验证
     * 
     * <p>当required=false且用户未设置谷歌验证器时为true</p>
     */
    private boolean skipped;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 私有构造函数
     */
    private GoogleAuthVerificationResult() {}

    /**
     * 创建验证成功的结果
     * 
     * @return 验证成功结果
     */
    public static GoogleAuthVerificationResult success() {
        GoogleAuthVerificationResult result = new GoogleAuthVerificationResult();
        result.success = true;
        result.skipped = false;
        return result;
    }

    /**
     * 创建跳过验证的结果
     * 
     * @return 跳过验证结果
     */
    public static GoogleAuthVerificationResult skipped() {
        GoogleAuthVerificationResult result = new GoogleAuthVerificationResult();
        result.success = true;
        result.skipped = true;
        return result;
    }

    /**
     * 创建验证失败的结果
     * 
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     * @return 验证失败结果
     */
    public static GoogleAuthVerificationResult failed(String errorCode, String errorMessage) {
        GoogleAuthVerificationResult result = new GoogleAuthVerificationResult();
        result.success = false;
        result.skipped = false;
        result.errorCode = errorCode;
        result.errorMessage = errorMessage;
        return result;
    }

    /**
     * 创建验证失败的结果
     * 
     * @param errorMessage 错误消息
     * @return 验证失败结果
     */
    public static GoogleAuthVerificationResult failed(String errorMessage) {
        return failed("GOOGLE_AUTH_VERIFICATION_FAILED", errorMessage);
    }

    /**
     * 判断是否验证通过（成功或跳过）
     * 
     * @return 验证通过返回true，失败返回false
     */
    public boolean isPassed() {
        return success;
    }

    /**
     * 判断是否需要抛出异常
     * 
     * @return 需要抛出异常返回true，否则返回false
     */
    public boolean shouldThrowException() {
        return !success;
    }
}
