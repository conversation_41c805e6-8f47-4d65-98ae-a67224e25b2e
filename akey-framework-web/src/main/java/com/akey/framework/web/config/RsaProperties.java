package com.akey.framework.web.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.util.StringUtils;

/**
 * RSA加密配置属性类
 * 
 * <p>功能说明：</p>
 * <ul>
 *   <li>管理RSA加密相关的配置属性</li>
 *   <li>支持请求解密和响应加密的密钥配置</li>
 *   <li>提供配置验证功能</li>
 *   <li>支持环境特定的开关控制</li>
 * </ul>
 * <AUTHOR>
 * @since 2025-07-05
 */
@Data
@ConfigurationProperties(prefix = "akey.security.rsa")
public class RsaProperties {

    /**
     * 是否启用RSA加密功能
     * 默认为false，需要在配置文件中显式启用
     */
    private Boolean enabled = false;

    /**
     * 请求解密配置
     */
    private Request request = new Request();

    /**
     * 响应加密配置
     */
    private Response response = new Response();

    /**
     * 请求解密配置
     */
    @Data
    public static class Request {

        /**
         * 是否启用请求解密功能
         */
        private Boolean enabled = true;

        /**
         * 请求解密私钥
         * 用于解密客户端发送的加密请求数据
         */
        private String privateKey;
    }

    /**
     * 响应加密配置
     */
    @Data
    public static class Response {

        /**
         * 是否启用响应加密功能
         */
        private Boolean enabled = true;

        /**
         * 响应加密公钥
         * 用于加密返回给客户端的响应数据
         */
        private String publicKey;
    }

    /**
     * 验证RSA配置是否有效
     * 
     * @return 配置有效返回true，否则返回false
     */
    public boolean isValidConfig() {
        if (!enabled) {
            return true; // 未启用时认为配置有效
        }

        // 启用时需要验证密钥配置
        boolean hasPrivateKey = StringUtils.hasText(request.getPrivateKey());
        boolean hasPublicKey = StringUtils.hasText(response.getPublicKey());

        return hasPrivateKey && hasPublicKey;
    }

    /**
     * 获取私钥
     *
     * @return 私钥字符串
     */
    public String getFormattedPrivateKey() {
        return request.getPrivateKey();
    }

    /**
     * 获取公钥
     *
     * @return 公钥字符串
     */
    public String getFormattedPublicKey() {
        return response.getPublicKey();
    }

    /**
     * 检查是否启用了RSA功能
     *
     * @return 启用返回true，否则返回false
     */
    public Boolean getEnabled() {
        return enabled != null ? enabled : false;
    }

    /**
     * 检查是否启用了请求解密功能
     *
     * @return 启用返回true，否则返回false
     */
    public boolean isRequestDecryptionEnabled() {
        return getEnabled() && (request.getEnabled() != null ? request.getEnabled() : true);
    }

    /**
     * 检查是否启用了响应加密功能
     *
     * @return 启用返回true，否则返回false
     */
    public boolean isResponseEncryptionEnabled() {
        return getEnabled() && (response.getEnabled() != null ? response.getEnabled() : true);
    }
}
