package com.akey.framework.web.vo;

import lombok.Data;

/**
 * 内存信息VO
 * 
 * <p>封装系统内存和JVM内存相关信息</p>
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
public class MemoryInfoVO {

    /**
     * 系统内存信息
     */
    private SystemMemoryVO systemMemory;

    /**
     * JVM内存信息
     */
    private JvmMemoryVO jvmMemory;

    /**
     * 交换内存信息
     */
    private SwapMemoryVO swapMemory;

    /**
     * 系统内存详细信息
     */
    @Data
    public static class SystemMemoryVO {
        
        /**
         * 总内存（字节）
         */
        private Long totalBytes;

        /**
         * 已用内存（字节）
         */
        private Long usedBytes;

        /**
         * 可用内存（字节）
         */
        private Long freeBytes;

        /**
         * 实际可用内存（字节）
         * 包括缓存和缓冲区可释放的内存
         */
        private Long actualFreeBytes;

        /**
         * 实际已用内存（字节）
         */
        private Long actualUsedBytes;

        /**
         * 内存使用率（%）
         */
        private Double usagePercent;

        /**
         * 总内存（格式化字符串）
         */
        private String totalFormatted;

        /**
         * 已用内存（格式化字符串）
         */
        private String usedFormatted;

        /**
         * 可用内存（格式化字符串）
         */
        private String freeFormatted;
    }

    /**
     * 交换内存详细信息
     */
    @Data
    public static class SwapMemoryVO {

        /**
         * 总交换内存（字节）
         */
        private Long totalBytes;

        /**
         * 已用交换内存（字节）
         */
        private Long usedBytes;

        /**
         * 可用交换内存（字节）
         */
        private Long freeBytes;

        /**
         * 交换内存使用率（%）
         */
        private Double usagePercent;

        /**
         * 总交换内存（格式化字符串）
         */
        private String totalFormatted;

        /**
         * 已用交换内存（格式化字符串）
         */
        private String usedFormatted;

        /**
         * 可用交换内存（格式化字符串）
         */
        private String freeFormatted;
    }

    /**
     * JVM内存详细信息
     */
    @Data
    public static class JvmMemoryVO {
        
        /**
         * 堆内存信息
         */
        private HeapMemoryVO heapMemory;

        /**
         * 非堆内存信息
         */
        private NonHeapMemoryVO nonHeapMemory;

        /**
         * 垃圾回收信息
         */
        private GcInfoVO gcInfo;

        /**
         * 堆内存信息
         */
        @Data
        public static class HeapMemoryVO {
            
            /**
             * 最大堆内存（字节）
             */
            private Long maxBytes;

            /**
             * 已分配堆内存（字节）
             */
            private Long committedBytes;

            /**
             * 已使用堆内存（字节）
             */
            private Long usedBytes;

            /**
             * 可用堆内存（字节）
             */
            private Long freeBytes;

            /**
             * 堆内存使用率（%）
             */
            private Double usagePercent;

            /**
             * 最大堆内存（格式化字符串）
             */
            private String maxFormatted;

            /**
             * 已使用堆内存（格式化字符串）
             */
            private String usedFormatted;

            /**
             * 可用堆内存（格式化字符串）
             */
            private String freeFormatted;
        }

        /**
         * 非堆内存信息
         */
        @Data
        public static class NonHeapMemoryVO {
            
            /**
             * 最大非堆内存（字节）
             */
            private Long maxBytes;

            /**
             * 已分配非堆内存（字节）
             */
            private Long committedBytes;

            /**
             * 已使用非堆内存（字节）
             */
            private Long usedBytes;

            /**
             * 非堆内存使用率（%）
             */
            private Double usagePercent;

            /**
             * 最大非堆内存（格式化字符串）
             */
            private String maxFormatted;

            /**
             * 已使用非堆内存（格式化字符串）
             */
            private String usedFormatted;
        }

        /**
         * 垃圾回收信息
         */
        @Data
        public static class GcInfoVO {
            
            /**
             * 年轻代GC次数
             */
            private Long youngGcCount;

            /**
             * 年轻代GC总时间（毫秒）
             */
            private Long youngGcTime;

            /**
             * 老年代GC次数
             */
            private Long oldGcCount;

            /**
             * 老年代GC总时间（毫秒）
             */
            private Long oldGcTime;

            /**
             * 总GC次数
             */
            private Long totalGcCount;

            /**
             * 总GC时间（毫秒）
             */
            private Long totalGcTime;
        }
    }
}
