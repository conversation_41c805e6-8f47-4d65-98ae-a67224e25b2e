package com.akey.framework.web.converter;

import com.akey.common.enums.BaseEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.converter.Converter;
import org.springframework.core.convert.converter.ConverterFactory;
import org.springframework.util.StringUtils;

/**
 * BaseEnum 转换器工厂
 * 
 * <p>用于将字符串转换为实现了 BaseEnum 接口的枚举类型</p>
 * <p>支持 HTTP 请求参数的自动转换，解决前端传递枚举值时的类型转换问题</p>
 * <p>使用泛型确保类型安全，支持所有实现 BaseEnum 接口的枚举类</p>
 * 
 * <AUTHOR>
 * @since 2025-07-09
 */
@Slf4j
public class BaseEnumConverterFactory implements ConverterFactory<String, BaseEnum<?>> {

    /**
     * 获取指定枚举类型的转换器
     * 
     * @param targetType 目标枚举类型
     * @param <T> 枚举类型，必须实现 BaseEnum 接口
     * @return 对应的转换器实例
     */
    @Override
    @SuppressWarnings("unchecked")
    public <T extends BaseEnum<?>> Converter<String, T> getConverter(Class<T> targetType) {
        log.debug("创建枚举转换器, 目标类型: {}", targetType.getSimpleName());
        return new StringToBaseEnumConverter<>(targetType);
    }

    /**
     * 字符串到 BaseEnum 的转换器
     * 
     * @param <T> 枚举类型，必须实现 BaseEnum 接口
     */
    private static class StringToBaseEnumConverter<T extends BaseEnum<?>> implements Converter<String, T> {
        
        private final Class<T> enumType;

        /**
         * 构造函数
         * 
         * @param enumType 枚举类型
         */
        public StringToBaseEnumConverter(Class<T> enumType) {
            this.enumType = enumType;
        }

        /**
         * 执行字符串到枚举的转换
         * 
         * @param source 源字符串值
         * @return 转换后的枚举实例，如果转换失败则返回 null
         */
        @Override
        public T convert(String source) {
            if (!StringUtils.hasText(source)) {
                log.debug("转换源字符串为空, 返回 null");
                return null;
            }

            try {
                log.debug("开始转换字符串到枚举, 源值: {}, 目标类型: {}", source, enumType.getSimpleName());
                
                // 使用 BaseEnum 接口提供的 fromString 方法进行转换
                T result = BaseEnum.fromString(enumType, source.trim());
                
                if (result != null) {
                    log.debug("枚举转换成功, 源值: {} -> 目标枚举: {}", source, result);
                } else {
                    log.warn("枚举转换失败, 无效的枚举值: {}, 目标类型: {}", source, enumType.getSimpleName());
                }
                
                return result;
                
            } catch (Exception e) {
                log.error("枚举转换异常, 源值: {}, 目标类型: {}, 错误: {}", 
                         source, enumType.getSimpleName(), e.getMessage(), e);
                return null;
            }
        }
    }
}
