<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <!-- 父项目配- 继承Spring Boot父项目获得版本管理和插件配置 -->
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.5.3</version>
        <relativePath/>
    </parent>
    
    <!-- 聚合项目基本信息 - packaging设为pom表示这是一个聚合项目，不产生jar-->
    <groupId>com.akey</groupId>
    <artifactId>akey</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>akey</name>
    <description>akey多模块聚合项目 - 统一管理子模块的构建和依赖</description>

    <!-- 子模块列表 - 定义项目包含的所有子模块，构建时会按依赖关系自动确定构建顺序 -->
    <modules>
        <!-- 公共模块 - 提供通用工具类、常量、异常定义等 -->
        <module>akey-common</module>
        <!-- Framework-core框架核心模块 - 对MybatisPlus、Redis等进行配置 -->
        <module>akey-framework-core</module>
        <!-- Framework-web 框架核心模块 - 对全局异常、Web等进行配置，依赖framework-core -->
        <module>akey-framework-web</module>
        <!-- 核心业务与数据模块，包含系统的核心数据模型、数据访问层（DAO/Repository）和业务逻辑实现（Service），是应用程序的核心功能载体。 -->
        <module>akey-core</module>
        <!-- Web应用模块 - 控制器、启动类、Web配置-->
        <module>akey-web</module>
    </modules>
    
    <!-- 全局属性配置 - 定义在所有子模块中都可以使用的属性-->
    <properties>
        <java.version>17</java.version>
        <!-- fastjson2 -->
        <fastjson2.version>2.0.57</fastjson2.version>
        <!-- lombok -->
        <lombok.version>1.18.30</lombok.version>
        <!-- 数据库相关版本 -->
        <druid.version>1.2.25</druid.version>
        <!-- mybatis-plus -->
        <mybatis-plus.version>3.5.7</mybatis-plus.version>
        <!--   Mysql驱动版本     -->
        <mysql.version>8.2.0</mysql.version>
        <!--   protobuf版本-覆盖Mysql自带的风险版本     -->
        <protobuf.version>4.28.2</protobuf.version>
        <!-- Sa-Token版本 -->
        <sa-token.version>1.43.0</sa-token.version>
        <!-- BCrypt密码加密版本 -->
        <bcrypt.version>0.10.2</bcrypt.version>
        <!-- OSHI 系统资源监控库版本 -->
        <oshi.version>6.6.5</oshi.version>

        <!-- 默认激活开发环境 -->
        <profiles.active>dev</profiles.active>
        <!-- 项目编码 -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    </properties>

    <!-- 依赖版本管理 - 统一管理所有子模块的依赖版本，确保版本一致性和兼容性 -->
    <dependencyManagement>
        <dependencies>
            <!-- Lombok - 用于简化实体类代码，减少样板代码 -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <!-- MySQL 驱动 -->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf.version}</version>
            </dependency>

            <!-- Druid数据库连接池 - 阿里巴巴开源的数据库连接池，提供监控功能 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-3-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            
            <!-- MyBatis-Plus - MyBatis的增强工具，简化CRUD操作 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <!-- MyBatis-Plus 注解依赖 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-annotation</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!-- 阿里JSON解析器 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>

            <!-- Sa-Token 权限认证，在线文档：https://sa-token.cc -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot3-starter</artifactId>
                <version>${sa-token.version}</version>
            </dependency>


            <!-- Sa-Token 整合 RedisTemplate -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-redis-template</artifactId>
                <version>${sa-token.version}</version>
            </dependency>

            <!-- Sa-Token 整合 Redis （使用 Fastjson2 序列化方式） -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-fastjson2</artifactId>
                <version>${sa-token.version}</version>
            </dependency>

            <!-- Sa-Token 整合 SpringAOP 实现注解鉴权 -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-aop</artifactId>
                <version>${sa-token.version}</version>
            </dependency>

            <!-- BCrypt密码加密库 -->
            <dependency>
                <groupId>at.favre.lib</groupId>
                <artifactId>bcrypt</artifactId>
                <version>${bcrypt.version}</version>
            </dependency>

            <!-- OSHI 系统资源监控库 - 用于获取系统CPU、内存、磁盘等信息 -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

     <!-- 构建配置 - 统一管理构建插件的版本和配置，确保所有子模块使用相同的插件版本和配置 -->
    <build>
        <pluginManagement>
            <plugins>
                <!-- Maven编译插件 - 配置注解处理器路径-->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <configuration>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.springframework.boot</groupId>
                                <artifactId>spring-boot-configuration-processor</artifactId>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>

                <!-- Spring Boot Maven插件 - 用于打包可执行jar -->
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <configuration>
                        <excludes>
                            <exclude>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                            </exclude>
                        </excludes>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <!-- 多环境配置 - 支持dev、test、prod三个环境 -->
    <profiles>
        <!-- 开发环境 -->
        <profile>
            <id>dev</id>
            <properties>
                <profiles.active>dev</profiles.active>
            </properties>
            <!-- 默认激活开发环境 -->
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>

        <!-- 测试环境 -->
        <profile>
            <id>test</id>
            <properties>
                <profiles.active>test</profiles.active>
            </properties>
        </profile>

        <!-- 生产环境 -->
        <profile>
            <id>prod</id>
            <properties>
                <profiles.active>prod</profiles.active>
            </properties>
        </profile>
    </profiles>

</project> 
