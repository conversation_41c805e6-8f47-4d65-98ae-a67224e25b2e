<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <!-- 继承父项目配- 继承Spring Boot父项目获得版本管理和插件配置 -->
    <parent>
        <groupId>com.akey</groupId>
        <artifactId>akey</artifactId>
        <version>0.0.1-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    
    <!-- Web应用模块 - 应用启动入口和Web接口 -->
    <artifactId>akey-web</artifactId>
    <name>akey-web</name>
    <description>Web应用模块 - 控制器、启动类、Web配置</description>
    
    <!-- 依赖配置 -->
    <dependencies>

        <!-- 核心业务模块依赖 - 包含实体类、Service、Mapper等 -->
        <dependency>
            <groupId>com.akey</groupId>
            <artifactId>akey-core</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <!-- Web框架模块依赖 - 包含Web配置、异常处理等，间接依赖framework-core -->
        <dependency>
            <groupId>com.akey</groupId>
            <artifactId>akey-framework-web</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- 测试启动器 - 提供测试支持，包括JUnit、Mockito等 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

    </dependencies>
    
    <!-- 构建配置 -->
    <build>
        <!-- 资源过滤配置 - 支持Maven变量替换 -->
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.yml</include>
                    <include>**/*.yaml</include>
                    <include>**/*.properties</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <excludes>
                    <exclude>**/*.yml</exclude>
                    <exclude>**/*.yaml</exclude>
                    <exclude>**/*.properties</exclude>
                </excludes>
            </resource>
        </resources>
        
        <plugins>
            <!-- Maven资源插件 - 处理资源文件过滤 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <useDefaultDelimiters>false</useDefaultDelimiters>
                    <delimiters>
                        <delimiter>@</delimiter>
                    </delimiters>
                </configuration>
            </plugin>
            
            <!-- Spring Boot Maven插件 - 用于打包可执行jar -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project> 
