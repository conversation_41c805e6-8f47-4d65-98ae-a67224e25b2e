package com.akey.web;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Spring Boot应用启动类
 * 
 * <p>配置说明：</p>
 * <ul>
 *   <li>scanBasePackages - 配置组件扫描路径以支持多模块架构</li>
 *   <li>MapperScan - 配置MyBatis Mapper接口扫描路径</li>
 * </ul>
 */
@SpringBootApplication(scanBasePackages = "com.akey")
@MapperScan("com.akey.core.dao.mapper")
@EnableScheduling
public class AkeyApplication {

    public static void main(String[] args) {
        SpringApplication.run(AkeyApplication.class, args);
    }

}
