package com.akey.web.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.akey.common.Result;

import com.akey.common.annotation.OperationModule;
import com.akey.common.constant.SystemConstant;
import com.akey.common.enums.OperationType;
import com.akey.core.dao.entity.OperationLog;
import com.akey.core.dao.service.OperationLogService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 操作日志控制器
 *
 * <p>提供操作日志的查询、统计和管理功能</p>
 * <p>包括日志分页查询、统计分析、清理管理等</p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@RestController
@RequestMapping("/system/operation-log")
@RequiredArgsConstructor
@Validated
@OperationModule("操作日志管理")
public class OperationLogController {

    private final OperationLogService operationLogService;

    /**
     * 分页查询操作日志
     *
     * @param current 当前页码
     * @param size 每页大小
     * @param userId 用户ID（可选）
     * @param username 用户名（可选）
     * @param operationModule 操作模块（可选）
     * @param operationType 操作类型（可选）
     * @param operationStatus 操作状态（可选，0-失败，1-成功）
     * @param requestMethod 请求方法（可选，如：GET、POST、PUT、DELETE）
     * @param clientIp 客户端IP（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 分页结果
     */
    @GetMapping("/page")
    @SaCheckPermission(value = "operation:log:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    public Result<IPage<OperationLog>> getOperationLogPage(
            @RequestParam(defaultValue = "1") @Min(1) Long current,
            @RequestParam(defaultValue = "10") @Min(1) Long size,
            @RequestParam(required = false) String userId,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String operationModule,
            @RequestParam(required = false) String operationType,
            @RequestParam(required = false) Integer operationStatus,
            @RequestParam(required = false) String requestMethod,
            @RequestParam(required = false) String clientIp,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {

        log.debug("分页查询操作日志, current: {}, size: {}, userId: {}, username: {}, operationModule: {}, operationType: {}, operationStatus: {}, requestMethod: {}, clientIp: {}, startTime: {}, endTime: {}",
                 current, size, userId, username, operationModule, operationType, operationStatus, requestMethod, clientIp, startTime, endTime);

        try {
            Page<OperationLog> page = new Page<>(current, size);
            IPage<OperationLog> result = operationLogService.getOperationLogPage(page, userId, username, operationModule, operationType, operationStatus, requestMethod, clientIp, startTime, endTime);

            log.debug("分页查询操作日志成功, 总记录数: {}, 当前页记录数: {}", result.getTotal(), result.getRecords().size());
            return Result.success("查询成功", result);

        } catch (Exception e) {
            log.error("分页查询操作日志失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取操作日志详情
     *
     * @param id 日志ID
     * @return 日志详情
     */
    @GetMapping("/{id}")
    @SaCheckPermission(value = "operation:log:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @com.akey.common.annotation.OperationLog(value = "查看操作日志详情", type = OperationType.QUERY)
    public Result<OperationLog> getOperationLogById(@PathVariable @NotNull String id) {

        log.debug("获取操作日志详情, id: {}", id);

        try {
            OperationLog result = operationLogService.getById(id);

            if (result == null) {
                log.warn("操作日志不存在, id: {}", id);
                return Result.error("操作日志不存在");
            }

            log.debug("获取操作日志详情成功, id: {}", id);
            return Result.success("查询成功", result);

        } catch (Exception e) {
            log.error("获取操作日志详情失败, id: {}", id, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户操作历史记录
     *
     * @param userId 用户ID
     * @param limit 限制数量（默认10条）
     * @return 操作记录列表
     */
    @GetMapping("/history/{userId}")
    @SaCheckPermission(value = "operation:log:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @com.akey.common.annotation.OperationLog(value = "查看用户操作历史", type = OperationType.QUERY)
    public Result<List<OperationLog>> getUserOperationHistory(
            @PathVariable @NotNull String userId,
            @RequestParam(defaultValue = "10") @Min(1) Integer limit) {

        log.debug("获取用户操作历史记录, userId: {}, limit: {}", userId, limit);

        try {
            List<OperationLog> result = operationLogService.getUserOperationHistory(userId, limit);

            log.debug("获取用户操作历史记录成功, userId: {}, 记录数: {}", userId, result.size());
            return Result.success("查询成功", result);

        } catch (Exception e) {
            log.error("获取用户操作历史记录失败, userId: {}", userId, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取操作日志统计信息
     *
     * @param startTime 开始时间（可选，默认7天前）
     * @param endTime 结束时间（可选，默认当前时间）
     * @return 统计信息
     */
    @GetMapping("/statistics")
    @SaCheckPermission(value = "operation:log:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @com.akey.common.annotation.OperationLog(value = "查看操作日志统计", type = OperationType.QUERY)
    public Result<Map<String, Object>> getOperationStatistics(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {

        // 设置默认时间范围（最近7天）
        if (startTime == null) {
            startTime = LocalDateTime.now().minusDays(7);
        }
        if (endTime == null) {
            endTime = LocalDateTime.now();
        }

        log.debug("获取操作日志统计信息, startTime: {}, endTime: {}", startTime, endTime);

        try {
            Map<String, Object> result = operationLogService.getOperationStatistics(startTime, endTime);

            log.debug("获取操作日志统计信息成功");
            return Result.success("查询成功", result);

        } catch (Exception e) {
            log.error("获取操作日志统计信息失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 按模块统计操作数量
     *
     * @param startTime 开始时间（可选，默认7天前）
     * @param endTime 结束时间（可选，默认当前时间）
     * @return 模块统计结果
     */
    @GetMapping("/statistics/module")
    @SaCheckPermission(value = "operation:log:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @com.akey.common.annotation.OperationLog(value = "查看模块操作统计", type = OperationType.QUERY)
    public Result<Map<String, Long>> getModuleStatistics(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {

        // 设置默认时间范围（最近7天）
        if (startTime == null) {
            startTime = LocalDateTime.now().minusDays(7);
        }
        if (endTime == null) {
            endTime = LocalDateTime.now();
        }

        log.debug("获取模块操作统计, startTime: {}, endTime: {}", startTime, endTime);

        try {
            Map<String, Long> result = operationLogService.countByOperationModule(startTime, endTime);

            log.debug("获取模块操作统计成功");
            return Result.success("查询成功", result);

        } catch (Exception e) {
            log.error("获取模块操作统计失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 按操作类型统计操作数量
     *
     * @param startTime 开始时间（可选，默认7天前）
     * @param endTime 结束时间（可选，默认当前时间）
     * @return 操作类型统计结果
     */
    @GetMapping("/statistics/type")
    @SaCheckPermission(value = "operation:log:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @com.akey.common.annotation.OperationLog(value = "查看操作类型统计", type = OperationType.QUERY)
    public Result<Map<String, Long>> getTypeStatistics(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {

        // 设置默认时间范围（最近7天）
        if (startTime == null) {
            startTime = LocalDateTime.now().minusDays(7);
        }
        if (endTime == null) {
            endTime = LocalDateTime.now();
        }

        log.debug("获取操作类型统计, startTime: {}, endTime: {}", startTime, endTime);

        try {
            Map<String, Long> result = operationLogService.countByOperationType(startTime, endTime);

            log.debug("获取操作类型统计成功");
            return Result.success("查询成功", result);

        } catch (Exception e) {
            log.error("获取操作类型统计失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
    /**
     * 按用户统计操作数量
     *
     * @param startTime 开始时间（可选，默认7天前）
     * @param endTime 结束时间（可选，默认当前时间）
     * @param limit 限制数量（默认10）
     * @return 用户统计结果
     */
    @GetMapping("/statistics/user")
    @SaCheckPermission(value = "operation:log:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @com.akey.common.annotation.OperationLog(value = "查看用户操作统计", type = OperationType.QUERY)
    public Result<Map<String, Long>> getUserStatistics(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @RequestParam(defaultValue = "10") @Min(1) Integer limit) {

        // 设置默认时间范围（最近7天）
        if (startTime == null) {
            startTime = LocalDateTime.now().minusDays(7);
        }
        if (endTime == null) {
            endTime = LocalDateTime.now();
        }

        log.debug("获取用户操作统计, startTime: {}, endTime: {}, limit: {}", startTime, endTime, limit);

        try {
            Map<String, Long> result = operationLogService.countByUser(startTime, endTime, limit);

            log.debug("获取用户操作统计成功");
            return Result.success("查询成功", result);

        } catch (Exception e) {
            log.error("获取用户操作统计失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取失败操作记录
     *
     * @param current 当前页码
     * @param size 每页大小
     * @param startTime 开始时间（可选，默认7天前）
     * @param endTime 结束时间（可选，默认当前时间）
     * @return 失败操作记录分页结果
     */
    @GetMapping("/failures")
    @SaCheckPermission(value = "operation:log:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @com.akey.common.annotation.OperationLog(value = "查看失败操作记录", type = OperationType.QUERY)
    public Result<IPage<OperationLog>> getFailedOperations(
            @RequestParam(defaultValue = "1") @Min(1) Long current,
            @RequestParam(defaultValue = "10") @Min(1) Long size,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {

        // 设置默认时间范围（最近7天）
        if (startTime == null) {
            startTime = LocalDateTime.now().minusDays(7);
        }
        if (endTime == null) {
            endTime = LocalDateTime.now();
        }

        log.debug("获取失败操作记录, current: {}, size: {}, startTime: {}, endTime: {}", current, size, startTime, endTime);

        try {
            Page<OperationLog> page = new Page<>(current, size);
            IPage<OperationLog> result = operationLogService.getFailedOperations(page, startTime, endTime);

            log.debug("获取失败操作记录成功, 总记录数: {}, 当前页记录数: {}", result.getTotal(), result.getRecords().size());
            return Result.success("查询成功", result);

        } catch (Exception e) {
            log.error("获取失败操作记录失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据IP地址查询操作记录
     *
     * @param clientIp 客户端IP
     * @param limit 限制数量（默认100）
     * @return 操作记录列表
     */
    @GetMapping("/ip/{clientIp}")
    @SaCheckPermission(value = "operation:log:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @com.akey.common.annotation.OperationLog(value = "查看IP操作记录", type = OperationType.QUERY)
    public Result<List<OperationLog>> getOperationsByIp(
            @PathVariable @NotNull String clientIp,
            @RequestParam(defaultValue = "100") @Min(1) Integer limit) {

        log.debug("根据IP查询操作记录, clientIp: {}, limit: {}", clientIp, limit);

        try {
            List<OperationLog> result = operationLogService.getOperationsByIp(clientIp, limit);

            log.debug("根据IP查询操作记录成功, clientIp: {}, 记录数: {}", clientIp, result.size());
            return Result.success("查询成功", result);

        } catch (Exception e) {
            log.error("根据IP查询操作记录失败, clientIp: {}", clientIp, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 清理过期的操作日志
     *
     * @param days 清理多少天前的记录（默认30天）
     * @return 清理的记录数
     */
    @DeleteMapping("/cleanup")
    @SaCheckPermission(value = "operation:log:delete", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @com.akey.common.annotation.OperationLog(value = "清理过期操作日志", type = OperationType.DELETE)
    public Result<Long> cleanupExpiredLogs(@RequestParam(defaultValue = "30") @Min(1) Integer days) {

        log.warn("开始物理删除过期操作日志, days: {}", days);

        try {
            // 计算删除的截止时间：days天前的23:59:59
            // 例如：今天是31号，days=1，则删除30号23:59:59之前的所有记录
            LocalDateTime beforeTime = LocalDateTime.now()
                    .minusDays(days)
                    .withHour(23)
                    .withMinute(59)
                    .withSecond(59)
                    .withNano(999999999);

            String deleteDate = beforeTime.toLocalDate().toString();
            log.warn("删除时间点: {}, 将删除{}及之前的所有操作日志（{}天前）",
                    beforeTime, deleteDate, days);

            Long deletedCount = operationLogService.cleanupExpiredLogs(beforeTime);

            log.warn("物理删除过期操作日志完成, 删除记录数: {}", deletedCount);
            return Result.success("清理成功", deletedCount);

        } catch (Exception e) {
            log.error("物理删除过期操作日志失败", e);
            return Result.error("清理失败：" + e.getMessage());
        }
    }

    /**
     * 清空所有操作日志（物理删除）
     *
     * @return 清空的记录数
     */
    @DeleteMapping("/clear")
    @SaCheckPermission(value = "operation:log:delete", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @com.akey.common.annotation.OperationLog(value = "物理清空所有操作日志", type = OperationType.DELETE)
    public Result<Long> clearAllLogs() {

        log.warn("开始物理清空所有操作日志");

        try {
            // 物理删除所有记录
            Long deletedCount = operationLogService.physicalClearAllLogs();

            log.warn("物理清空所有操作日志完成, 删除记录数: {}", deletedCount);
            return Result.success("清空成功", deletedCount);

        } catch (Exception e) {
            log.error("物理清空所有操作日志失败", e);
            return Result.error("清空失败：" + e.getMessage());
        }
    }
}