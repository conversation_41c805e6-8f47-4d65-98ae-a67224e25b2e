package com.akey.web.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.akey.common.Result;
import com.akey.common.annotation.OperationLog;
import com.akey.common.annotation.OperationModule;
import com.akey.common.constant.SystemConstant;
import com.akey.common.enums.OperationType;
import com.akey.core.dto.BatchKickoutDTO;
import com.akey.core.dto.OnlineUserQueryDTO;
import com.akey.core.service.OnlineUserService;
import com.akey.core.vo.OnlineUserPageVO;
import com.akey.core.vo.UserTerminalVO;
import com.akey.framework.web.annotation.GoogleAuthRequired;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 在线用户管理控制器
 * 
 * <p>提供在线用户查询、管理、踢出等功能的REST API接口</p>
 * <p>基于Sa-Token框架实现用户会话管理</p>
 * <p>支持多设备登录管理和批量操作</p>
 * <p>所有敏感操作都需要相应的权限验证</p>
 * 
 * <AUTHOR>
 * @since 2025-08-02
 */
@Slf4j
@RestController
@RequestMapping("/online-user")
@RequiredArgsConstructor
@OperationModule("在线用户管理")
public class OnlineUserController {

    private final OnlineUserService onlineUserService;

    /**
     * 获取在线用户列表
     * 
     * <p>分页查询当前系统中的在线用户</p>
     * <p>支持关键词搜索、设备类型筛选等功能</p>
     * <p>返回用户基本信息、登录状态、设备信息等</p>
     * 
     * @param queryDTO 查询条件DTO
     * @return 在线用户分页列表
     */
    @OperationLog(value = "查询在线用户列表", type = OperationType.QUERY)
    @SaCheckPermission(value = "online-user:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @GetMapping("/list")
    public Result<OnlineUserPageVO> getOnlineUsers(@Valid OnlineUserQueryDTO queryDTO) {
        try {
            log.info("查询在线用户列表, queryDTO: {}", queryDTO);
            
            OnlineUserPageVO pageResult = onlineUserService.getOnlineUsers(queryDTO);
            
            log.info("查询在线用户列表成功, 总数: {}, 当前页数量: {}", 
                    pageResult.getTotal(), pageResult.getCurrentPageSize());
            
            return Result.success("查询在线用户列表成功", pageResult);
            
        } catch (Exception e) {
            log.error("查询在线用户列表失败, queryDTO: {}, error: {}", queryDTO, e.getMessage(), e);
            return Result.error("查询在线用户列表失败");
        }
    }



    /**
     * 强制注销用户
     * 
     * <p>强制指定用户下线，完全清除Token信息</p>
     * <p>用户后续访问将提示"Token无效"</p>
     * <p>支持指定设备类型进行精确注销</p>
     * 
     * @param loginId 用户登录ID
     * @param deviceType 设备类型（可选）
     * @return 操作结果
     */
    @OperationLog(value = "强制注销用户", type = OperationType.UPDATE)
    @SaCheckPermission(value = "online-user:manage", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @PostMapping("/logout")
    public Result<Void> forceLogout(
            @RequestParam @NotBlank(message = "用户登录ID不能为空") String loginId,
            @RequestParam(required = false) String deviceType) {
        try {
            log.info("强制注销用户, loginId: {}, deviceType: {}", loginId, deviceType);
            
            boolean success = onlineUserService.forceLogout(loginId, deviceType);
            
            if (success) {
                String message = StringUtils.hasText(deviceType) ? 
                        String.format("强制注销用户成功 (设备类型: %s)", deviceType) : "强制注销用户成功";
                log.info("强制注销用户成功, loginId: {}, deviceType: {}", loginId, deviceType);
                return Result.success(message);
            } else {
                log.warn("强制注销用户失败, loginId: {}, deviceType: {}", loginId, deviceType);
                return Result.error("强制注销用户失败");
            }
            
        } catch (Exception e) {
            log.error("强制注销用户失败, loginId: {}, deviceType: {}, error: {}", 
                    loginId, deviceType, e.getMessage(), e);
            return Result.error("强制注销用户失败");
        }
    }

    /**
     * 踢出用户
     * 
     * <p>将指定用户踢下线，不清除Token信息</p>
     * <p>Token被标记为"已被踢下线"状态</p>
     * <p>用户后续访问将提示"Token已被踢下线"</p>
     * 
     * @param loginId 用户登录ID
     * @param deviceType 设备类型（可选）
     * @return 操作结果
     */
    @OperationLog(value = "踢出用户", type = OperationType.UPDATE)
    @SaCheckPermission(value = "online-user:manage", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @PostMapping("/kickout")
    public Result<Void> kickoutUser(
            @RequestParam @NotBlank(message = "用户登录ID不能为空") String loginId,
            @RequestParam(required = false) String deviceType) {
        try {
            log.info("踢出用户, loginId: {}, deviceType: {}", loginId, deviceType);
            
            boolean success = onlineUserService.kickoutUser(loginId, deviceType);
            
            if (success) {
                String message = StringUtils.hasText(deviceType) ? 
                        String.format("踢出用户成功 (设备类型: %s)", deviceType) : "踢出用户成功";
                log.info("踢出用户成功, loginId: {}, deviceType: {}", loginId, deviceType);
                return Result.success(message);
            } else {
                log.warn("踢出用户失败, loginId: {}, deviceType: {}", loginId, deviceType);
                return Result.error("踢出用户失败");
            }
            
        } catch (Exception e) {
            log.error("踢出用户失败, loginId: {}, deviceType: {}, error: {}", 
                    loginId, deviceType, e.getMessage(), e);
            return Result.error("踢出用户失败");
        }
    }

    /**
     * 批量踢出用户
     * 
     * <p>根据用户ID列表批量执行踢出操作</p>
     * <p>支持强制注销和踢人下线两种操作类型</p>
     * <p>支持按设备类型进行批量操作</p>
     * <p>敏感操作，需要Google验证码</p>
     * 
     * @param batchDTO 批量操作参数DTO
     * @return 操作结果，包含成功踢出的用户数量
     */
    @OperationLog(value = "批量踢出用户", type = OperationType.UPDATE)
    @SaCheckPermission(value = "online-user:manage", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @GoogleAuthRequired(message = "批量踢出用户需要Google验证码")
    @PostMapping("/batch-kickout")
    public Result<Integer> batchKickout(@RequestBody @Valid BatchKickoutDTO batchDTO) {
        try {
            log.info("批量踢出用户, batchDTO: {}", batchDTO);
            
            int successCount = onlineUserService.batchKickout(batchDTO);
            
            String message = String.format("批量踢出用户完成，成功踢出 %d 个用户", successCount);
            log.info("批量踢出用户成功, 总数: {}, 成功: {}", batchDTO.getLoginIds().size(), successCount);
            
            return Result.success(message, successCount);
            
        } catch (Exception e) {
            log.error("批量踢出用户失败, batchDTO: {}, error: {}", batchDTO, e.getMessage(), e);
            return Result.error("批量踢出用户失败");
        }
    }

    /**
     * 根据Token的MD5哈希值踢出用户
     *
     * <p>根据Token的MD5哈希值匹配并踢出对应的用户会话</p>
     * <p>安全且精确的踢出方式，避免Token泄露</p>
     *
     * @param tokenMd5 Token的MD5哈希值
     * @return 操作结果
     */
    @OperationLog(value = "根据Token MD5踢出用户", type = OperationType.UPDATE)
    @SaCheckPermission(value = "online-user:manage", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @DeleteMapping("/token-md5/{tokenMd5}")
    public Result<Void> kickoutByTokenMd5(
            @PathVariable @NotBlank(message = "Token MD5值不能为空") String tokenMd5) {
        try {
            log.info("根据Token MD5踢出用户, tokenMd5: {}", tokenMd5);

            boolean success = onlineUserService.kickoutByTokenMd5(tokenMd5);

            if (success) {
                log.info("根据Token MD5踢出用户成功, tokenMd5: {}", tokenMd5);
                return Result.success("踢出用户成功");
            } else {
                log.warn("根据Token MD5踢出用户失败，未找到匹配的Token, tokenMd5: {}", tokenMd5);
                return Result.error("未找到匹配的用户会话");
            }

        } catch (Exception e) {
            log.error("根据Token MD5踢出用户失败, tokenMd5: {}, error: {}",
                    tokenMd5, e.getMessage(), e);
            return Result.error("踢出用户失败");
        }
    }

    /**
     * 获取在线用户统计信息
     *
     * <p>统计当前系统中在线用户的各项数据</p>
     * <p>包括总数、设备类型分布、用户类型分布等</p>
     *
     * @return 在线用户统计信息
     */
    @OperationLog(value = "获取在线用户统计信息", type = OperationType.QUERY)
    @SaCheckPermission(value = "online-user:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @GetMapping("/statistics")
    public Result<OnlineUserPageVO.OnlineUserStatistics> getOnlineUserStatistics() {
        try {
            log.info("获取在线用户统计信息");

            OnlineUserPageVO.OnlineUserStatistics statistics = onlineUserService.getOnlineUserStatistics();

            log.info("获取在线用户统计信息成功, 总在线用户数: {}", statistics.getTotalOnlineUsers());

            return Result.success("获取在线用户统计信息成功", statistics);

        } catch (Exception e) {
            log.error("获取在线用户统计信息失败, error: {}", e.getMessage(), e);
            return Result.error("获取在线用户统计信息失败");
        }
    }

    /**
     * 检查用户是否在线
     *
     * <p>检查指定用户是否处于在线状态</p>
     *
     * @param loginId 用户登录ID
     * @return 用户是否在线
     */
    @OperationLog(value = "检查用户在线状态", type = OperationType.QUERY)
    @SaCheckPermission(value = "online-user:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @GetMapping("/{loginId}/online-status")
    public Result<Boolean> isUserOnline(
            @PathVariable @NotBlank(message = "用户登录ID不能为空") String loginId) {
        try {
            log.debug("检查用户在线状态, loginId: {}", loginId);

            boolean isOnline = onlineUserService.isUserOnline(loginId);

            log.debug("检查用户在线状态成功, loginId: {}, isOnline: {}", loginId, isOnline);

            return Result.success("检查用户在线状态成功", isOnline);

        } catch (Exception e) {
            log.error("检查用户在线状态失败, loginId: {}, error: {}", loginId, e.getMessage(), e);
            return Result.error("检查用户在线状态失败");
        }
    }

    /**
     * 获取用户在线设备数量
     *
     * <p>统计指定用户当前在线的设备数量</p>
     *
     * @param loginId 用户登录ID
     * @return 在线设备数量
     */
    @OperationLog(value = "获取用户设备数量", type = OperationType.QUERY)
    @SaCheckPermission(value = "online-user:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @GetMapping("/{loginId}/device-count")
    public Result<Integer> getUserDeviceCount(
            @PathVariable @NotBlank(message = "用户登录ID不能为空") String loginId) {
        try {
            log.debug("获取用户设备数量, loginId: {}", loginId);

            int deviceCount = onlineUserService.getUserDeviceCount(loginId);

            log.debug("获取用户设备数量成功, loginId: {}, deviceCount: {}", loginId, deviceCount);

            return Result.success("获取用户设备数量成功", deviceCount);

        } catch (Exception e) {
            log.error("获取用户设备数量失败, loginId: {}, error: {}", loginId, e.getMessage(), e);
            return Result.error("获取用户设备数量失败");
        }
    }

    /**
     * 对Token值进行脱敏处理
     *
     * @param tokenValue Token值
     * @return 脱敏后的Token值
     */
    private String maskToken(String tokenValue) {
        if (tokenValue == null || tokenValue.length() <= 10) {
            return "***";
        }

        String prefix = tokenValue.substring(0, 6);
        String suffix = tokenValue.substring(tokenValue.length() - 4);
        return prefix + "***" + suffix;
    }
}
