package com.akey.web.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.akey.common.Result;
import com.akey.common.annotation.OperationLog;
import com.akey.common.annotation.OperationModule;
import com.akey.common.constant.SystemConstant;
import com.akey.common.enums.OperationType;
import com.akey.core.dto.MenuCreateDTO;
import com.akey.core.dto.MenuQueryDTO;
import com.akey.core.dto.MenuUpdateDTO;
import com.akey.core.dao.entity.Menu;
import com.akey.core.dao.service.MenuService;
import com.akey.core.vo.MenuVO;
import com.akey.common.enums.BuiltinEnum;
import com.akey.common.enums.EnableStatusEnum;
import com.akey.common.enums.MenuTypeEnum;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.List;

/**
 * 菜单管理控制器
 * 
 * <p>提供菜单管理的REST API接口</p>
 * <p>包括菜单的增删改查、树形结构获取、状态管理等功能</p>
 * 
 * <AUTHOR>
 * @since 2025-07-09
 */
@Slf4j
@RestController
@RequestMapping("/menu")
@RequiredArgsConstructor
@Validated
@OperationModule("菜单管理")
public class MenuController {

    private final MenuService menuService;

    /**
     * 获取所有菜单
     * 
     * <p>支持多种筛选条件：菜单名称、菜单类型、状态等</p>
     * 
     * @param queryDTO 查询条件DTO
     * @return 菜单树列表
     */
    @SaCheckPermission(value = "menu:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @GetMapping("/getMenus")
    public Result<List<Menu>> getMenuTree(MenuQueryDTO queryDTO) {
        try {
            log.info("获取菜单树列表, queryDTO: {}", queryDTO);
            
            // 调用服务层获取菜单列表
            List<Menu> getMenus = menuService.getMenuList(
                queryDTO.getMenuName(),
                queryDTO.getMenuType(),
                queryDTO.getStatus()
            );
            return Result.success("获取菜单列表成功", getMenus);
        } catch (Exception e) {
            log.error("获取菜单列表失败, queryDTO: {}, error: {}", queryDTO, e.getMessage(), e);
            return Result.error("获取菜单列表失败");
        }
    }

    /**
     * 根据ID获取菜单详情
     * 
     * <p>用于编辑菜单时回显数据</p>
     * <p>需要菜单查看权限或管理员角色</p>
     * 
     * @param id 菜单ID
     * @return 菜单详情
     */
    @OperationLog(value = "获取菜单详情", type = OperationType.QUERY)
    @SaCheckPermission(value = "menu:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @GetMapping("/{id}")
    public Result<Menu> getMenuById(@PathVariable @NotBlank(message = "菜单ID不能为空") String id) {
        try {
            return Result.success("获取菜单详情成功", menuService.getMenuById(id));
        } catch (Exception e) {
            log.error("根据ID获取菜单详情失败, id: {}, error: {}", id, e.getMessage(), e);
            return Result.error("获取菜单详情失败");
        }
    }

    /**
     * 创建菜单
     * 
     * <p>新增菜单功能</p>
     * <p>需要菜单创建权限或管理员角色</p>
     * 
     * @param createDTO 创建菜单DTO
     * @return 操作结果
     */
    @OperationLog(value = "创建菜单", type = OperationType.CREATE)
    @SaCheckPermission(value = "menu:create", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @PostMapping
    public Result<Void> createMenu(@RequestBody @Valid MenuCreateDTO createDTO) {
        try {
            log.info("创建菜单, createDTO: {}", createDTO);
            
            // 检查菜单编码是否已存在
            if (menuService.existsByMenuCode(createDTO.getMenuCode(), null)) {
                log.warn("菜单编码已存在, menuCode: {}", createDTO.getMenuCode());
                return Result.error("菜单编码已存在");
            }
            
            // 检查父菜单是否有效
            if (StringUtils.hasText(createDTO.getParentId())) {
                if (!menuService.isValidParent(createDTO.getParentId(), null)) {
                    log.warn("无效的父菜单ID, parentId: {}", createDTO.getParentId());
                    return Result.error("无效的父菜单");
                }
            }
            
            // 转换为实体对象
            Menu menu = new Menu();
            BeanUtils.copyProperties(createDTO, menu);
            
            // 创建菜单
            boolean success = menuService.createMenu(menu);
            
            if (success) {
                log.info("创建菜单成功, menuName: {}, menuCode: {}", createDTO.getMenuName(), createDTO.getMenuCode());
                return Result.success("创建菜单成功");
            } else {
                log.warn("创建菜单失败, menuName: {}, menuCode: {}", createDTO.getMenuName(), createDTO.getMenuCode());
                return Result.error("创建菜单失败");
            }
            
        } catch (Exception e) {
            log.error("创建菜单异常, createDTO: {}, error: {}", createDTO, e.getMessage(), e);
            return Result.error("创建菜单失败");
        }
    }

    /**
     * 更新菜单
     * 
     * <p>修改菜单功能</p>
     * <p>需要菜单更新权限或管理员角色</p>
     * 
     * @param updateDTO 更新菜单DTO
     * @return 操作结果
     */
    @OperationLog(value = "修改菜单", type = OperationType.UPDATE)
    @SaCheckPermission(value = "menu:update", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @PutMapping
    public Result<Void> updateMenu(@RequestBody @Valid MenuUpdateDTO updateDTO) {
        try {
            log.info("更新菜单, updateDTO: {}", updateDTO);

            // 检查菜单是否存在
            Menu existingMenu = menuService.getMenuById(updateDTO.getId());
            if (existingMenu == null) {
                log.warn("菜单不存在, id: {}", updateDTO.getId());
                return Result.error("菜单不存在");
            }

            // 检查是否为内置菜单，内置菜单不允许修改状态
            if (existingMenu.getIsBuiltin() == BuiltinEnum.BUILTIN &&
                    updateDTO.getStatus() != null &&
                    !updateDTO.getStatus().equals(existingMenu.getStatus())) {
                log.warn("内置菜单不允许修改状态, id: {}, menuName: {}", updateDTO.getId(), existingMenu.getMenuName());
                return Result.error("内置菜单不允许修改状态");
            }

            // 检查菜单编码是否已存在（排除自己）
            if (menuService.existsByMenuCode(updateDTO.getMenuCode(), updateDTO.getId())) {
                log.warn("菜单编码已存在, menuCode: {}", updateDTO.getMenuCode());
                return Result.error("菜单编码已存在");
            }

            // 检查父菜单是否有效
            if (StringUtils.hasText(updateDTO.getParentId())) {
                if (!menuService.isValidParent(updateDTO.getParentId(), updateDTO.getId())) {
                    log.warn("无效的父菜单ID, parentId: {}", updateDTO.getParentId());
                    return Result.error("无效的父菜单");
                }
            }

            // 转换为实体对象
            Menu menu = new Menu();
            BeanUtils.copyProperties(updateDTO, menu);

            // 更新菜单
            boolean success = menuService.updateMenu(menu);

            if (success) {
                log.info("更新菜单成功, id: {}, menuName: {}", updateDTO.getId(), updateDTO.getMenuName());
                return Result.success("更新菜单成功");
            } else {
                log.warn("更新菜单失败, id: {}, menuName: {}", updateDTO.getId(), updateDTO.getMenuName());
                return Result.error("更新菜单失败");
            }
            
        } catch (Exception e) {
            log.error("更新菜单异常, updateDTO: {}, error: {}", updateDTO, e.getMessage(), e);
            return Result.error("更新菜单失败");
        }
    }

    /**
     * 删除菜单
     * 
     * <p>删除菜单功能，会检查是否有子菜单和关联关系</p>
     * <p>需要菜单删除权限或管理员角色</p>
     * 
     * @param id 菜单ID
     * @return 操作结果
     */
    @OperationLog(value = "删除菜单", type = OperationType.DELETE)
    @SaCheckPermission(value = "menu:delete", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @DeleteMapping("/{id}")
    public Result<Void> deleteMenu(@PathVariable @NotBlank(message = "菜单ID不能为空") String id) {
        try {
            log.info("删除菜单, id: {}", id);

            // 检查菜单是否存在
            Menu menu = menuService.getMenuById(id);
            if (menu == null) {
                log.warn("菜单不存在, id: {}", id);
                return Result.error("菜单不存在");
            }

            // 检查是否为内置菜单
            if (menu.getIsBuiltin() == BuiltinEnum.BUILTIN) {
                log.warn("内置菜单不允许删除, id: {}, menuName: {}", id, menu.getMenuName());
                return Result.error("内置菜单不允许删除");
            }

            // 检查是否有子菜单
            List<Menu> childMenus = menuService.getChildMenus(id);
            if (!childMenus.isEmpty()) {
                log.warn("菜单存在子菜单，无法删除, id: {}, childCount: {}", id, childMenus.size());
                return Result.error("菜单存在子菜单，请先删除子菜单");
            }

            // 检查是否被用户类型分配
            if (menuService.isMenuAssigned(id)) {
                log.warn("菜单已被分配，无法删除, id: {}", id);
                return Result.error("菜单已被分配，无法删除");
            }

            // 删除菜单
            boolean success = menuService.deleteMenu(id);

            if (success) {
                log.info("删除菜单成功, id: {}", id);
                return Result.success("删除菜单成功");
            } else {
                log.warn("删除菜单失败, id: {}", id);
                return Result.error("删除菜单失败");
            }
            
        } catch (Exception e) {
            log.error("删除菜单异常, id: {}, error: {}", id, e.getMessage(), e);
            return Result.error("删除菜单失败");
        }
    }

    /**
     * 批量更新菜单状态
     *
     * <p>批量启用或禁用菜单</p>
     * <p>需要菜单更新权限或管理员角色</p>
     *
     * @param menuIds 菜单ID列表
     * @param status 要设置的状态
     * @return 操作结果
     */
    @OperationLog(value = "批量更新菜单状态", type = OperationType.UPDATE)
    @SaCheckPermission(value = "menu:update", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @PutMapping("/batch-status")
    public Result<Void> batchUpdateMenuStatus(
            @RequestParam @NotEmpty(message = "菜单ID列表不能为空") List<String> menuIds,
            @RequestParam @NotNull(message = "状态不能为空") EnableStatusEnum status) {
        try {
            log.info("批量更新菜单状态, menuIds: {}, status: {}", menuIds, status);
            int updateCount = menuService.batchUpdateMenuStatus(menuIds, status);

            log.info("批量更新菜单状态成功, 更新数量: {}", updateCount);
            return Result.success("批量更新菜单状态成功，共更新" + updateCount + "条记录");

        } catch (Exception e) {
            log.error("批量更新菜单状态异常, menuIds: {}, status: {}, error: {}", menuIds, status, e.getMessage(), e);
            return Result.error("批量更新菜单状态失败");
        }
    }

    /**
     * 获取所有目录菜单
     *
     * <p>获取所有类型为目录的菜单，用于作为父菜单选项</p>
     * <p>只返回启用状态的目录菜单</p>
     * <p>需要菜单查看权限或管理员角色</p>
     *
     * @return 目录菜单列表
     */
    @OperationLog(value = "获取所有目录菜单", type = OperationType.QUERY)
    @SaCheckPermission(value = "menu:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @GetMapping("/directories")
    public Result<List<MenuVO>> getDirectoryMenus() {
        try {
            log.info("获取所有目录菜单");

            // 调用服务层获取目录类型的菜单
            List<Menu> directoryMenus = menuService.getMenuList(
                null,
                MenuTypeEnum.DIRECTORY,
                EnableStatusEnum.ENABLED
            );

            // 转换为VO对象
            List<MenuVO> menuVOList = new ArrayList<>();
            for (Menu menu : directoryMenus) {
                MenuVO menuVO = new MenuVO();
                BeanUtils.copyProperties(menu, menuVO);
                menuVOList.add(menuVO);
            }

            log.info("获取目录菜单成功, 数量: {}", menuVOList.size());
            return Result.success("获取目录菜单成功", menuVOList);

        } catch (Exception e) {
            log.error("获取目录菜单失败, error: {}", e.getMessage(), e);
            return Result.error("获取目录菜单失败");
        }
    }

}
