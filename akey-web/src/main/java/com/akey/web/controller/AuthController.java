package com.akey.web.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.akey.common.Result;
import com.akey.common.entity.CaptchaResult;
import com.akey.common.entity.DeviceInfo;
import com.akey.common.exception.AuthException;
import com.akey.common.exception.BusinessException;
import com.akey.core.factory.AsyncLoginLogFactory;
import com.akey.core.dao.service.CaptchaService;
import com.akey.core.service.OnlineUserLocationService;
import com.akey.framework.web.util.GoogleAuthUtil;
import com.akey.framework.web.util.DeviceInfoUtil;
import com.akey.core.dao.entity.User;
import com.akey.core.dao.entity.UserType;
import com.akey.core.dao.service.UserService;
import com.akey.core.dao.service.UserTypeService;
import com.akey.web.dto.LoginRequestDTO;
import com.akey.web.vo.CaptchaVo;
import com.akey.web.vo.LoginResponseVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器
 *
 * <p>提供基于Sa-Token的用户认证和权限管理功能</p>
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Validated
public class AuthController {

    private final UserService userService;
    private final UserTypeService userTypeService;
    private final CaptchaService captchaService;
    private final AsyncLoginLogFactory asyncLoginLogFactory;
    private final DeviceInfoUtil deviceInfoUtil;
    private final OnlineUserLocationService onlineUserLocationService;

    /**
     * 用户登录
     *
     * @param loginRequest 登录请求参数
     * @param request      HTTP请求对象
     * @return 登录结果
     */
    @PostMapping("/login")
    public Result<LoginResponseVO> login(@Valid @RequestBody LoginRequestDTO loginRequest,
                                         HttpServletRequest request) {
        log.info("用户登录请求, username: {}", loginRequest.getUsername());
        // 获取基础设备信息
        DeviceInfo deviceInfo = null;
        try {
            deviceInfo = deviceInfoUtil.getDeviceInfo(request);
            log.info("用户登录设备信息 - IP: {}, 设备: {}, 风险等级: {}",
                    deviceInfo.getClientIp(),
                    deviceInfo.getDeviceDescription(),
                    deviceInfo.getRiskLevel());

            // 安全检查：高风险设备可能需要额外验证
            if ("HIGH".equals(deviceInfo.getRiskLevel())) {
                log.warn("检测到高风险登录尝试 - 用户: {}, IP: {}, 设备: {}",
                        loginRequest.getUsername(),
                        deviceInfo.getClientIp(),
                        deviceInfo.getDeviceDescription());
                // 这里可以添加额外的安全验证，如验证码、短信验证等
            }

            // 先检查图形验证码是否正确（但不消费）
            boolean isCaptchaValid = captchaService.checkCaptcha(
                    loginRequest.getCaptchaId(),
                    loginRequest.getCaptcha(),
                    deviceInfo.getClientIp()
            );
            if (!isCaptchaValid) {
                log.warn("图形验证码验证失败, username: {}, captchaId: {}",
                        loginRequest.getUsername(), loginRequest.getCaptchaId());

                // 异步记录登录失败日志
                asyncLoginLogFactory.recordCredentialFailureAsync(
                        loginRequest.getUsername(), deviceInfo, "图形验证码验证失败");

                return Result.error("验证码输入错误，请重试");
            }
            log.debug("图形验证码检查成功, username: {}", loginRequest.getUsername());

            // 验证用户名和密码
            User user = userService.verifyUser(loginRequest.getUsername(), loginRequest.getPassword());

            // 谷歌验证码验证
            if (StringUtils.hasText(user.getGoogleAuthKey())) {
                log.info("用户已设置谷歌验证器，需要验证谷歌验证码, userId: {}", user.getId());

                // 检查是否提供了谷歌验证码
                if (!StringUtils.hasText(loginRequest.getGoogleAuthCode())) {
                    log.warn("用户已设置谷歌验证器但未提供验证码, userId: {}", user.getId());

                    // 异步记录谷歌验证码未提供的失败日志
                    asyncLoginLogFactory.recordCredentialFailureAsync(
                            loginRequest.getUsername(), deviceInfo, "未提供谷歌验证码");

                    return Result.error(9999, "请提供谷歌验证码");
                }

                // 验证谷歌验证码
                boolean isGoogleAuthValid = GoogleAuthUtil.verifyTOTP(
                        user.getGoogleAuthKey(),
                        loginRequest.getGoogleAuthCode()
                );

                if (!isGoogleAuthValid) {
                    log.warn("谷歌验证码验证失败, userId: {}, username: {}",
                            user.getId(), loginRequest.getUsername());

                    // 异步记录谷歌验证码错误的失败日志
                    asyncLoginLogFactory.recordCredentialFailureAsync(
                            loginRequest.getUsername(), deviceInfo, "谷歌验证码错误");

                    return Result.error(-9998, "谷歌验证码错误，请检查后重试");
                }

                log.info("谷歌验证码验证成功, userId: {}", user.getId());
            } else {
                log.debug("用户未设置谷歌验证器，跳过谷歌验证, userId: {}", user.getId());
            }

            // 所有验证都通过后，消费图形验证码
            captchaService.consumeCaptcha(loginRequest.getCaptchaId());
            log.debug("图形验证码已消费, username: {}", loginRequest.getUsername());

            // 执行登录
            StpUtil.login(user.getId(), "PC");
            String sessionId = String.valueOf(StpUtil.getSession().getLoginId());

            // 设置基础信息
            StpUtil.getSession().set("username", user.getUsername());
            StpUtil.getSession().set("userTypeId", user.getUserTypeId());

            // 设置token,用于异步任务中可以获取登录的用户
            deviceInfo.setToken(StpUtil.getTokenValue());

            // 异步记录登录成功日志
            asyncLoginLogFactory.recordSuccessLoginAsync(
                    user.getId(), user.getUsername(), sessionId, deviceInfo);

            // 异步获取并存储用户位置信息
            onlineUserLocationService.fetchAndStoreLocationAsync(
                    StpUtil.getTokenValue(), deviceInfo.getClientIp());

            // 更新登录信息
            userService.updateLoginInfo(user.getId(), deviceInfo.getClientIp());

            // 获取用户类型信息
            UserType userType = userTypeService.getUserTypeById(user.getUserTypeId());
            String userTypeName = (userType != null) ? userType.getTypeName() : "未知类型";

            // 构建返回数据
            LoginResponseVO responseVO = new LoginResponseVO();
            responseVO.setToken(StpUtil.getTokenValue());
            responseVO.setUserId(user.getId());
            responseVO.setUsername(user.getUsername());
            responseVO.setUserTypeId(user.getUserTypeId());
            responseVO.setUserTypeName(userTypeName);
            responseVO.setLoginTime(LocalDateTime.now());
            responseVO.setLastLoginTime(user.getLastLoginTime());
            responseVO.setLastLoginIp(user.getLastLoginIp());

            // 设置谷歌验证器状态
            responseVO.setHasGoogleAuth(StringUtils.hasText(user.getGoogleAuthKey()));

            // 设置Token过期时间（根据Sa-Token配置计算）
            long tokenTimeout = StpUtil.getTokenTimeout();
            if (tokenTimeout > 0) {
                responseVO.setTokenExpireTime(LocalDateTime.now().plusSeconds(tokenTimeout));
            }

            // 判断是否首次登录（如果上次登录时间为空，则认为是首次登录）
            responseVO.setFirstLogin(user.getLastLoginTime() == null);

            log.info("用户登录成功, username: {}, userId: {}", loginRequest.getUsername(), user.getId());
            return Result.success("登录成功", responseVO);

        } catch (BusinessException e) {
            // 异步记录登录失败日志
            asyncLoginLogFactory.recordCredentialFailureAsync(
                    loginRequest.getUsername(), deviceInfo, e.getMessage());

            return Result.error(e.getMessage());
        } catch (AuthException authException) {
            log.warn("用户登录失败, username: {}, error: {}", loginRequest.getUsername(), authException.getMessage());

            // 异步记录登录失败日志
            asyncLoginLogFactory.recordCredentialFailureAsync(
                    loginRequest.getUsername(), deviceInfo, authException.getMessage());

            return Result.error(authException.getMessage());
        } catch (Exception e) {
            log.error("用户登录异常, username: {}, error: {}", loginRequest.getUsername(), e.getMessage(), e);

            // 异步记录登录失败日志
            asyncLoginLogFactory.recordCredentialFailureAsync(
                    loginRequest.getUsername(), deviceInfo, "系统异常");

            return Result.error("登录失败，请稍后重试");
        }
    }

    /**
     * 用户注销
     *
     * @return 注销结果
     */
    @GetMapping("/logout")
    public Result<Void> logout() {
        try {
            String userId = StpUtil.getLoginIdAsString();
            StpUtil.logout();
            log.info("用户注销成功, userId: {}", userId);
            return Result.success("注销成功");
        } catch (Exception e) {
            log.error("用户注销异常, error: {}", e.getMessage(), e);
            return Result.error("注销失败");
        }
    }

    /**
     * 获取验证码
     *
     * @return 验证码信息
     */
    @GetMapping("/captcha")
    public Result<CaptchaVo> getCaptcha(HttpServletRequest request) {
        // 获取IP
        DeviceInfo deviceInfo = deviceInfoUtil.getDeviceInfo(request);
        // 生成验证码
        CaptchaResult captchaResult = captchaService.generateCaptcha(deviceInfo.getClientIp());

        CaptchaVo captchaInfo = CaptchaVo.builder().captchaId(captchaResult.getCaptchaId())
                .imageBase64(captchaResult.getImageBase64())
                .expireTime(captchaResult.getTimestamp())
                .build();

        return Result.success("获取验证码成功", captchaInfo);
    }

    /**
     * 检查登录状态
     *
     * @return 登录状态信息
     */
    @GetMapping("/isLogin")
    public Result<Map<String, Object>> isLogin() {
        Map<String, Object> result = new HashMap<>();
        result.put("isLogin", StpUtil.isLogin());

        if (StpUtil.isLogin()) {
            result.put("userId", StpUtil.getLoginIdAsString());
            result.put("tokenInfo", StpUtil.getTokenInfo());
        }

        return Result.success("获取登录状态成功", result);
    }


}
