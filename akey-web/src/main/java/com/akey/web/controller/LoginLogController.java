package com.akey.web.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import com.akey.common.Result;
import com.akey.common.annotation.OperationLog;
import com.akey.common.annotation.OperationModule;
import com.akey.common.constant.SystemConstant;
import com.akey.common.enums.OperationType;
import com.akey.core.dao.entity.LoginLog;
import com.akey.core.dao.service.LoginLogService;
import com.akey.core.vo.UserLoginRecordVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 登录日志控制器
 * 
 * <p>提供登录日志的查询、统计和管理功能</p>
 * <p>包括日志分页查询、统计分析、可疑行为检测等</p>
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@RestController
@RequestMapping("/system/login-log")
@RequiredArgsConstructor
@Validated
@OperationModule("登录日志管理")
public class LoginLogController {

    private final LoginLogService loginLogService;

    /**
     * 分页查询登录日志
     *
     * @param current 当前页码
     * @param size 每页大小
     * @param userId 用户ID（可选）
     * @param username 用户名（可选）
     * @param clientIp 客户端IP（可选）
     * @param loginStatus 登录状态（可选，0-失败，1-成功）
     * @param isSuspicious 是否可疑（可选，0-否，1-是）
     * @param riskLevel 风险等级（可选，LOW/MEDIUM/HIGH）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 分页结果
     */
    @GetMapping("/page")
    @SaCheckPermission(value = "login:log:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    public Result<IPage<LoginLog>> getLoginLogPage(
            @RequestParam(defaultValue = "1") @Min(1) Long current,
            @RequestParam(defaultValue = "10") @Min(1) Long size,
            @RequestParam(required = false) String userId,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String clientIp,
            @RequestParam(required = false) Integer loginStatus,
            @RequestParam(required = false) Integer isSuspicious,
            @RequestParam(required = false) String riskLevel,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        
        log.debug("分页查询登录日志, current: {}, size: {}, userId: {}, username: {}, clientIp: {}, loginStatus: {}, isSuspicious: {}, riskLevel: {}, startTime: {}, endTime: {}",
                 current, size, userId, username, clientIp, loginStatus, isSuspicious, riskLevel, startTime, endTime);

        try {
            Page<LoginLog> page = new Page<>(current, size);
            IPage<LoginLog> result = loginLogService.getLoginLogPage(page, userId, username, clientIp, loginStatus, isSuspicious, riskLevel, startTime, endTime);
            
            log.debug("分页查询登录日志成功, 总记录数: {}, 当前页记录数: {}", result.getTotal(), result.getRecords().size());
            return Result.success("查询成功", result);
            
        } catch (Exception e) {
            log.error("分页查询登录日志失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户最近的登录记录
     * 
     * @param userId 用户ID
     * @param limit 限制数量（默认10条）
     * @return 登录记录列表
     */
    @OperationLog(value = "获取用户最近登录记录", type = OperationType.QUERY)
    @GetMapping("/recent/{userId}")
    @SaCheckPermission(value = "login:log:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    public Result<List<LoginLog>> getRecentLoginsByUser(
            @PathVariable @NotNull String userId,
            @RequestParam(defaultValue = "10") @Min(1) Integer limit) {
        
        log.debug("获取用户最近登录记录, userId: {}, limit: {}", userId, limit);
        
        try {
            List<LoginLog> result = loginLogService.getRecentLoginsByUser(userId, limit);
            
            log.debug("获取用户最近登录记录成功, userId: {}, 记录数: {}", userId, result.size());
            return Result.success("查询成功", result);
            
        } catch (Exception e) {
            log.error("获取用户最近登录记录失败, userId: {}", userId, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前登录用户的最近登录记录
     *
     * <p>获取当前登录用户最近几条登录记录，包含登录时间、设备信息、地理位置等</p>
     * <p>按登录时间倒序排列，只需要登录即可访问，无需特殊权限</p>
     *
     * @return 登录记录列表
     */
    @OperationLog(value = "获取我的登录记录", type = OperationType.QUERY)
    @GetMapping("/my-records")
    public Result<List<UserLoginRecordVO>> getMyLoginRecords() {
        try {
            // 获取当前登录用户ID
            String username = StpUtil.getSession().getString("username");
            log.debug("获取当前用户登录记录, username: {}", username);

            // 获取最近15条登录记录
            List<LoginLog> loginLogs = loginLogService.getRecentLoginsByUser(username, 10);

            // 转换为VO对象
            List<UserLoginRecordVO> records = loginLogs.stream()
                    .map(this::convertToUserLoginRecordVO)
                    .collect(Collectors.toList());

            log.debug("获取当前用户登录记录成功, username: {}, 记录数: {}", username, records.size());
            return Result.success("查询成功", records);

        } catch (Exception e) {
            log.error("获取当前用户登录记录失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取登录统计信息
     * 
     * @param startTime 开始时间（可选，默认7天前）
     * @param endTime 结束时间（可选，默认当前时间）
     * @return 统计信息
     */
    @OperationLog(value = "获取登录统计信息", type = OperationType.QUERY)
    @GetMapping("/statistics")
    @SaCheckPermission(value = "login:log:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    public Result<Map<String, Object>> getLoginStatistics(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        
        // 设置默认时间范围（最近7天）
        if (startTime == null) {
            startTime = LocalDateTime.now().minusDays(7);
        }
        if (endTime == null) {
            endTime = LocalDateTime.now();
        }
        
        log.debug("获取登录统计信息, startTime: {}, endTime: {}", startTime, endTime);
        
        try {
            Map<String, Object> result = loginLogService.getLoginStatistics(startTime, endTime);
            
            log.debug("获取登录统计信息成功");
            return Result.success("查询成功", result);
            
        } catch (Exception e) {
            log.error("获取登录统计信息失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }



    /**
     * 统计用户登录次数
     * 
     * @param userId 用户ID
     * @param startTime 开始时间（可选，默认7天前）
     * @param endTime 结束时间（可选，默认当前时间）
     * @param loginStatus 登录状态（可选，0-失败，1-成功）
     * @return 登录次数
     */
    @OperationLog(value = "统计用户登录次数", type = OperationType.QUERY)
    @GetMapping("/count/user/{userId}")
    @SaCheckPermission(value = "login:log:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    public Result<Long> countLoginsByUser(
            @PathVariable @NotNull String userId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @RequestParam(required = false) Integer loginStatus) {
        
        // 设置默认时间范围（最近7天）
        if (startTime == null) {
            startTime = LocalDateTime.now().minusDays(7);
        }
        if (endTime == null) {
            endTime = LocalDateTime.now();
        }
        
        log.debug("统计用户登录次数, userId: {}, startTime: {}, endTime: {}, loginStatus: {}", userId, startTime, endTime, loginStatus);
        
        try {
            Long count = loginLogService.countLoginsByUser(userId, startTime, endTime, loginStatus);
            
            log.debug("统计用户登录次数成功, userId: {}, count: {}", userId, count);
            return Result.success("查询成功", count);
            
        } catch (Exception e) {
            log.error("统计用户登录次数失败, userId: {}", userId, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 统计IP地址登录次数
     * 
     * @param clientIp 客户端IP
     * @param startTime 开始时间（可选，默认7天前）
     * @param endTime 结束时间（可选，默认当前时间）
     * @param loginStatus 登录状态（可选，0-失败，1-成功）
     * @return 登录次数
     */
    @OperationLog(value = "统计IP地址登录次数", type = OperationType.QUERY)
    @GetMapping("/count/ip/{clientIp}")
    @SaCheckPermission(value = "login:log:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    public Result<Long> countLoginsByIp(
            @PathVariable @NotNull String clientIp,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @RequestParam(required = false) Integer loginStatus) {
        
        // 设置默认时间范围（最近7天）
        if (startTime == null) {
            startTime = LocalDateTime.now().minusDays(7);
        }
        if (endTime == null) {
            endTime = LocalDateTime.now();
        }
        
        log.debug("统计IP地址登录次数, clientIp: {}, startTime: {}, endTime: {}, loginStatus: {}", clientIp, startTime, endTime, loginStatus);
        
        try {
            Long count = loginLogService.countLoginsByIp(clientIp, startTime, endTime, loginStatus);
            
            log.debug("统计IP地址登录次数成功, clientIp: {}, count: {}", clientIp, count);
            return Result.success("查询成功", count);
            
        } catch (Exception e) {
            log.error("统计IP地址登录次数失败, clientIp: {}", clientIp, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 清理过期的登录日志（物理删除）
     *
     * @param days 清理多少天前的记录（默认30天）
     * @return 清理的记录数
     */
    @OperationLog(value = "清理登录日志", type = OperationType.DELETE)
    @DeleteMapping("/cleanup")
    @SaCheckPermission(value = "login:log:delete", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    public Result<Long> cleanupExpiredLogs(@RequestParam(defaultValue = "30") @Min(1) Integer days) {

        log.warn("开始物理删除过期登录日志, days: {}", days);

        try {
            // 计算删除的截止时间：days天前的23:59:59
            // 例如：今天是31号，days=1，则删除30号23:59:59之前的所有记录
            LocalDateTime beforeTime = LocalDateTime.now()
                    .minusDays(days)
                    .withHour(23)
                    .withMinute(59)
                    .withSecond(59)
                    .withNano(999999999);

            String deleteDate = beforeTime.toLocalDate().toString();
            log.warn("删除时间点: {}, 将删除{}及之前的所有登录日志（{}天前）",
                    beforeTime, deleteDate, days);

            Long deletedCount = loginLogService.physicalCleanupExpiredLogs(beforeTime);

            log.warn("物理删除过期登录日志完成, 删除记录数: {}", deletedCount);
            return Result.success("清理成功", deletedCount);

        } catch (Exception e) {
            log.error("物理删除过期登录日志失败", e);
            return Result.error("清理失败：" + e.getMessage());
        }
    }

    /**
     * 获取登录日志详情
     *
     * @param id 登录日志ID
     * @return 登录日志详情
     */
    @OperationLog(value = "获取登录日志心情", type = OperationType.QUERY)
    @GetMapping("/{id}")
    @SaCheckPermission(value = "login:log:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    public Result<LoginLog> getLoginLogById(@PathVariable @NotNull String id) {

        log.debug("获取登录日志详情, id: {}", id);

        try {
            LoginLog loginLog = loginLogService.getById(id);
            if (loginLog == null) {
                return Result.error("登录日志不存在");
            }

            log.debug("获取登录日志详情成功, id: {}", id);
            return Result.success("查询成功", loginLog);

        } catch (Exception e) {
            log.error("获取登录日志详情失败, id: {}", id, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }



    /**
     * 获取登录趋势数据（用于图表展示）
     *
     * @param days 统计天数（默认7天）
     * @return 趋势数据
     */
    @OperationLog(value = "获取登录趋势数据", type = OperationType.QUERY)
    @GetMapping("/trend")
    @SaCheckPermission(value = "login:log:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    public Result<Map<String, Object>> getLoginTrend(@RequestParam(defaultValue = "7") @Min(1) Integer days) {

        log.debug("获取登录趋势数据, days: {}", days);

        try {
            Map<String, Object> trendData = new HashMap<>();

            // 获取每日登录统计
            List<Map<String, Object>> dailyStats = loginLogService.getDailyLoginStatistics(days);
            trendData.put("dailyStats", dailyStats);

            // 获取基础统计数据
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusDays(days);
            Map<String, Object> basicStats = loginLogService.getLoginStatistics(startTime, endTime);
            trendData.put("basicStats", basicStats);

            log.debug("获取登录趋势数据成功");
            return Result.success("查询成功", trendData);

        } catch (Exception e) {
            log.error("获取登录趋势数据失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取热门登录IP地址
     *
     * @param limit 限制数量（默认10个）
     * @param days 统计天数（默认7天）
     * @return IP地址统计列表
     */
    @OperationLog(value = "获取热门登录IP地址", type = OperationType.QUERY)
    @GetMapping("/top-ips")
    @SaCheckPermission(value = "login:log:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    public Result<List<Map<String, Object>>> getTopLoginIps(
            @RequestParam(defaultValue = "10") @Min(1) Integer limit,
            @RequestParam(defaultValue = "7") @Min(1) Integer days) {

        log.debug("获取热门登录IP地址, limit: {}, days: {}", limit, days);

        try {
            List<Map<String, Object>> topIps = loginLogService.getTopLoginIps(limit, days);

            log.debug("获取热门登录IP地址成功, 数量: {}", topIps.size());
            return Result.success("查询成功", topIps);

        } catch (Exception e) {
            log.error("获取热门登录IP地址失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取登录设备类型统计
     *
     * @param days 统计天数（默认7天）
     * @return 设备类型统计
     */
    @OperationLog(value = "获取登录设备类型统计", type = OperationType.QUERY)
    @GetMapping("/device-stats")
    @SaCheckPermission(value = "login:log:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    public Result<Map<String, Object>> getDeviceStatistics(@RequestParam(defaultValue = "7") @Min(1) Integer days) {

        log.debug("获取登录设备类型统计, days: {}", days);

        try {
            Map<String, Object> deviceStats = loginLogService.getDeviceStatistics(days);

            log.debug("获取登录设备类型统计成功");
            return Result.success("查询成功", deviceStats);

        } catch (Exception e) {
            log.error("获取登录设备类型统计失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取地理位置统计
     *
     * @param days 统计天数（默认7天）
     * @return 地理位置统计
     */
    @OperationLog(value = "获取地址位置统计", type = OperationType.QUERY)
    @GetMapping("/location-stats")
    @SaCheckPermission(value = "login:log:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    public Result<Map<String, Object>> getLocationStatistics(@RequestParam(defaultValue = "7") @Min(1) Integer days) {

        log.debug("获取地理位置统计, days: {}", days);

        try {
            Map<String, Object> locationStats = loginLogService.getLocationStatistics(days);

            log.debug("获取地理位置统计成功");
            return Result.success("查询成功", locationStats);

        } catch (Exception e) {
            log.error("获取地理位置统计失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 清空所有登录日志（物理删除）
     *
     * @return 清空的记录数
     */
    @DeleteMapping("/clear")
    @SaCheckPermission(value = "login:log:delete", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @OperationLog(value = "物理清空所有登录日志", type = OperationType.DELETE)
    public Result<Long> clearAllLogs() {

        log.warn("开始物理清空所有登录日志");

        try {
            // 物理删除所有记录
            Long deletedCount = loginLogService.physicalClearAllLogs();

            log.warn("物理清空所有登录日志完成, 删除记录数: {}", deletedCount);
            return Result.success("清空成功", deletedCount);

        } catch (Exception e) {
            log.error("物理清空所有登录日志失败", e);
            return Result.error("清空失败：" + e.getMessage());
        }
    }

    /**
     * 将LoginLog转换为UserLoginRecordVO
     *
     * @param loginLog 登录日志实体
     * @return 用户登录记录VO
     */
    private UserLoginRecordVO convertToUserLoginRecordVO(LoginLog loginLog) {
        UserLoginRecordVO vo = new UserLoginRecordVO();

        // 复制基本属性
        BeanUtils.copyProperties(loginLog, vo);

        return vo;
    }
}
