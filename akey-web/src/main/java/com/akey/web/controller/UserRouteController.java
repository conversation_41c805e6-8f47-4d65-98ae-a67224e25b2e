package com.akey.web.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import com.akey.common.Result;
import com.akey.common.annotation.OperationLog;
import com.akey.common.annotation.OperationModule;
import com.akey.common.enums.OperationType;
import com.akey.core.dao.service.UserRouteService;
import com.akey.core.vo.MenuRouterTreeVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户路由控制器
 *
 * <p>提供用户路由和权限相关的接口</p>
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Slf4j
@RestController
@RequestMapping("/route")
@RequiredArgsConstructor
@OperationModule("用户路由")
public class UserRouteController {

    private final UserRouteService userRouteService;

    /**
     * 获取当前登录用户的菜单树 (getUserRouters)
     *
     * <p>只返回菜单和目录，排除按钮级权限</p>
     * <p>超级管理员可获取全部数据，其他用户按用户类型过滤</p>
     * <p>返回树形结构，符合前端组件要求</p>
     *
     * @return 用户菜单树
     */
    @SaCheckLogin
    @OperationLog(value = "获取当前登录用户菜单列表", type = OperationType.QUERY)
    @GetMapping("/routers")
    public Result<List<MenuRouterTreeVO>> getUserRouters() {
        try {
            String userId = StpUtil.getLoginIdAsString();
            log.info("获取用户路由菜单, userId: {}", userId);
            List<MenuRouterTreeVO> menuTree = userRouteService.getUserRouters(userId);
            log.info("获取用户路由菜单成功, userId: {}, 菜单数: {}", userId, menuTree.size());
            return Result.success("获取成功", menuTree);
        } catch (Exception e) {
            log.error("获取用户路由菜单异常, error: {}", e.getMessage(), e);
            return Result.error("菜单获异常,请重试!");
        }
    }

    /**
     * 获取当前登录用户的权限列表 (getUserPermissions)
     *
     * <p>返回权限字符串数组</p>
     * <p>超级管理员可获取全部数据，其他用户按用户类型过滤</p>
     *
     * @return 用户权限字符串列表
     */
    @OperationLog(value = "获取当前登录用户权限列表", type = OperationType.QUERY)
    @SaCheckLogin
    @GetMapping("/permissions")
    public Result<List<String>> getUserPermissions() {
        try {
            String userId = StpUtil.getLoginIdAsString();
            log.info("获取用户权限列表, userId: {}", userId);

            List<String> permissions = userRouteService.getUserPermissions(userId);

            log.info("获取用户权限列表成功, userId: {}, 权限数: {}", userId, permissions.size());

            return Result.success("获取用户权限列表成功", permissions);

        } catch (Exception e) {
            log.error("获取用户权限列表异常, error: {}", e.getMessage(), e);
            return Result.error("权限获取异常,请重试!");
        }
    }


}
