package com.akey.web.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.akey.common.Result;
import com.akey.common.annotation.OperationLog;
import com.akey.common.annotation.OperationModule;
import com.akey.common.enums.OperationType;
import com.akey.core.dao.entity.User;
import com.akey.core.dao.service.UserService;
import com.akey.core.dto.GoogleAuthSetupDTO;
import com.akey.core.dto.GoogleAuthVerifyDTO;

import com.akey.core.vo.GoogleAuthSetupVO;
import com.akey.framework.web.util.GoogleAuthUtil;
import com.akey.framework.web.annotation.GoogleAuthRequired;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 谷歌验证器管理控制器
 * 
 * <p>提供谷歌验证器的设置、验证、重置等功能</p>
 * <p>支持TOTP（基于时间的一次性密码）验证</p>
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@RestController
@RequestMapping("/google-auth")
@RequiredArgsConstructor
@Validated
@OperationModule("谷歌验证管理")
public class GoogleAuthController {

    private final UserService userService;

    /**
     * 应用名称，用于谷歌验证器显示
     */
    @Value("${app.name:AKey System}")
    private String appName;

    /**
     * 生成谷歌验证器设置信息
     *
     * <p>为当前用户生成谷歌验证器设置所需的密钥和OTP Auth URL</p>
     * <p>如果用户已设置谷歌验证器，将生成新的密钥（需要重新设置）</p>
     *
     * @return 谷歌验证器设置信息
     */
    @OperationLog(value = "生成谷歌验证器设置信息", type = OperationType.QUERY, recordResult = false)
    @GetMapping("/setup")
    public Result<GoogleAuthSetupVO> generateSetupInfo() {
        try {
            // 获取当前登录用户
            String currentUserId = StpUtil.getLoginIdAsString();
            User user = userService.getUserById(currentUserId);
            if (user == null) {
                log.warn("用户不存在, userId: {}", currentUserId);
                return Result.error("用户不存在");
            }

            log.info("生成谷歌验证器设置信息, userId: {}, username: {}", currentUserId, user.getUsername());

            // 生成新的密钥
            String secretKey = GoogleAuthUtil.generateSecretKey();

            // 将临时密钥存储到会话中，用于后续验证
            StpUtil.getSession().set("google_auth_temp_key", secretKey);

            // 生成OTP Auth URL
            String accountName = user.getUsername();
            String otpAuthUrl = GoogleAuthUtil.getOtpAuthUrl(secretKey, accountName, appName);

            // 构建响应对象
            GoogleAuthSetupVO setupVO = new GoogleAuthSetupVO();
            setupVO.setSecretKey(secretKey);
            setupVO.setOtpAuthUrl(otpAuthUrl);
            setupVO.setAccountName(accountName);
            setupVO.setIssuer(appName);
            setupVO.setRemainingTime(GoogleAuthUtil.getRemainingTime());

            log.info("生成谷歌验证器设置信息成功, userId: {}", currentUserId);
            return Result.success("生成成功", setupVO);

        } catch (Exception e) {
            log.error("生成谷歌验证器设置信息失败, error: {}", e.getMessage(), e);
            return Result.error("生成失败：" + e.getMessage());
        }
    }

    /**
     * 确认设置谷歌验证器
     * 
     * <p>用户手动输入密钥或OTP Auth URL后，输入验证码确认设置</p>
     * <p>验证成功后将密钥保存到用户账户中</p>
     * 
     * @param setupDTO 设置确认参数
     * @return 设置结果
     */
    @OperationLog(value = "设置谷歌验证器", type = OperationType.UPDATE)
    @PostMapping("/setup")
    public Result<String> confirmSetup(@Valid @RequestBody GoogleAuthSetupDTO setupDTO) {
        try {
            // 获取当前登录用户
            String currentUserId = StpUtil.getLoginIdAsString();
            User user = userService.getUserById(currentUserId);
            if (user == null) {
                log.warn("用户不存在, userId: {}", currentUserId);
                return Result.error("用户不存在");
            }

            log.info("确认设置谷歌验证器, userId: {}, username: {}", currentUserId, user.getUsername());

            // 从会话中获取临时密钥
            String secretKey = (String) StpUtil.getSession().get("google_auth_temp_key");
            if (!StringUtils.hasText(secretKey)) {
                log.warn("未找到临时密钥, userId: {}", currentUserId);
                return Result.error("请先获取设置信息");
            }

            // 验证用户输入的验证码
            boolean isValid = GoogleAuthUtil.verifyTOTP(secretKey, setupDTO.getCode());
            if (!isValid) {
                log.warn("谷歌验证码验证失败, userId: {}, code: {}", currentUserId, setupDTO.getCode());
                return Result.error("验证码错误，请输入正确的验证码。");
            }

            // 保存密钥到用户账户
            boolean success = userService.updateGoogleAuthKey(currentUserId, secretKey);
            if (!success) {
                log.error("保存谷歌验证密钥失败, userId: {}", currentUserId);
                return Result.error("设置失败");
            }

            // 清除临时密钥
            StpUtil.getSession().delete("google_auth_temp_key");

            log.info("设置谷歌验证器成功, userId: {}", currentUserId);
            return Result.success("设置成功");

        } catch (Exception e) {
            log.error("确认设置谷歌验证器失败, error: {}", e.getMessage(), e);
            return Result.error("设置失败：" + e.getMessage());
        }
    }

    /**
     * 验证谷歌验证码
     * 
     * <p>验证用户输入的谷歌验证码是否正确</p>
     * <p>用于登录时的二次验证或敏感操作的验证</p>
     * 
     * @param verifyDTO 验证参数
     * @return 验证结果
     */
    @OperationLog(value = "验证谷歌验证码", type = OperationType.OTHER)
    @PostMapping("/verify")
    public Result<String> verifyCode(@Valid @RequestBody GoogleAuthVerifyDTO verifyDTO) {
        try {
            // 获取当前登录用户
            String currentUserId = StpUtil.getLoginIdAsString();
            User user = userService.getUserById(currentUserId);
            if (user == null) {
                log.warn("用户不存在, userId: {}", currentUserId);
                return Result.error("用户不存在");
            }

            // 检查用户是否已设置谷歌验证器
            if (!StringUtils.hasText(user.getGoogleAuthKey())) {
                log.warn("用户未设置谷歌验证器, userId: {}", currentUserId);
                return Result.error("请先设置谷歌验证器");
            }

            log.info("验证谷歌验证码, userId: {}, username: {}", currentUserId, user.getUsername());

            // 验证用户输入的验证码
            boolean isValid = GoogleAuthUtil.verifyTOTP(user.getGoogleAuthKey(), verifyDTO.getCode());
            if (isValid) {
                log.info("谷歌验证码验证成功, userId: {}", currentUserId);
                return Result.success("验证成功");
            } else {
                log.warn("谷歌验证码验证失败, userId: {}, code: {}", currentUserId, verifyDTO.getCode());
                return Result.error("验证码错误");
            }

        } catch (Exception e) {
            log.error("验证谷歌验证码失败, error: {}", e.getMessage(), e);
            return Result.error("验证失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前用户谷歌验证器状态
     * 
     * <p>检查当前用户是否已设置谷歌验证器</p>
     * 
     * @return 谷歌验证器状态
     */
    @OperationLog(value = "获取谷歌验证器状态", type = OperationType.QUERY)
    @GetMapping("/status")
    public Result<Boolean> getGoogleAuthStatus() {
        try {
            // 获取当前登录用户
            String currentUserId = StpUtil.getLoginIdAsString();
            User user = userService.getUserById(currentUserId);
            if (user == null) {
                log.warn("用户不存在, userId: {}", currentUserId);
                return Result.error("用户不存在");
            }

            boolean hasGoogleAuth = StringUtils.hasText(user.getGoogleAuthKey());
            log.info("获取谷歌验证器状态, userId: {}, hasGoogleAuth: {}", currentUserId, hasGoogleAuth);
            
            return Result.success("查询成功", hasGoogleAuth);

        } catch (Exception e) {
            log.error("获取谷歌验证器状态失败, error: {}", e.getMessage(), e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 重置谷歌验证器
     * 
     * <p>清除当前用户的谷歌验证器设置</p>
     * <p>重置后用户需要重新设置谷歌验证器</p>
     * 
     * @return 重置结果
     */
    @OperationLog(value = "重置谷歌验证器", type = OperationType.RESET)
    @GoogleAuthRequired
    @DeleteMapping("/reset")
    public Result<String> resetGoogleAuth() {
        try {
            // 获取当前登录用户
            String currentUserId = StpUtil.getLoginIdAsString();
            User user = userService.getUserById(currentUserId);
            if (user == null) {
                log.warn("用户不存在, userId: {}", currentUserId);
                return Result.error("用户不存在");
            }

            log.info("重置谷歌验证器, userId: {}, username: {}", currentUserId, user.getUsername());

            // 重置谷歌验证密钥
            boolean success = userService.resetGoogleAuthKey(currentUserId);
            if (success) {
                log.info("重置谷歌验证器成功, userId: {}", currentUserId);
                return Result.success("重置成功");
            } else {
                log.warn("重置谷歌验证器失败, userId: {}", currentUserId);
                return Result.error("重置失败");
            }

        } catch (Exception e) {
            log.error("重置谷歌验证器失败, error: {}", e.getMessage(), e);
            return Result.error("重置失败：" + e.getMessage());
        }
    }
}
