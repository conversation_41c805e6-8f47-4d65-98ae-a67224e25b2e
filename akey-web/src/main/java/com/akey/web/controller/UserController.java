package com.akey.web.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import com.akey.common.Result;
import com.akey.common.annotation.OperationLog;
import com.akey.common.annotation.OperationModule;
import com.akey.common.constant.SystemConstant;
import com.akey.common.enums.OperationType;
import com.akey.common.exception.BusinessException;
import com.akey.core.dto.UserChangePasswordDTO;
import com.akey.core.dto.UserCreateDTO;
import com.akey.core.dto.UserQueryDTO;
import com.akey.core.dto.UserResetPasswordDTO;
import com.akey.core.dto.UserUpdateDTO;
import com.akey.core.dao.entity.User;
import com.akey.core.dao.entity.UserType;
import com.akey.core.dao.service.UserService;
import com.akey.core.dao.service.UserTypeService;
import com.akey.core.vo.UserInfoVO;
import com.akey.core.vo.UserVO;
import com.akey.framework.web.annotation.GoogleAuthRequired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 用户管理控制器
 * 
 * <p>提供用户管理的完整功能</p>
 * <p>包括分页查询、修改、删除、锁定、重置密码、重置谷歌验证KEY等操作</p>
 * 
 * <AUTHOR>
 * @since 2025-07-23
 */
@Slf4j
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
@Validated
@OperationModule("用户管理")
public class UserController {

    private final UserService userService;
    private final UserTypeService userTypeService;

    /**
     * 分页查询用户列表
     * 
     * <p>支持多种筛选条件：用户名模糊查询、用户类型筛选、账户锁定状态筛选等</p>
     * <p>按创建时间倒序排列</p>
     * 
     * @param queryDTO 查询条件DTO
     * @return 分页结果
     */
    @SaCheckPermission(value = "user:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @GetMapping("/page")
    public Result<IPage<UserVO>> getUserPage(@Valid UserQueryDTO queryDTO) {
        try {
            log.info("分页查询用户列表, queryDTO: {}", queryDTO);
            
            // 构建分页参数
            Page<User> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
            
            // 调用服务层进行分页查询
            IPage<User> userPage = userService.getUserPageList(
                page, 
                queryDTO.getUsername(), 
                queryDTO.getUserTypeId(), 
                queryDTO.getAccountLocked() != null ? queryDTO.getAccountLocked().getValue() : null
            );

            // 转换为VO对象
            IPage<UserVO> result = userPage.convert(user -> {
                UserVO vo = new UserVO();
                BeanUtils.copyProperties(user, vo);

                // 设置是否有谷歌验证KEY（不返回具体的KEY值）
                vo.setHasGoogleAuthFromKey(user.getGoogleAuthKey());

                return vo;
            });
            
            log.info("分页查询用户列表成功, total: {}, pages: {}", result.getTotal(), result.getPages());
            return Result.success("查询成功", result);
            
        } catch (Exception e) {
            log.error("分页查询用户列表失败, queryDTO: {}, error: {}", queryDTO, e.getMessage(), e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取全部用户类型列表
     * 
     * <p>用于前端下拉框选择，方便根据用户类型ID映射类型名称</p>
     * 
     * @return 用户类型列表
     */
    @OperationLog(value = "获取用户类型列表", type = OperationType.QUERY)
    @SaCheckPermission(value = "user:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @GetMapping("/types")
    public Result<List<UserType>> getAllUserTypes() {
        try {
            log.info("获取全部用户类型列表");
            
            List<UserType> userTypes = userTypeService.getAllUserTypes(StpUtil.getLoginIdAsString());
            
            log.info("获取全部用户类型列表成功, count: {}", userTypes.size());
            return Result.success("查询成功", userTypes);
            
        } catch (Exception e) {
            log.error("获取全部用户类型列表失败, error: {}", e.getMessage(), e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前用户信息
     *
     * <p>获取当前登录用户的基本信息</p>
     * <p>用于用户登录后获取个人信息，包含用户类型名称等扩展信息</p>
     * <p>不返回密码和谷歌验证KEY等敏感信息</p>
     *
     * @return 当前用户信息
     */
    @OperationLog(value = "获取当前用户信息", type = OperationType.QUERY)
    @GetMapping("/info")
    public Result<UserInfoVO> getUserInfo() {
        try {
            // 获取当前登录用户ID
            String currentUserId = StpUtil.getLoginIdAsString();
            log.info("获取当前用户信息, userId: {}", currentUserId);
            // 查询用户信息
            User user = userService.getUserById(currentUserId);
            if (user == null) {
                log.warn("当前用户不存在, userId: {}", currentUserId);
                return Result.error("用户不存在");
            }

            // 转换为VO对象
            UserInfoVO userInfoVO = new UserInfoVO();
            BeanUtils.copyProperties(user, userInfoVO);

            // 设置用户类型名称
            UserType userType = userTypeService.getUserTypeById(user.getUserTypeId());
            if (userType != null) {
                userInfoVO.setUserTypeName(userType.getTypeName());
            }

            // 设置是否有谷歌验证KEY（不返回具体的KEY值）
            userInfoVO.setHasGoogleAuthFromKey(user.getGoogleAuthKey());

            log.info("获取当前用户信息成功, userId: {}, username: {}, userType: {}",
                    currentUserId, user.getUsername(), userInfoVO.getUserTypeName());
            return Result.success("查询成功", userInfoVO);

        } catch (Exception e) {
            log.error("获取当前用户信息失败, error: {}", e.getMessage(), e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 修改当前用户密码
     *
     * <p>用户自身修改密码，需要验证旧密码</p>
     * <p>新密码会进行加密处理后存储</p>
     * <p>修改成功后会更新密码修改时间</p>
     * <p>密码修改成功后会踢出用户的所有登录会话，强制重新登录</p>
     *
     * @param changePasswordDTO 修改密码参数DTO
     * @return 修改结果
     */
    @OperationLog(value = "修改旧密码", type = OperationType.UPDATE)
    @GoogleAuthRequired(required = false)
    @PostMapping("/change-password")
    public Result<String> changePassword(@Valid @RequestBody UserChangePasswordDTO changePasswordDTO) {
        String currentUserId = null;
        try {
            // 获取当前登录用户ID
            currentUserId = StpUtil.getLoginIdAsString();
            log.info("用户修改密码, userId: {}", currentUserId);

            // 验证新密码和确认密码是否一致
            if (!changePasswordDTO.getNewPassword().equals(changePasswordDTO.getConfirmPassword())) {
                log.warn("新密码和确认密码不一致, userId: {}", currentUserId);
                return Result.error("新密码和确认密码不一致");
            }

            // 调用服务层修改密码
            userService.changePassword(
                currentUserId,
                changePasswordDTO.getOldPassword(),
                changePasswordDTO.getNewPassword()
            );

            // 密码修改成功后，踢出用户的所有登录会话，强制重新登录
            try {
                StpUtil.kickout(currentUserId);
                log.info("用户修改密码成功，已踢出所有登录会话, userId: {}", currentUserId);
                return Result.success("密码修改成功，请重新登录");
            } catch (Exception e) {
                log.warn("踢出用户登录会话失败, userId: {}, error: {}", currentUserId, e.getMessage());
                // 踢出失败不影响密码修改的成功
                return Result.success("密码修改成功，建议重新登录");
            }

        } catch (BusinessException e) {
            log.warn("用户修改密码业务异常, userId: {}, error: {}", currentUserId, e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("用户修改密码失败, userId: {}, error: {}", currentUserId, e.getMessage(), e);
            return Result.error("密码修改失败：" + e.getMessage());
        }
    }

    /**
     * 新增用户
     *
     * <p>创建新的用户，包含用户名、密码、用户类型</p>
     * <p>会自动校验用户名的唯一性和用户类型的有效性</p>
     * <p>密码会进行加密处理后存储</p>
     *
     * @param createDTO 创建参数DTO
     * @return 创建结果
     */
    @OperationLog(value = "新增用户", type = OperationType.CREATE)
    @SaCheckPermission(value = "user:add", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @PostMapping
    public Result<String> createUser(@Valid @RequestBody UserCreateDTO createDTO) {
        try {
            log.info("新增用户, createDTO: {}", createDTO);

            // 转换为实体对象
            User user = new User();
            BeanUtils.copyProperties(createDTO, user);

            // 调用服务层创建
            userService.createUser(user);
            log.info("新增用户成功, username: {}", createDTO.getUsername());
            return Result.success("新增成功");

        } catch (BusinessException e) {
            log.warn("新增用户失败, createDTO: {}, error: {}", createDTO, e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("新增用户失败, createDTO: {}, error: {}", createDTO, e.getMessage(), e);
            return Result.error("新增失败：" + e.getMessage());
        }
    }

    /**
     * 修改用户信息
     *
     * <p>修改用户名、用户类型和账户锁定状态，密码有单独的重置接口</p>
     * <p>会自动校验用户名唯一性和用户类型的有效性</p>
     * <p>如果修改为锁定状态，会立即踢出该用户的所有登录会话</p>
     *
     * @param id 用户ID
     * @param updateDTO 修改参数DTO
     * @return 修改结果
     */
    @OperationLog(value = "修改用户信息", type = OperationType.UPDATE)
    @SaCheckPermission(value = "user:edit", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @PutMapping("/{id}")
    public Result<String> updateUser(@PathVariable @NotBlank(message = "用户ID不能为空") String id,
                                   @Valid @RequestBody UserUpdateDTO updateDTO) {
        try {
            log.info("修改用户信息, id: {}, updateDTO: {}", id, updateDTO);

            // 转换为实体对象
            User user = new User();
            BeanUtils.copyProperties(updateDTO, user);
            user.setId(id);

            // 调用服务层修改
            userService.updateUser(user);

            // 如果修改为锁定状态，踢出该用户的所有登录会话
            if (updateDTO.getAccountLocked() != null &&
                updateDTO.getAccountLocked().getValue() == 1) {
                try {
                    StpUtil.kickout(id);
                    log.info("用户已被锁定，已踢出所有登录会话, userId: {}", id);
                } catch (Exception e) {
                    log.warn("踢出用户登录会话失败, userId: {}, error: {}", id, e.getMessage());
                    // 踢出失败不影响修改操作的成功
                }
            }

            log.info("修改用户信息成功, id: {}, username: {}, userTypeId: {}, accountLocked: {}",
                    id, updateDTO.getUsername(), updateDTO.getUserTypeId(), updateDTO.getAccountLocked());

            String message = "修改成功";
            if (updateDTO.getAccountLocked() != null &&
                updateDTO.getAccountLocked().getValue() == 1) {
                message = "修改成功，用户已被锁定并踢出所有登录会话";
            }

            return Result.success(message);

        } catch (BusinessException e) {
            log.warn("修改用户信息失败, id: {}, error: {}", id, e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("修改用户信息失败, id: {}, updateDTO: {}, error: {}", id, updateDTO, e.getMessage(), e);
            return Result.error("修改失败：" + e.getMessage());
        }
    }

    /**
     * 重置用户密码
     *
     * <p>管理员重置用户密码为指定的新密码</p>
     * <p>重置后用户需要使用新密码登录</p>
     * <p>重置成功后会踢出该用户的所有登录会话，强制重新登录</p>
     *
     * @param id 用户ID
     * @param resetPasswordDTO 重置密码参数DTO
     * @return 重置结果
     */
    @OperationLog(value = "重置用户密码", type = OperationType.RESET)
    @GoogleAuthRequired
    @SaCheckPermission(value = "user:reset", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @PostMapping("/{id}/reset-password")
    public Result<String> resetPassword(@PathVariable @NotBlank(message = "用户ID不能为空") String id,
                                      @Valid @RequestBody UserResetPasswordDTO resetPasswordDTO) {
        try {
            log.info("重置用户密码, id: {}", id);

            // 调用服务层重置密码
            boolean success = userService.resetPassword(id, resetPasswordDTO.getNewPassword());

            if (success) {
                // 重置密码成功后，踢出该用户的所有登录会话，强制重新登录
                try {
                    StpUtil.kickout(id);
                    log.info("重置用户密码成功，已踢出所有登录会话, userId: {}", id);
                    return Result.success("重置密码成功，用户已被踢出所有登录会话");
                } catch (Exception e) {
                    log.warn("踢出用户登录会话失败, userId: {}, error: {}", id, e.getMessage());
                    // 踢出失败不影响重置密码的成功
                    return Result.success("重置密码成功，建议通知用户重新登录");
                }
            } else {
                log.warn("重置用户密码失败, id: {}", id);
                return Result.error("重置密码失败");
            }

        } catch (Exception e) {
            log.error("重置用户密码失败, id: {}, error: {}", id, e.getMessage(), e);
            return Result.error("重置密码失败：" + e.getMessage());
        }
    }

    /**
     * 重置用户谷歌验证KEY
     * 
     * <p>清除用户的谷歌验证KEY设置</p>
     * <p>重置后用户需要重新设置谷歌验证器</p>
     * 
     * @param id 用户ID
     * @return 重置结果
     */
    @OperationLog(value = "重置谷歌KEY", type = OperationType.RESET)
    @GoogleAuthRequired
    @SaCheckPermission(value = "user:reset", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @PostMapping("/{id}/reset-google-auth")
    public Result<String> resetGoogleAuthKey(@PathVariable @NotBlank(message = "用户ID不能为空") String id) {
        try {
            log.info("重置用户谷歌验证KEY, id: {}", id);
            
            // 调用服务层重置谷歌验证KEY
            boolean success = userService.resetGoogleAuthKey(id);
            
            if (success) {
                log.info("重置用户谷歌验证KEY成功, id: {}", id);
                return Result.success("重置谷歌验证KEY成功");
            } else {
                log.warn("重置用户谷歌验证KEY失败, id: {}", id);
                return Result.error("重置谷歌验证KEY失败");
            }
            
        } catch (Exception e) {
            log.error("重置用户谷歌验证KEY失败, id: {}, error: {}", id, e.getMessage(), e);
            return Result.error("重置谷歌验证KEY失败：" + e.getMessage());
        }
    }

    /**
     * 删除用户
     *
     * <p>删除指定用户，超级管理员用户不能删除</p>
     * <p>删除操作为逻辑删除，删除成功后会踢出该用户的所有登录会话</p>
     *
     * @param id 用户ID
     * @return 删除结果
     */
    @OperationLog(value = "删除用户", type = OperationType.DELETE)
    @GoogleAuthRequired
    @SaCheckPermission(value = "user:del", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @DeleteMapping("/{id}")
    public Result<String> deleteUser(@PathVariable @NotBlank(message = "用户ID不能为空") String id) {
        try {
            log.info("删除用户, id: {}", id);

            // 调用服务层删除
            boolean success = userService.deleteUser(id);

            if (success) {
                // 删除成功后，踢出该用户的所有登录会话
                try {
                    StpUtil.kickout(id);
                    log.info("已踢出用户的所有登录会话, userId: {}", id);
                } catch (Exception e) {
                    log.warn("踢出用户登录会话失败, userId: {}, error: {}", id, e.getMessage());
                    // 踢出失败不影响删除操作的成功
                }

                log.info("删除用户成功, id: {}", id);
                return Result.success("删除成功，已踢出所有登录会话");
            } else {
                log.warn("删除用户失败, id: {}", id);
                return Result.error("删除失败");
            }

        } catch (Exception e) {
            log.error("删除用户失败, id: {}, error: {}", id, e.getMessage(), e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除用户
     *
     * <p>批量删除指定的用户，超级管理员用户不能删除</p>
     * <p>删除操作为逻辑删除，删除成功后会踢出这些用户的所有登录会话</p>
     *
     * @param userIds 用户ID列表
     * @return 删除结果
     */
    @OperationLog(value = "批量删除用户", type = OperationType.DELETE)
    @GoogleAuthRequired
    @SaCheckPermission(value = "user:del", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @DeleteMapping("/batch")
    public Result<String> batchDeleteUsers(@RequestBody @NotEmpty(message = "用户ID列表不能为空") List<String> userIds) {
        try {
            log.info("批量删除用户, userIds: {}", userIds);

            // 逐个删除用户并踢出登录会话
            int successCount = 0;
            int kickoutCount = 0;

            for (String userId : userIds) {
                try {
                    // 删除单个用户
                    boolean deleteSuccess = userService.deleteUser(userId);
                    if (deleteSuccess) {
                        successCount++;

                        // 删除成功后踢出登录会话
                        try {
                            StpUtil.kickout(userId);
                            kickoutCount++;
                            log.debug("已踢出用户的所有登录会话, userId: {}", userId);
                        } catch (Exception e) {
                            log.warn("踢出用户登录会话失败, userId: {}, error: {}", userId, e.getMessage());
                            // 踢出失败不影响删除操作
                        }
                    }
                } catch (Exception e) {
                    log.error("批量删除用户失败, userId: {}, error: {}", userId, e.getMessage());
                }
            }

            log.info("批量删除用户完成, total: {}, success: {}, kickout: {}",
                    userIds.size(), successCount, kickoutCount);

            if (successCount == userIds.size()) {
                return Result.success("批量删除成功，已踢出所有登录会话");
            } else if (successCount > 0) {
                return Result.success(String.format("批量删除部分成功，成功删除%d个用户，共%d个用户，已踢出登录会话",
                        successCount, userIds.size()));
            } else {
                return Result.error("批量删除失败");
            }

        } catch (Exception e) {
            log.error("批量删除用户失败, userIds: {}, error: {}", userIds, e.getMessage(), e);
            return Result.error("批量删除失败：" + e.getMessage());
        }
    }

    /**
     * 锁定用户
     *
     * <p>锁定指定用户，锁定后用户无法登录</p>
     * <p>锁定成功后会立即踢出该用户的所有登录会话</p>
     *
     * @param id 用户ID
     * @return 锁定结果
     */
    @OperationLog(value = "冻结用户", type = OperationType.UPDATE)
    @GoogleAuthRequired
    @SaCheckPermission(value = "user:lock", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @PostMapping("/{id}/lock")
    public Result<String> lockUser(@PathVariable @NotBlank(message = "用户ID不能为空") String id) {
        try {
            log.info("锁定用户, id: {}", id);

            // 调用服务层锁定用户
            boolean success = userService.setUserLockStatus(id, 1);

            if (success) {
                // 锁定成功后，踢出该用户的所有登录会话
                try {
                    StpUtil.kickout(id);
                    log.info("已踢出用户的所有登录会话, userId: {}", id);
                } catch (Exception e) {
                    log.warn("踢出用户登录会话失败, userId: {}, error: {}", id, e.getMessage());
                    // 踢出失败不影响锁定操作的成功
                }

                log.info("锁定用户成功, id: {}", id);
                return Result.success("锁定成功，已踢出所有登录会话");
            } else {
                log.warn("锁定用户失败, id: {}", id);
                return Result.error("锁定失败");
            }

        } catch (Exception e) {
            log.error("锁定用户失败, id: {}, error: {}", id, e.getMessage(), e);
            return Result.error("锁定失败：" + e.getMessage());
        }
    }

    /**
     * 解锁用户
     *
     * <p>解锁指定用户，解锁后用户可以正常登录</p>
     *
     * @param id 用户ID
     * @return 解锁结果
     */
    @OperationLog(value = "解锁用户", type = OperationType.UPDATE)
    @GoogleAuthRequired
    @SaCheckPermission(value = "user:unlock", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @PostMapping("/{id}/unlock")
    public Result<String> unlockUser(@PathVariable @NotBlank(message = "用户ID不能为空") String id) {
        try {
            log.info("解锁用户, id: {}", id);

            // 调用服务层解锁用户
            boolean success = userService.setUserLockStatus(id, 0);

            if (success) {
                log.info("解锁用户成功, id: {}", id);
                return Result.success("解锁成功");
            } else {
                log.warn("解锁用户失败, id: {}", id);
                return Result.error("解锁失败");
            }

        } catch (Exception e) {
            log.error("解锁用户失败, id: {}, error: {}", id, e.getMessage(), e);
            return Result.error("解锁失败：" + e.getMessage());
        }
    }

    /**
     * 批量锁定用户
     *
     * <p>批量锁定指定的用户，锁定后用户无法登录</p>
     * <p>锁定成功后会立即踢出这些用户的所有登录会话</p>
     *
     * @param userIds 用户ID列表
     * @return 锁定结果
     */
    @OperationLog(value = "批量冻结用户", type = OperationType.UPDATE)
    @GoogleAuthRequired
    @SaCheckPermission(value = "user:lock", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @PostMapping("/batch/lock")
    public Result<String> batchLockUsers(@RequestBody @NotEmpty(message = "用户ID列表不能为空") List<String> userIds) {
        try {
            log.info("批量锁定用户, userIds: {}", userIds);

            // 逐个锁定用户并踢出登录会话
            int successCount = 0;
            int kickoutCount = 0;

            for (String userId : userIds) {
                try {
                    // 锁定单个用户
                    boolean lockSuccess = userService.setUserLockStatus(userId, 1);
                    if (lockSuccess) {
                        successCount++;

                        // 锁定成功后踢出登录会话
                        try {
                            StpUtil.kickout(userId);
                            kickoutCount++;
                            log.debug("已踢出用户的所有登录会话, userId: {}", userId);
                        } catch (Exception e) {
                            log.warn("踢出用户登录会话失败, userId: {}, error: {}", userId, e.getMessage());
                            // 踢出失败不影响锁定操作
                        }
                    }
                } catch (Exception e) {
                    log.error("批量锁定用户失败, userId: {}, error: {}", userId, e.getMessage());
                }
            }

            log.info("批量锁定用户完成, total: {}, success: {}, kickout: {}",
                    userIds.size(), successCount, kickoutCount);

            if (successCount == userIds.size()) {
                return Result.success("批量锁定成功，已踢出所有登录会话");
            } else if (successCount > 0) {
                return Result.success(String.format("批量锁定部分成功，成功锁定%d个用户，共%d个用户，已踢出登录会话",
                        successCount, userIds.size()));
            } else {
                return Result.error("批量锁定失败");
            }

        } catch (Exception e) {
            log.error("批量锁定用户失败, userIds: {}, error: {}", userIds, e.getMessage(), e);
            return Result.error("批量锁定失败：" + e.getMessage());
        }
    }

    /**
     * 批量解锁用户
     *
     * <p>批量解锁指定的用户，解锁后用户可以正常登录</p>
     *
     * @param userIds 用户ID列表
     * @return 解锁结果
     */
    @OperationLog(value = "批量解锁用户", type = OperationType.UPDATE)
    @GoogleAuthRequired
    @SaCheckPermission(value = "user:unlock", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @PostMapping("/batch/unlock")
    public Result<String> batchUnlockUsers(@RequestBody @NotEmpty(message = "用户ID列表不能为空") List<String> userIds) {
        try {
            log.info("批量解锁用户, userIds: {}", userIds);

            // 调用服务层批量解锁
            int successCount = userService.batchSetUserLockStatus(userIds, 0);

            log.info("批量解锁用户完成, total: {}, success: {}", userIds.size(), successCount);

            if (successCount == userIds.size()) {
                return Result.success("批量解锁成功");
            } else if (successCount > 0) {
                return Result.success(String.format("批量解锁部分成功，成功解锁%d个用户，共%d个用户", successCount, userIds.size()));
            } else {
                return Result.error("批量解锁失败");
            }

        } catch (Exception e) {
            log.error("批量解锁用户失败, userIds: {}, error: {}", userIds, e.getMessage(), e);
            return Result.error("批量解锁失败：" + e.getMessage());
        }
    }
}