package com.akey.web.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import com.akey.common.Result;
import com.akey.common.annotation.OperationLog;
import com.akey.common.annotation.OperationModule;
import com.akey.common.constant.SystemConstant;
import com.akey.common.enums.OperationType;
import com.akey.core.dao.entity.User;
import com.akey.core.dao.service.UserService;
import com.akey.framework.web.service.SystemResourceService;
import com.akey.framework.web.vo.SystemResourceVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 系统资源监控控制器
 *
 * <p>提供系统资源监控的RESTful API接口</p>
 * <p>获取完整的系统资源信息，包括CPU、内存、磁盘、JVM、服务器等所有信息</p>
 * <p>使用OSHI库获取详细的系统信息，在OSHI不可用时降级到Java标准库</p>
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Slf4j
@RestController
@OperationModule("系统资源管理")
@RequestMapping("/system/resource")
@RequiredArgsConstructor
public class SystemResourceController {

    private final UserService userService;
    private final SystemResourceService systemResourceService;

    /**
     * 获取完整的系统资源信息
     *
     * <p>包含以下信息模块：</p>
     * <ul>
     *   <li>CPU信息：型号、核心数、使用率、负载等</li>
     *   <li>内存信息：系统内存和JVM内存详细信息</li>
     *   <li>服务器信息：主机名、操作系统、IP地址、启动时间等</li>
     *   <li>JVM信息：版本、启动参数、类加载、线程等</li>
     *   <li>磁盘信息：分区、使用情况、I/O统计等</li>
     * </ul>
     *
     * @return 完整的系统资源信息
     */
    @OperationLog(value = "获取系统资源信息", type = OperationType.QUERY)
    @SaCheckPermission(value = "sys:resource:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @GetMapping
    public Result<SystemResourceVO> getSystemResource() {
        log.debug("获取完整系统资源信息");
        try {
            // 获取当前登录用户ID
            String loginIdAsString = StpUtil.getLoginIdAsString();
            // 获取用户信息
            User user = userService.getUserById(loginIdAsString);
            SystemResourceVO systemResource = systemResourceService.getSystemResource();

            // 判断用户不是超级管理员，隐藏敏感信息
            if (!user.getUserTypeId().equals(SystemConstant.ADMIN_USER_TYPE_ID)) {
                filterSensitiveInfo(systemResource);
                log.debug("已为非超级管理员用户过滤敏感信息，用户ID: {}", loginIdAsString);
            }
            log.debug("系统资源信息获取成功，耗时: {}ms", systemResource.getCollectDuration());
            return Result.success(systemResource);
        } catch (Exception e) {
            log.error("获取系统资源信息失败", e);
            return Result.error("获取系统资源信息失败: " + e.getMessage());
        }
    }

    /**
     * 为非超级管理员用户过滤敏感信息
     *
     * @param systemResource 系统资源信息
     */
    private void filterSensitiveInfo(SystemResourceVO systemResource) {
        if (systemResource == null) {
            return;
        }

        // 过滤服务器敏感信息
        if (systemResource.getServerInfo() != null) {
            var serverInfo = systemResource.getServerInfo();

            // 隐藏真实主机名，只显示脱敏版本
            if (serverInfo.getHostname() != null) {
                serverInfo.setHostname(maskSensitiveString(serverInfo.getHostname()));
            }

            // 隐藏内网IP地址
            serverInfo.setInternalIp("***.***.***.**");

            // 隐藏外网IP地址
            /*if (!"N/A".equals(serverInfo.getExternalIp())) {
                serverInfo.setExternalIp("***.***.***.***");
            }*/

            // 隐藏详细IP地址列表
            if (serverInfo.getIpAddresses() != null && !serverInfo.getIpAddresses().isEmpty()) {
                // 创建新的可修改列表来替换原有的集合
                List<String> maskedIps = new ArrayList<>();
                maskedIps.add("***.***.***.**");
                serverInfo.setIpAddresses(maskedIps);
            }

            // 隐藏操作系统详细版本信息
            if (serverInfo.getOsVersion() != null) {
                serverInfo.setOsVersion(maskOsVersion(serverInfo.getOsVersion()));
            }

            // 隐藏系统架构详细信息
            if (serverInfo.getOsArch() != null) {
                serverInfo.setOsArch(maskSensitiveString(serverInfo.getOsArch()));
            }
        }

        // 过滤CPU敏感信息
        if (systemResource.getCpuInfo() != null) {
            var cpuInfo = systemResource.getCpuInfo();

            // 隐藏CPU详细型号，只保留厂商信息
            if (cpuInfo.getModel() != null) {
                cpuInfo.setModel(maskCpuModel(cpuInfo.getModel()));
            }

            // 隐藏CPU架构详细信息
            if (cpuInfo.getArchitecture() != null) {
                cpuInfo.setArchitecture("***");
            }

            // 隐藏CPU系列和步进信息
            cpuInfo.setFamily("***");
            cpuInfo.setStepping("***");
        }

        // 过滤JVM敏感信息
        if (systemResource.getJvmInfo() != null) {
            var jvmInfo = systemResource.getJvmInfo();

            // 隐藏JVM启动参数（可能包含敏感配置）
            if (jvmInfo.getJvmArguments() != null && !jvmInfo.getJvmArguments().isEmpty()) {
                // 创建新的可修改列表来替换原有的不可修改集合
                List<String> maskedArgs = new ArrayList<>();
                maskedArgs.add("*** (已隐藏敏感参数，共" + jvmInfo.getJvmArguments().size() + "个参数)");
                jvmInfo.setJvmArguments(maskedArgs);
            }

            // 隐藏Java类路径（可能包含敏感路径信息）
            if (jvmInfo.getClassPath() != null) {
                jvmInfo.setClassPath("*** (已隐藏敏感路径)");
            }

            // 隐藏Java库路径
            if (jvmInfo.getLibraryPath() != null) {
                jvmInfo.setLibraryPath("*** (已隐藏敏感路径)");
            }

            // 隐藏Java主目录路径
            if (jvmInfo.getJavaHome() != null) {
                jvmInfo.setJavaHome(maskSensitiveString(jvmInfo.getJavaHome()));
            }

            // 过滤系统属性中的敏感信息
            if (jvmInfo.getSystemProperties() != null) {
                var sysProps = jvmInfo.getSystemProperties();

                // 隐藏用户名
                if (sysProps.getUserName() != null) {
                    sysProps.setUserName(maskSensitiveString(sysProps.getUserName()));
                }

                // 隐藏用户主目录
                if (sysProps.getUserHome() != null) {
                    sysProps.setUserHome("/Users/<USER>");
                }

                // 隐藏用户工作目录
                if (sysProps.getUserDir() != null) {
                    sysProps.setUserDir("/***");
                }
            }
        }

        // 过滤磁盘敏感信息
        if (systemResource.getDiskInfo() != null &&
            systemResource.getDiskInfo().getPartitions() != null) {

            for (var partition : systemResource.getDiskInfo().getPartitions()) {
                // 隐藏设备真实路径
                if (partition.getDeviceName() != null) {
                    partition.setDeviceName(maskDeviceName(partition.getDeviceName()));
                }

                // 隐藏挂载点详细路径
                if (partition.getMountPoint() != null) {
                    partition.setMountPoint(maskMountPoint(partition.getMountPoint()));
                }
            }
        }

        // 添加过滤标记
        systemResource.setWarningMessage("部分敏感信息已隐藏");
    }

    /**
     * 脱敏字符串，保留前后字符，中间用*替换
     */
    private String maskSensitiveString(String str) {
        if (str == null || str.length() <= 2) {
            return "***";
        }
        if (str.length() <= 4) {
            return str.charAt(0) + "**" + str.charAt(str.length() - 1);
        }
        return str.substring(0, 2) + "***" + str.substring(str.length() - 2);
    }

    /**
     * 脱敏操作系统版本信息
     */
    private String maskOsVersion(String osVersion) {
        if (osVersion == null) {
            return "***";
        }
        // 保留主要版本号，隐藏详细构建信息
        String[] parts = osVersion.split("\\s+");
        if (parts.length > 0) {
            return parts[0] + " ***";
        }
        return "***";
    }

    /**
     * 脱敏CPU型号信息
     */
    private String maskCpuModel(String cpuModel) {
        if (cpuModel == null) {
            return "***";
        }
        // 保留厂商名称，隐藏具体型号
        if (cpuModel.toLowerCase().contains("intel")) {
            return "Intel *** CPU";
        } else if (cpuModel.toLowerCase().contains("amd")) {
            return "AMD *** CPU";
        } else if (cpuModel.toLowerCase().contains("apple")) {
            return "Apple *** CPU";
        }
        return "*** CPU";
    }

    /**
     * 脱敏设备名称
     */
    private String maskDeviceName(String deviceName) {
        if (deviceName == null) {
            return "***";
        }
        // 保留设备类型，隐藏具体路径
        if (deviceName.startsWith("/dev/")) {
            return "/dev/***";
        }
        return maskSensitiveString(deviceName);
    }

    /**
     * 脱敏挂载点
     */
    private String maskMountPoint(String mountPoint) {
        if (mountPoint == null) {
            return "***";
        }
        // 保留根目录和常见系统目录，隐藏用户目录
        if ("/".equals(mountPoint) ||
            mountPoint.startsWith("/System") ||
            mountPoint.startsWith("/usr") ||
            mountPoint.startsWith("/var")) {
            return mountPoint;
        }
        return "/***";
    }
}
