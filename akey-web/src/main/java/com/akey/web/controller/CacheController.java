package com.akey.web.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.akey.common.constant.SystemConstant;
import com.akey.common.result.Result;
import com.akey.core.cache.CacheMonitorService;
import com.akey.core.cache.UserCacheService;
import com.akey.framework.core.log.annotation.OperationLog;
import com.akey.framework.core.log.enums.OperationType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.Map;
import java.util.Set;

/**
 * 缓存管理Controller
 * 
 * <p>功能说明：</p>
 * <ul>
 *   <li>提供缓存统计信息查询</li>
 *   <li>提供缓存清理功能</li>
 *   <li>提供缓存监控功能</li>
 * </ul>
 * 
 * <p>权限要求：</p>
 * <ul>
 *   <li>只有超级管理员可以访问</li>
 *   <li>所有操作都会记录操作日志</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-08-03
 * @version 1.0
 */
@Slf4j
@RestController
@RequestMapping("/api/cache")
@RequiredArgsConstructor
@Validated
public class CacheController {

    private final CacheMonitorService cacheMonitorService;
    private final UserCacheService userCacheService;

    /**
     * 获取缓存统计信息
     * 
     * @return 缓存统计信息
     */
    @SaCheckPermission(value = "cache:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getCacheStatistics() {
        try {
            log.info("获取缓存统计信息");
            
            Map<String, Object> statistics = cacheMonitorService.getCacheStatistics();
            
            return Result.success(statistics);
            
        } catch (Exception e) {
            log.error("获取缓存统计信息失败", e);
            return Result.error("获取缓存统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 清除指定用户的缓存
     * 
     * @param userId 用户ID
     * @return 操作结果
     */
    @OperationLog(value = "清除用户缓存", type = OperationType.DELETE)
    @SaCheckPermission(value = "cache:clear", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @DeleteMapping("/user/{userId}")
    public Result<String> clearUserCache(@PathVariable @NotBlank(message = "用户ID不能为空") String userId) {
        try {
            log.info("清除用户缓存, userId: {}", userId);
            
            userCacheService.clearUserCache(userId);
            
            return Result.success("清除用户缓存成功");
            
        } catch (Exception e) {
            log.error("清除用户缓存失败, userId: {}", userId, e);
            return Result.error("清除用户缓存失败：" + e.getMessage());
        }
    }

    /**
     * 清除指定用户类型的缓存
     * 
     * @param userTypeId 用户类型ID
     * @return 操作结果
     */
    @OperationLog(value = "清除用户类型缓存", type = OperationType.DELETE)
    @SaCheckPermission(value = "cache:clear", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @DeleteMapping("/usertype/{userTypeId}")
    public Result<String> clearUserTypeCache(@PathVariable @NotBlank(message = "用户类型ID不能为空") String userTypeId) {
        try {
            log.info("清除用户类型缓存, userTypeId: {}", userTypeId);
            
            userCacheService.clearUserTypeCache(userTypeId);
            
            return Result.success("清除用户类型缓存成功");
            
        } catch (Exception e) {
            log.error("清除用户类型缓存失败, userTypeId: {}", userTypeId, e);
            return Result.error("清除用户类型缓存失败：" + e.getMessage());
        }
    }

    /**
     * 清除所有用户相关缓存
     * 
     * @return 操作结果
     */
    @OperationLog(value = "清除所有用户缓存", type = OperationType.DELETE)
    @SaCheckPermission(value = "cache:clear", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @DeleteMapping("/users/all")
    public Result<String> clearAllUserCache() {
        try {
            log.info("清除所有用户相关缓存");
            
            long count = cacheMonitorService.clearAllUserCache();
            
            return Result.success("清除所有用户相关缓存成功，清除数量：" + count);
            
        } catch (Exception e) {
            log.error("清除所有用户相关缓存失败", e);
            return Result.error("清除所有用户相关缓存失败：" + e.getMessage());
        }
    }

    /**
     * 清除所有权限相关缓存
     * 
     * @return 操作结果
     */
    @OperationLog(value = "清除所有权限缓存", type = OperationType.DELETE)
    @SaCheckPermission(value = "cache:clear", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @DeleteMapping("/permissions/all")
    public Result<String> clearAllPermissionsCache() {
        try {
            log.info("清除所有权限相关缓存");
            
            long count = cacheMonitorService.clearAllPermissionsCache();
            
            return Result.success("清除所有权限相关缓存成功，清除数量：" + count);
            
        } catch (Exception e) {
            log.error("清除所有权限相关缓存失败", e);
            return Result.error("清除所有权限相关缓存失败：" + e.getMessage());
        }
    }

    /**
     * 清除所有缓存
     * 
     * @return 操作结果
     */
    @OperationLog(value = "清除所有缓存", type = OperationType.DELETE)
    @SaCheckPermission(value = "cache:clear", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @DeleteMapping("/all")
    public Result<String> clearAllCache() {
        try {
            log.info("清除所有缓存");
            
            long count = cacheMonitorService.clearAllCache();
            
            return Result.success("清除所有缓存成功，清除数量：" + count);
            
        } catch (Exception e) {
            log.error("清除所有缓存失败", e);
            return Result.error("清除所有缓存失败：" + e.getMessage());
        }
    }

    /**
     * 获取指定模式的缓存Key列表
     * 
     * @param pattern 匹配模式
     * @return 缓存Key列表
     */
    @SaCheckPermission(value = "cache:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @GetMapping("/keys")
    public Result<Set<String>> getCacheKeys(@RequestParam @NotBlank(message = "匹配模式不能为空") String pattern) {
        try {
            log.info("获取缓存Key列表, pattern: {}", pattern);
            
            Set<String> keys = cacheMonitorService.getCacheKeys(pattern);
            
            return Result.success(keys);
            
        } catch (Exception e) {
            log.error("获取缓存Key列表失败, pattern: {}", pattern, e);
            return Result.error("获取缓存Key列表失败：" + e.getMessage());
        }
    }

    /**
     * 检查缓存Key是否存在
     * 
     * @param key 缓存Key
     * @return 存在返回true，不存在返回false
     */
    @SaCheckPermission(value = "cache:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @GetMapping("/exists")
    public Result<Boolean> hasKey(@RequestParam @NotBlank(message = "缓存Key不能为空") String key) {
        try {
            log.info("检查缓存Key是否存在, key: {}", key);
            
            boolean exists = cacheMonitorService.hasKey(key);
            
            return Result.success(exists);
            
        } catch (Exception e) {
            log.error("检查缓存Key是否存在失败, key: {}", key, e);
            return Result.error("检查缓存Key是否存在失败：" + e.getMessage());
        }
    }

    /**
     * 获取缓存Key的过期时间
     * 
     * @param key 缓存Key
     * @return 过期时间（秒），-1表示永不过期，-2表示Key不存在
     */
    @SaCheckPermission(value = "cache:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @GetMapping("/expire")
    public Result<Long> getExpire(@RequestParam @NotBlank(message = "缓存Key不能为空") String key) {
        try {
            log.info("获取缓存Key过期时间, key: {}", key);
            
            long expire = cacheMonitorService.getExpire(key);
            
            return Result.success(expire);
            
        } catch (Exception e) {
            log.error("获取缓存Key过期时间失败, key: {}", key, e);
            return Result.error("获取缓存Key过期时间失败：" + e.getMessage());
        }
    }

    /**
     * 预热缓存
     * 
     * @return 操作结果
     */
    @OperationLog(value = "预热缓存", type = OperationType.OTHER)
    @SaCheckPermission(value = "cache:warmup", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @PostMapping("/warmup")
    public Result<String> warmUpCache() {
        try {
            log.info("预热缓存");
            
            cacheMonitorService.warmUpCache();
            
            return Result.success("预热缓存成功");
            
        } catch (Exception e) {
            log.error("预热缓存失败", e);
            return Result.error("预热缓存失败：" + e.getMessage());
        }
    }
}
