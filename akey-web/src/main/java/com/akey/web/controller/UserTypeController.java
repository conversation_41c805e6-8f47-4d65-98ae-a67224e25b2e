package com.akey.web.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import com.akey.common.Result;
import com.akey.common.annotation.OperationLog;
import com.akey.common.annotation.OperationModule;
import com.akey.common.constant.SystemConstant;
import com.akey.common.enums.OperationType;
import com.akey.common.exception.BusinessException;
import com.akey.core.dto.UserTypeAssignMenuDTO;
import com.akey.core.dto.UserTypeCreateDTO;
import com.akey.core.dto.UserTypeQueryDTO;
import com.akey.core.dto.UserTypeUpdateDTO;
import com.akey.core.dao.entity.Menu;
import com.akey.core.dao.entity.User;
import com.akey.core.dao.entity.UserType;
import com.akey.core.dao.service.MenuService;
import com.akey.core.dao.service.UserService;
import com.akey.core.dao.service.UserTypeMenuService;
import com.akey.core.dao.service.UserTypeService;
import com.akey.core.vo.UserTypeVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 用户类型控制器
 * 
 * <p>提供用户类型的CRUD操作和菜单权限分配功能</p>
 * <p>包含分页查询、新增、修改、删除、菜单分配等功能</p>
 * <p>使用Sa-Token进行权限控制，确保操作安全性</p>
 * 
 * <AUTHOR>
 * @since 2025-07-16
 */
@Slf4j
@RestController
@RequestMapping("/userType")
@RequiredArgsConstructor
@Validated
@OperationModule("用户类型管理")
public class UserTypeController {

    private final UserTypeService userTypeService;
    private final UserTypeMenuService userTypeMenuService;
    private final UserService userService;
    private final MenuService menuService;

    /**
     * 分页查询用户类型列表
     * 
     * <p>支持多种筛选条件：类型名称模糊查询、是否内置筛选等</p>
     * <p>内置类型优先显示，然后按创建时间倒序排列</p>
     * 
     * @param queryDTO 查询条件DTO
     * @return 分页结果
     */
    @SaCheckPermission(value = "user:type:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @GetMapping("/page")
    public Result<IPage<UserTypeVO>> getUserTypePage(@Valid UserTypeQueryDTO queryDTO) {
        try {
            log.info("分页查询用户类型列表, queryDTO: {}", queryDTO);
            
            // 构建分页参数
            Page<UserType> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
            
            // 获取当前用户ID
            String currentUserId = StpUtil.getLoginIdAsString();

            // 调用服务层进行分页查询，根据用户权限过滤
            IPage<UserType> userTypePage = getUserTypePageWithPermissionFilter(
                page, queryDTO, currentUserId
            );

            // 转换为VO对象
            IPage<UserTypeVO> result = userTypePage.convert(userType -> {
                UserTypeVO vo = new UserTypeVO();
                BeanUtils.copyProperties(userType, vo);

                // 统计关联用户数量
                vo.setUserCount(getUserCountByUserType(userType.getId()));

                // 统计关联菜单数量 - 根据用户权限决定
                vo.setMenuCount(getMenuCountByUserType(userType.getId(), currentUserId));

                return vo;
            });
            
            log.info("分页查询用户类型列表成功, total: {}, pages: {}", result.getTotal(), result.getPages());
            return Result.success("查询成功", result);
            
        } catch (Exception e) {
            log.error("分页查询用户类型列表失败, queryDTO: {}, error: {}", queryDTO, e.getMessage(), e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 新增用户类型
     * 
     * <p>创建新的用户类型，支持设置类型名称和是否内置</p>
     * <p>会自动校验类型名称的唯一性</p>
     * 
     * @param createDTO 创建参数DTO
     * @return 创建结果
     */
    @OperationLog(value = "新增用户类型", type = OperationType.CREATE)
    @SaCheckPermission(value = "user:type:add", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @PostMapping
    public Result<String> createUserType(@Valid @RequestBody UserTypeCreateDTO createDTO) {
        try {
            log.info("新增用户类型, createDTO: {}", createDTO);
            
            // 转换为实体对象
            UserType userType = new UserType();
            BeanUtils.copyProperties(createDTO, userType);
            
            // 调用服务层创建
            boolean success = userTypeService.createUserType(userType);
            
            if (success) {
                log.info("新增用户类型成功, typeName: {}", createDTO.getTypeName());
                return Result.success("新增成功");
            } else {
                log.warn("新增用户类型失败, typeName: {}", createDTO.getTypeName());
                return Result.error("新增失败");
            }
            
        } catch (BusinessException e) {
            log.warn("新增用户类型业务异常, createDTO: {}, error: {}", createDTO, e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("新增用户类型失败, createDTO: {}, error: {}", createDTO, e.getMessage(), e);
            return Result.error("新增失败：" + e.getMessage());
        }
    }

    /**
     * 修改用户类型
     * 
     * <p>修改指定用户类型的信息</p>
     * <p>内置类型的某些字段可能不允许修改</p>
     * 
     * @param id 用户类型ID
     * @param updateDTO 修改参数DTO
     * @return 修改结果
     */
    @OperationLog(value = "修改用户类型", type = OperationType.UPDATE)
    @SaCheckPermission(value = "user:type:edit", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @PutMapping("/{id}")
    public Result<String> updateUserType(@PathVariable @NotBlank(message = "用户类型ID不能为空") String id,
                                        @Valid @RequestBody UserTypeUpdateDTO updateDTO) {
        try {
            log.info("修改用户类型, id: {}, updateDTO: {}", id, updateDTO);
            
            // 转换为实体对象
            UserType userType = new UserType();
            BeanUtils.copyProperties(updateDTO, userType);
            userType.setId(id);
            
            // 调用服务层修改
            boolean success = userTypeService.updateUserType(userType);
            
            if (success) {
                log.info("修改用户类型成功, id: {}, typeName: {}", id, updateDTO.getTypeName());
                return Result.success("修改成功");
            } else {
                log.warn("修改用户类型失败, id: {}, typeName: {}", id, updateDTO.getTypeName());
                return Result.error("修改失败");
            }
            
        } catch (BusinessException e) {
            log.warn("修改用户类型业务异常, id: {}, updateDTO: {}, error: {}", id, updateDTO, e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("修改用户类型失败, id: {}, updateDTO: {}, error: {}", id, updateDTO, e.getMessage(), e);
            return Result.error("修改失败：" + e.getMessage());
        }
    }

    /**
     * 删除用户类型
     * 
     * <p>删除指定的用户类型</p>
     * <p>内置类型不允许删除，有关联用户的类型也不允许删除</p>
     * 
     * @param id 用户类型ID
     * @return 删除结果
     */
    @OperationLog(value = "删除用户类型", type = OperationType.DELETE)
    @SaCheckPermission(value = "user:type:delete", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @DeleteMapping("/{id}")
    public Result<String> deleteUserType(@PathVariable @NotBlank(message = "用户类型ID不能为空") String id) {
        try {
            log.info("删除用户类型, id: {}", id);
            
            // 调用服务层删除
            boolean success = userTypeService.deleteUserType(id);
            
            if (success) {
                log.info("删除用户类型成功, id: {}", id);
                return Result.success("删除成功");
            } else {
                log.warn("删除用户类型失败, id: {}", id);
                return Result.error("删除失败");
            }
            
        } catch (BusinessException e) {
            log.warn("删除用户类型业务异常, id: {}, error: {}", id, e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("删除用户类型失败, id: {}, error: {}", id, e.getMessage(), e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 为用户类型分配菜单权限
     * 
     * <p>批量分配菜单权限给指定用户类型</p>
     * <p>采用先清空再分配的策略，确保权限配置的准确性</p>
     * 
     * @param id 用户类型ID
     * @param assignMenuDTO 分配菜单参数DTO
     * @return 分配结果
     */
    @OperationLog(value = "权限分配", type = OperationType.UPDATE)
    @SaCheckPermission(value = "user:type:assignMenu", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @PostMapping("/{id}/assignMenus")
    public Result<String> assignMenusToUserType(@PathVariable @NotBlank(message = "用户类型ID不能为空") String id,
                                               @Valid @RequestBody UserTypeAssignMenuDTO assignMenuDTO) {
        try {
            log.info("为用户类型分配菜单权限, id: {}, menuIds: {}", id, assignMenuDTO.getMenuIds());
            
            // 调用服务层分配菜单
            boolean success = userTypeMenuService.assignMenusToUserType(id, assignMenuDTO.getMenuIds());
            
            if (success) {
                log.info("为用户类型分配菜单权限成功, id: {}, menuCount: {}", id, assignMenuDTO.getMenuIds().size());
                return Result.success("分配成功");
            } else {
                log.warn("为用户类型分配菜单权限失败, id: {}", id);
                return Result.error("分配失败");
            }
            
        } catch (BusinessException e) {
            log.warn("为用户类型分配菜单权限业务异常, id: {}, error: {}", id, e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("为用户类型分配菜单权限失败, id: {}, error: {}", id, e.getMessage(), e);
            return Result.error("分配失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户类型已分配的菜单ID列表
     * 
     * <p>返回指定用户类型已分配的所有菜单ID以及所有菜单ID</p>
     * <p>用于前端回显已选择的菜单权限</p>
     * 
     * @param id 用户类型ID
     * @return 菜单ID列表
     */
    @OperationLog(value = "获取用户类型已分配菜单", type = OperationType.QUERY)
    @SaCheckPermission(value = "user:type:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @GetMapping("/{id}/menus")
    public Result<Map<String, Object>> getUserTypeMenus(@PathVariable @NotBlank(message = "用户类型ID不能为空") String id) {
        try {
            log.info("获取用户类型已分配的菜单列表, id: {}", id);
            // 获取所有菜单ID
            List<Menu> allMenuIds = menuService.getMenuList(null, null, null);

            // 调用服务层获取菜单ID列表
            List<String> menuIds = null;
            // 判断是否超级管理员类型
            if (SystemConstant.ADMIN_USER_TYPE_ID.equals(id)) {
                menuIds = allMenuIds.stream().map(Menu::getId).collect(Collectors.toList());
            } else {
                menuIds = userTypeMenuService.getMenuIdsByUserType(id);
            }

            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("menus", menuIds);
            resultMap.put("allMenus", allMenuIds);
            return Result.success("查询成功", resultMap);
        } catch (Exception e) {
            log.error("获取用户类型已分配的菜单列表失败, id: {}, error: {}", id, e.getMessage(), e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有用户类型列表
     * 
     * <p>返回所有用户类型的基本信息</p>
     * <p>用于下拉选择等场景</p>
     * 
     * @return 用户类型列表
     */
    @OperationLog(value = "获取所以用户类型列表", type = OperationType.QUERY)
    @SaCheckPermission(value = "user:type:view", orRole = SystemConstant.ADMIN_USER_TYPE_ID)
    @GetMapping("/list")
    public Result<List<UserTypeVO>> getAllUserTypes() {
        try {
            log.info("获取所有用户类型列表");
            
            // 调用服务层获取所有用户类型
            List<UserType> userTypes = userTypeService.getAllUserTypes();
            
            // 获取当前用户ID
            String currentUserId = StpUtil.getLoginIdAsString();

            // 根据用户权限过滤用户类型列表
            List<UserType> filteredUserTypes = filterUserTypesByPermission(userTypes, currentUserId);

            // 转换为VO对象
            List<UserTypeVO> result = filteredUserTypes.stream().map(userType -> {
                UserTypeVO vo = new UserTypeVO();
                BeanUtils.copyProperties(userType, vo);
                return vo;
            }).collect(Collectors.toList());
            
            log.info("获取所有用户类型列表成功, count: {}", result.size());
            return Result.success("查询成功", result);
            
        } catch (Exception e) {
            log.error("获取所有用户类型列表失败, error: {}", e.getMessage(), e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 统计指定用户类型的用户数量
     *
     * @param userTypeId 用户类型ID
     * @return 用户数量
     */
    private Long getUserCountByUserType(String userTypeId) {
        try {
            if (!StringUtils.hasText(userTypeId)) {
                return 0L;
            }
            return userTypeService.countUsersByUserType(userTypeId);
        } catch (Exception e) {
            log.warn("统计用户类型关联用户数量失败, userTypeId: {}, error: {}", userTypeId, e.getMessage());
            return 0L;
        }
    }

    /**
     * 根据用户权限获取菜单数量
     *
     * @param userTypeId 用户类型ID
     * @param currentUserId 当前用户ID
     * @return 菜单数量
     */
    private Long getMenuCountByUserType(String userTypeId, String currentUserId) {
        try {
            if (!StringUtils.hasText(userTypeId)) {
                return 0L;
            }

            // 如果是超级管理员类型，返回所有菜单数量
            if (SystemConstant.ADMIN_USER_TYPE_ID.equals(userTypeId)) {
                return menuService.countAllMenus();
            }

            // 普通用户类型返回分配的菜单数量
            return userTypeMenuService.countMenusByUserType(userTypeId);
        } catch (Exception e) {
            log.warn("统计用户类型关联菜单数量失败, userTypeId: {}, error: {}", userTypeId, e.getMessage());
            return 0L;
        }
    }

    /**
     * 检查当前用户是否为超级管理员
     *
     * @param userId 用户ID
     * @return 是超级管理员返回true，否则返回false
     */
    private boolean isSuperAdmin(String userId) {
        try {
            if (!StringUtils.hasText(userId)) {
                return false;
            }

            User user = userService.getUserById(userId);
            if (user == null) {
                return false;
            }

            // 检查用户类型是否为超级管理员
            return Objects.equals(user.getUserTypeId(), SystemConstant.ADMIN_USER_TYPE_ID);
        } catch (Exception e) {
            log.warn("检查用户是否为超级管理员失败, userId: {}, error: {}", userId, e.getMessage());
            return false;
        }
    }

    /**
     * 根据用户权限过滤分页查询结果
     *
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @param currentUserId 当前用户ID
     * @return 过滤后的分页结果
     */
    private IPage<UserType> getUserTypePageWithPermissionFilter(Page<UserType> page,
                                                               UserTypeQueryDTO queryDTO,
                                                               String currentUserId) {
        // 如果是超级管理员，可以查看所有用户类型
        if (isSuperAdmin(currentUserId)) {
            return userTypeService.getUserTypePageList(
                page,
                queryDTO.getTypeName(),
                queryDTO.getIsBuiltin() != null ? queryDTO.getIsBuiltin().getValue() : null
            );
        }

        // 非超级管理员用户，需要排除超级管理员类型
        return userTypeService.getUserTypePageListExcludeAdmin(
            page,
            queryDTO.getTypeName(),
            queryDTO.getIsBuiltin() != null ? queryDTO.getIsBuiltin().getValue() : null
        );
    }

    /**
     * 根据用户权限过滤用户类型列表
     *
     * @param userTypes 用户类型列表
     * @param currentUserId 当前用户ID
     * @return 过滤后的用户类型列表
     */
    private List<UserType> filterUserTypesByPermission(List<UserType> userTypes, String currentUserId) {
        // 如果是超级管理员，可以查看所有用户类型
        if (isSuperAdmin(currentUserId)) {
            return userTypes;
        }

        // 非超级管理员用户，需要排除超级管理员类型
        return userTypes.stream()
                .filter(userType -> !SystemConstant.ADMIN_USER_TYPE_ID.equals(userType.getId()))
                .collect(Collectors.toList());
    }
}
