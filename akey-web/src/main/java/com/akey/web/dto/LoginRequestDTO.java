package com.akey.web.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 登录请求数据传输对象
 * 
 * <p>用于接收用户登录时提交的参数</p>
 * <p>包含用户名、密码和验证码等登录必要信息</p>
 * <p>使用Bean Validation注解进行参数校验，确保数据的有效性</p>
 * 
 * <AUTHOR>
 * @since 2025-07-05
 */
@Data
public class LoginRequestDTO {

    /**
     * 用户名
     * 
     * <p>用于登录的唯一标识</p>
     * <p>不能为空，长度限制在2-50个字符之间</p>
     */
    @NotBlank(message = "请填写用户名")
    @Size(min = 2, max = 50, message = "用户名长度必须在2-50个字符之间")
    private String username;

    /**
     * 密码
     * 
     * <p>用户登录密码</p>
     * <p>不能为空，长度限制在6-100个字符之间</p>
     * <p>注意：此处接收的是前端传输的密码，可能是明文或前端加密后的密文</p>
     */
    @NotBlank(message = "请填写密码")
    @Size(min = 6, max = 100, message = "密码长度必须在6-100个字符之间")
    private String password;

    /**
     * 验证码ID
     */
    @NotBlank(message = "请填写验证码")
    private String captchaId;

    /**
     * 验证码
     *
     * <p>图形验证码或其他类型的验证码</p>
     * <p>可选字段，根据系统安全策略决定是否启用</p>
     * <p>如果启用验证码功能，建议长度限制在4-10个字符之间</p>
     */
    @Size(max = 10, message = "验证码错误")
    private String captcha;

    /**
     * 谷歌验证码
     *
     * <p>谷歌验证器生成的6位数字验证码</p>
     * <p>可选字段，只有当用户设置了谷歌验证器时才需要提供</p>
     * <p>用于增强登录安全性的二次验证</p>
     */
    @Size(max = 6, message = "谷歌验证码格式错误")
    private String googleAuthCode;
}
