package com.akey.web.dto;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.Min;
import java.time.LocalDateTime;

/**
 * 登录日志查询DTO
 * 
 * <p>用于封装登录日志查询的参数</p>
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class LoginLogQueryDTO {

    /**
     * 当前页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Long current = 1L;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Long size = 10L;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 客户端IP地址
     */
    private String clientIp;

    /**
     * 登录状态
     * 0-失败，1-成功
     */
    private Integer loginStatus;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 风险等级
     * LOW-低风险，MEDIUM-中风险，HIGH-高风险
     */
    private String riskLevel;

    /**
     * 是否可疑登录
     * 0-否，1-是
     */
    private Integer isSuspicious;

    /**
     * 设备类型
     * DESKTOP, MOBILE, TABLET, BOT
     */
    private String deviceType;

    /**
     * 操作系统名称
     */
    private String osName;

    /**
     * 浏览器名称
     */
    private String browserName;

    /**
     * 国家
     */
    private String country;

    /**
     * 省份/州
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 网络服务提供商
     */
    private String isp;

    /**
     * 排序字段
     * 默认按登录时间倒序
     */
    private String orderBy = "login_time";

    /**
     * 排序方向
     * ASC-升序，DESC-降序
     */
    private String orderDirection = "DESC";
}
