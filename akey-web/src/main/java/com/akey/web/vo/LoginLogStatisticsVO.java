package com.akey.web.vo;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 登录日志统计结果VO
 * 
 * <p>用于返回登录日志的统计分析结果</p>
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LoginLogStatisticsVO {

    /**
     * 统计时间范围 - 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 统计时间范围 - 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 总登录次数
     */
    private Long totalLogins;

    /**
     * 成功登录次数
     */
    private Long successLogins;

    /**
     * 失败登录次数
     */
    private Long failureLogins;

    /**
     * 登录成功率（百分比）
     */
    private Double successRate;

    /**
     * 独立用户数
     */
    private Long uniqueUsers;

    /**
     * 独立IP数
     */
    private Long uniqueIps;

    /**
     * 可疑登录次数
     */
    private Long suspiciousLogins;

    /**
     * 高风险登录次数
     */
    private Long highRiskLogins;

    /**
     * 每日登录统计
     * key: 日期(yyyy-MM-dd), value: 登录次数
     */
    private Map<String, Long> dailyLogins;

    /**
     * 每小时登录统计
     * key: 小时(0-23), value: 登录次数
     */
    private Map<String, Long> hourlyLogins;

    /**
     * 设备类型统计
     * key: 设备类型, value: 登录次数
     */
    private Map<String, Long> deviceTypeStats;

    /**
     * 操作系统统计
     * key: 操作系统, value: 登录次数
     */
    private Map<String, Long> osStats;

    /**
     * 浏览器统计
     * key: 浏览器, value: 登录次数
     */
    private Map<String, Long> browserStats;

    /**
     * 地理位置统计
     * key: 国家/省份/城市, value: 登录次数
     */
    private Map<String, Long> locationStats;

    /**
     * 风险等级统计
     * key: 风险等级, value: 登录次数
     */
    private Map<String, Long> riskLevelStats;

    /**
     * 热门登录IP地址（前10）
     */
    private List<IpStatistics> topIps;

    /**
     * 活跃用户统计（前10）
     */
    private List<UserStatistics> topUsers;

    /**
     * IP地址统计内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class IpStatistics {
        /**
         * IP地址
         */
        private String ip;

        /**
         * 登录次数
         */
        private Long count;

        /**
         * 成功次数
         */
        private Long successCount;

        /**
         * 失败次数
         */
        private Long failureCount;

        /**
         * 地理位置
         */
        private String location;

        /**
         * 是否可疑
         */
        private Boolean suspicious;
    }

    /**
     * 用户统计内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class UserStatistics {
        /**
         * 用户ID
         */
        private String userId;

        /**
         * 用户名
         */
        private String username;

        /**
         * 登录次数
         */
        private Long count;

        /**
         * 成功次数
         */
        private Long successCount;

        /**
         * 失败次数
         */
        private Long failureCount;

        /**
         * 最后登录时间
         */
        private LocalDateTime lastLoginTime;

        /**
         * 最后登录IP
         */
        private String lastLoginIp;
    }
}
