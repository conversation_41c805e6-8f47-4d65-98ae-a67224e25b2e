package com.akey.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 登录响应视图对象
 * 
 * <p>用于返回用户登录成功后的相关信息</p>
 * <p>包含认证token、用户基本信息等，不包含敏感信息</p>
 * <p>遵循数据安全原则，只返回前端必要的用户信息</p>
 * 
 * <AUTHOR>
 * @since 2025-07-05
 */
@Data
public class LoginResponseVO {

    /**
     * 认证令牌
     * 
     * <p>用户登录成功后生成的访问令牌</p>
     * <p>前端需要在后续请求的Header中携带此token进行身份验证</p>
     * <p>格式：Bearer {token}</p>
     */
    private String token;

    /**
     * 用户ID
     * 
     * <p>用户的唯一标识符</p>
     * <p>用于标识当前登录的用户</p>
     */
    private String userId;

    /**
     * 用户名
     * 
     * <p>用户的登录名称</p>
     * <p>用于在前端界面显示当前登录用户</p>
     */
    private String username;

    /**
     * 用户类型ID
     * 
     * <p>用户所属的类型标识</p>
     * <p>用于前端根据用户类型显示不同的功能和权限</p>
     */
    private String userTypeId;

    /**
     * 用户类型名称
     * 
     * <p>用户类型的显示名称</p>
     * <p>如：超级管理员、普通管理员、普通用户等</p>
     */
    private String userTypeName;

    /**
     * 登录时间
     * 
     * <p>本次登录的时间</p>
     * <p>用于前端显示登录时间信息</p>
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime loginTime;

    /**
     * Token过期时间
     * 
     * <p>认证令牌的过期时间</p>
     * <p>前端可根据此时间提醒用户重新登录</p>
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime tokenExpireTime;

    /**
     * 是否首次登录
     * 
     * <p>标识用户是否为首次登录</p>
     * <p>首次登录可能需要引导用户完善信息或修改密码</p>
     */
    private Boolean firstLogin = false;

    /**
     * 上次登录时间
     * 
     * <p>用户上一次登录的时间</p>
     * <p>用于安全提醒，让用户了解账户的使用情况</p>
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastLoginTime;

    /**
     * 上次登录IP
     *
     * <p>用户上一次登录的IP地址</p>
     * <p>用于安全提醒，帮助用户识别异常登录</p>
     */
    private String lastLoginIp;

    /**
     * 是否已设置谷歌验证器
     *
     * <p>标识用户是否已设置谷歌验证器</p>
     * <p>前端可根据此字段决定是否显示谷歌验证相关功能</p>
     * <p>true: 已设置，false: 未设置</p>
     */
    private Boolean hasGoogleAuth = false;
}
