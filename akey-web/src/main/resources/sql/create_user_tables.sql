-- 用户管理模块表结构 - 执行脚本
USE akey_admin;

-- 用户类型表
DROP TABLE IF EXISTS `sys_user_type`;
CREATE TABLE `sys_user_type` (
    `id` varchar(64) NOT NULL COMMENT '主键ID',
    `type_name` varchar(50) NOT NULL COMMENT '类型名称',
    `is_builtin` int(10) NOT NULL DEFAULT 1 COMMENT '是否内置类型 (0:是内置,不可删除 1:否,可删除)',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` varchar(64) NOT NULL DEFAULT 'system' COMMENT '创建人',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
    `deleted` int(10) NOT NULL DEFAULT 0 COMMENT '逻辑删除标志 (0:未删除 1:已删除)',
    `version` int(10) NOT NULL DEFAULT 0 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_type_name` (`type_name`, (IF(`deleted` = 0, 0, NULL))),
    KEY `idx_is_builtin` (`is_builtin`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户类型表';

-- 用户表
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user` (
    `id` varchar(64) NOT NULL COMMENT '用户ID',
    `username` varchar(50) NOT NULL COMMENT '用户名',
    `password` varchar(255) NOT NULL COMMENT '用户密码',
    `user_type_id` varchar(64) NOT NULL COMMENT '用户类型ID',
    `google_auth_key` varchar(100) DEFAULT NULL COMMENT '谷歌验证KEY',
    `account_locked` int(10) NOT NULL DEFAULT 0 COMMENT '账户是否锁定 (0:未锁定 1:锁定)',
    `password_update_time` datetime DEFAULT NULL COMMENT '密码最后修改时间',
    `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
    `last_login_ip` varchar(64) DEFAULT NULL COMMENT '最后登录IP',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` varchar(64) NOT NULL DEFAULT 'system' COMMENT '创建人',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
    `deleted` int(10) NOT NULL DEFAULT 0 COMMENT '逻辑删除标志 (0:未删除 1:已删除)',
    `version` int(10) NOT NULL DEFAULT 0 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`, (IF(`deleted` = 0, 0, NULL))),
    KEY `idx_user_type_id` (`user_type_id`),
    KEY `idx_account_locked` (`account_locked`),
    KEY `idx_deleted` (`deleted`),
    CONSTRAINT `fk_user_type_id` FOREIGN KEY (`user_type_id`) REFERENCES `sys_user_type` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 初始化数据
INSERT INTO `sys_user_type` (`id`, `type_name`, `is_builtin`) VALUES ('d875b80b81bf273489639a6075c662f6', '超级管理员', 0);
INSERT INTO `sys_user` (`id`, `username`, `password`, `user_type_id`) VALUES ('7245b87945b676f847b0235fc8856f6d', 'admin', '$2a$10$7JB720yubVSOfvVWmNhge.aWC3EqyFE2a8oOgOdEsWJBHKcpLgvue', 'd875b80b81bf273489639a6075c662f6'); 