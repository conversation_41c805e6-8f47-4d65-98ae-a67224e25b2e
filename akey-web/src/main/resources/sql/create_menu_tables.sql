-- =====================================================
-- 菜单管理表结构 (简化执行版本)
-- 创建日期: 2024-12-19
-- 描述: 仅包含表结构，无测试数据
-- =====================================================

-- ----------------------------
-- 1. 菜单表 (sys_menu)
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE IF NOT EXISTS `sys_menu` (
    `id` varchar(36) NOT NULL COMMENT '菜单ID',
    `parent_id` varchar(36) DEFAULT NULL COMMENT '父菜单ID(顶级菜单为null)',
    `menu_name` varchar(100) NOT NULL COMMENT '菜单名称',
    `menu_type` int(10) NOT NULL DEFAULT '2' COMMENT '菜单类型(1:目录 2:菜单 3:按钮)',
    `menu_code` varchar(100) NOT NULL COMMENT '菜单编码(唯一标识符)',
    `route_path` varchar(255) DEFAULT NULL COMMENT '路由路径',
    `component_path` varchar(255) DEFAULT NULL COMMENT '组件路径',
    `icon` varchar(100) DEFAULT NULL COMMENT '图标',
    `sort_order` int(10) NOT NULL DEFAULT '0' COMMENT '排序号',
    `visible` int(10) NOT NULL DEFAULT '1' COMMENT '是否可见(0:隐藏 1:显示)',
    `status` int(10) NOT NULL DEFAULT '1' COMMENT '状态(0:禁用 1:启用)',
    `permission` varchar(200) DEFAULT NULL COMMENT '权限标识',
    `external_link` int(10) NOT NULL DEFAULT '0' COMMENT '是否外部链接(0:否 1:是)',
    `open_mode` varchar(20) NOT NULL DEFAULT '_self' COMMENT '打开方式(_self:当前窗口 _blank:新窗口)',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` varchar(36) NOT NULL DEFAULT 'system' COMMENT '创建人ID',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by` varchar(36) DEFAULT NULL COMMENT '更新人ID',
    `deleted` int(10) NOT NULL DEFAULT '0' COMMENT '逻辑删除(0:未删除 1:已删除)',
    `version` int(10) NOT NULL DEFAULT '1' COMMENT '版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_menu_code` (`menu_code`, (IF(`deleted` = 0, 0, NULL))),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_menu_type` (`menu_type`),
    KEY `idx_visible_status` (`visible`, `status`),
    KEY `idx_sort_order` (`sort_order`),
    KEY `idx_deleted` (`deleted`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜单表';

-- ----------------------------
-- 2. 用户类型菜单关联表 (sys_user_type_menu)
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_type_menu`;
CREATE TABLE IF NOT EXISTS `sys_user_type_menu` (
    `id` varchar(36) NOT NULL COMMENT '关联ID',
    `user_type_id` varchar(36) NOT NULL COMMENT '用户类型ID',
    `menu_id` varchar(36) NOT NULL COMMENT '菜单ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` varchar(36) DEFAULT 'system' COMMENT '创建人ID',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by` varchar(36) DEFAULT NULL COMMENT '更新人ID',
    `deleted` int(10) NOT NULL DEFAULT '0' COMMENT '逻辑删除(0:未删除 1:已删除)',
    `version` int(10) NOT NULL DEFAULT '1' COMMENT '版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_type_menu` (`user_type_id`, `menu_id`, (IF(`deleted` = 0, 0, NULL))),
    KEY `idx_user_type_id` (`user_type_id`),
    KEY `idx_menu_id` (`menu_id`),
    KEY `idx_deleted` (`deleted`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户类型菜单关联表';

-- 注意：外键约束需要在用户类型表创建后再添加
-- ALTER TABLE `sys_user_type_menu` ADD CONSTRAINT `fk_user_type_menu_user_type` FOREIGN KEY (`user_type_id`) REFERENCES `sys_user_type` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
-- ALTER TABLE `sys_user_type_menu` ADD CONSTRAINT `fk_user_type_menu_menu` FOREIGN KEY (`menu_id`) REFERENCES `sys_menu` (`id`) ON DELETE CASCADE ON UPDATE CASCADE; 