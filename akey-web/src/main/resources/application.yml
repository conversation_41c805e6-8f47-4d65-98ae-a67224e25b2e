# 服务端口
server:
  port: 8080

spring:
  application:
    name: akey
  # 多环境配置 - 通过Maven profiles.active属性动态指定
  profiles:
    active: @profiles.active@

  # Jackson 配置
  jackson:
    # 日期格式化
    date-format: yyyy-MM-dd HH:mm:ss
    # 时区设置
    time-zone: GMT+8
    # 序列化配置
    serialization:
      # 禁用将日期写为时间戳
      write-dates-as-timestamps: false
      # 禁用将空对象序列化失败
      fail-on-empty-beans: false
    # 反序列化配置
    deserialization:
      # 忽略未知属性
      fail-on-unknown-properties: false

  # 定时任务配置
  task:
    scheduling:
      # 定时任务线程池配置
      pool:
        size: 2
      # 线程名前缀
      thread-name-prefix: "scheduled-task-"

# aKey 应用配置
akey:
  # 验证码配置
  captcha:
    # 是否启用验证码功能
    enabled: true
    # 默认验证码类型：NUMBER(纯数字)、LETTER(英文大小写)、MIXED(英文加数字)、ARITHMETIC(算式)、RANDOM(随机类型)
    default-type: NUMBER
    # 默认验证码长度（算式类型忽略此参数）
    default-length: 4

    # 图片配置
    image:
      # 图片宽度（像素）
      width: 120
      # 图片高度（像素）
      height: 40
      # 字体大小
      font-size: 24
      # 干扰线数量
      interference-line-count: 5
      # 噪点数量
      noise-point-count: 50
      # 是否启用字符旋转
      enable-rotation: true
      # 字符旋转角度范围（度）
      rotation-range: 15

    # 缓存配置
    cache:
      # 验证码过期时间（分钟）
      expire-minutes: 5
      # 缓存键前缀
      key-prefix: "captcha:"
      # 是否启用缓存
      enabled: true
      # 最大验证次数
      max-verify-count: 3

    # 安全配置
    security:
      # 是否忽略大小写
      ignore-case: true
      # 是否启用防暴力破解
      enable-antibrute: true
      # 同一IP最大生成次数（每分钟）
      max-generate-per-minute: 10
      # 同一IP最大验证次数（每分钟）
      max-verify-per-minute: 20
      # 是否记录验证日志
      enable-logging: true

  # 定时任务配置
  task:
    # 登录日志清理任务
    login-log-cleanup:
      # 是否启用定时清理任务
      enabled: true
      # 保留天数（超过此天数的记录将被清理）
      retain-days: 3
      # 定时任务执行时间（cron表达式）
      # 默认每天凌晨0点执行：0 0 0 * * ?
      # 格式：秒 分 时 日 月 周
      cron: "0 0 0 * * ?"

    # 操作日志清理任务
    operation-log-cleanup:
      # 是否启用定时清理任务
      enabled: true
      # 保留天数（超过此天数的记录将被清理）
      retain-days: 3
      # 定时任务执行时间（cron表达式）
      # 默认每天凌晨1点执行：0 0 1 * * ?（避免与登录日志清理任务冲突）
      # 格式：秒 分 时 日 月 周
      cron: "0 0 1 * * ?"

# 应用配置
app:
  name: AKey System
  # 谷歌验证码配置
  google-auth:
    enabled: true                                    # 是否启用谷歌验证拦截
    default-time-window: 1                          # 默认时间窗口
    code-source-priority: [ "header", "param", "body" ]  # 验证码获取方式优先级
    header-name: "X-Google-Auth-Code"               # 请求头名称
    param-name: "googleAuthCode"                    # 请求参数名称
    audit-log-enabled: true                         # 是否启用审计日志
    log-failure-details: true                       # 验证失败时是否记录详细日志