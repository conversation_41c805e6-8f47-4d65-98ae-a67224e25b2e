# 生产环境配置
spring:
  # Redis配置
  data:
    redis:
      # Redis服务器地址（生产环境建议使用集群）
      host: localhost
      # Redis服务器端口
      port: 6379
      # Redis数据库索引（生产环境使用0号数据库）
      database: 0
      # Redis服务器连接密码（生产环境必须设置密码）
      password: your_redis_password
      # 连接超时时间
      timeout: 10000ms
      # Lettuce连接池配置
      lettuce:
        pool:
          # 连接池最大连接数
          max-active: 50
          # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms
          # 连接池中的最大空闲连接
          max-idle: 20
          # 连接池中的最小空闲连接
          min-idle: 10

# Sa-Token配置
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: satoken
  # token 有效期（单位：秒） 生产环境7天
  timeout: 604800
  # token 最低活跃频率（单位：秒）
  active-timeout: -1
  # 是否允许同一账号多地同时登录
  is-concurrent: false
  # 在多人登录同一账号时，是否共用一个 token
  is-share: false
  # token 风格
  token-style: random-128
  # 是否输出操作日志
  is-log: false
  # 是否从 cookie 中读取 token
  is-read-cookie: false
  # 是否从 header 中读取 token
  is-read-header: true
  # 是否从 请求体 中读取 token
  is-read-body: false
  # token前缀
  token-prefix: "Bearer"
  # jwt秘钥（生产环境请使用复杂密钥）
  jwt-secret-key: your-production-secret-key-here

# 自定义Redis配置
akey:
  redis:
    enabled: true
    cache:
      default-expire-time: 7200  # 生产环境缓存时间长一些
      null-cache-time: 600
      key-prefix: "akey:prod:cache:"
      enable-null-cache: true
    lock:
      default-expire-time: 60
      key-prefix: "akey:prod:lock:"
      retry-count: 5
      retry-interval: 200

  # RSA加密配置
  security:
    rsa:
      # 生产环境默认启用RSA加密
      enabled: true
      request:
        # 是否启用请求解密功能
        enabled: true
        # 请求解密私钥（生产环境请配置真实密钥）
        private-key: ""
      response:
        # 是否启用响应加密功能
        enabled: true
        # 响应加密公钥（生产环境请配置真实密钥）
        public-key: ""

  # 验证码配置（生产环境）
  captcha:
    # 生产环境启用验证码
    enabled: true
    # 生产环境使用算式验证码，安全性最高
    default-type: ARITHMETIC
    # 生产环境使用标准长度（算式类型此参数无效）
    default-length: 4

    # 图片配置
    image:
      # 生产环境使用较大尺寸，增加识别难度
      width: 140
      height: 45
      # 较大字体
      font-size: 26
      # 更多干扰，增加安全性
      interference-line-count: 8
      noise-point-count: 80
      enable-rotation: true
      rotation-range: 20

    # 缓存配置
    cache:
      # 生产环境验证码过期时间较短
      expire-minutes: 3
      key-prefix: "akey:prod:captcha:"
      enabled: true
      # 生产环境限制验证次数
      max-verify-count: 3

    # 安全配置
    security:
      # 生产环境区分大小写（算式类型无影响）
      ignore-case: false
      # 生产环境启用防暴力破解
      enable-antibrute: true
      # 生产环境严格限制频率
      max-generate-per-minute: 5
      max-verify-per-minute: 10
      # 生产环境关闭详细日志，只记录异常
      enable-logging: false