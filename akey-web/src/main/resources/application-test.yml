# 测试环境配置
spring:
  # 数据源配置
  datasource:
    # 数据库连接类型 - Druid连接池
    type: com.alibaba.druid.pool.DruidDataSource
    # 数据库连接驱动 - MySQL连接驱动
    driver-class-name: com.mysql.cj.jdbc.Driver

    # Druid连接池配置
    druid:
      # 数据库连接URL
      url: ***************************************************************************************************************************************************************
      # 数据库连接用户名
      username: root
      # 数据库连接密码
      password: 123456
      # 初始化大小
      initial-size: 3
      # 最小连接池数量
      min-idle: 3
      # 最大连接池数量
      max-active: 10

  # Redis配置
  data:
    redis:
      # Redis服务器地址
      host: localhost
      # Redis服务器端口
      port: 6379
      # Redis数据库索引（测试环境使用1号数据库）
      database: 1
      # Redis服务器连接密码（默认为空）
      password:
      # 连接超时时间
      timeout: 10000ms
      # Lettuce连接池配置
      lettuce:
        pool:
          # 连接池最大连接数
          max-active: 10
          # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms
          # 连接池中的最大空闲连接
          max-idle: 5
          # 连接池中的最小空闲连接
          min-idle: 2

# Sa-Token配置
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: satoken
  # token 有效期（单位：秒） 测试环境设置较短时间
  timeout: 7200
  # token 最低活跃频率（单位：秒）
  active-timeout: -1
  # 是否允许同一账号多地同时登录
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token
  is-share: true
  # token 风格
  token-style: random-128
  # 是否输出操作日志
  is-log: true
  # 是否从 cookie 中读取 token
  is-read-cookie: true
  # 是否从 header 中读取 token
  is-read-header: true
  # 是否从 请求体 中读取 token
  is-read-body: false
  # token前缀
  token-prefix: "Bearer"
  # jwt秘钥
  jwt-secret-key: abcdefghijklmnopqrstuvwxyz

# 自定义Redis配置
akey:
  redis:
    enabled: true
    cache:
      default-expire-time: 1800  # 测试环境缓存时间短一些
      null-cache-time: 180
      key-prefix: "akey:test:cache:"
      enable-null-cache: true
    lock:
      default-expire-time: 20
      key-prefix: "akey:test:lock:"
      retry-count: 3
      retry-interval: 100

  # RSA加密配置
  security:
    rsa:
      # 测试环境默认启用RSA加密
      enabled: true
      request:
        # 是否启用请求解密功能
        enabled: true
        # 请求解密私钥（测试环境密钥，生产环境请更换）
        private-key: ""
      response:
        # 是否启用响应加密功能
        enabled: true
        # 响应加密公钥（测试环境密钥，生产环境请更换）
        public-key: ""

  # 验证码配置（测试环境）
  captcha:
    # 测试环境启用验证码
    enabled: true
    # 测试环境使用混合验证码，增加安全性
    default-type: MIXED
    # 测试环境使用标准长度
    default-length: 4

    # 图片配置
    image:
      # 测试环境使用标准尺寸
      width: 120
      height: 40
      # 标准字体大小
      font-size: 24
      # 标准干扰设置
      interference-line-count: 5
      noise-point-count: 50
      enable-rotation: true
      rotation-range: 15

    # 缓存配置
    cache:
      # 测试环境验证码过期时间适中
      expire-minutes: 5
      key-prefix: "akey:test:captcha:"
      enabled: true
      # 测试环境标准验证次数
      max-verify-count: 3

    # 安全配置
    security:
      # 测试环境忽略大小写
      ignore-case: true
      # 测试环境启用防暴力破解
      enable-antibrute: true
      max-generate-per-minute: 20
      max-verify-per-minute: 30
      # 测试环境启用日志
      enable-logging: true