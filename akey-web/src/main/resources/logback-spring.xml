<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 日志存放路径 -->
    <property name="log.path" value="logs" />
    <!-- 日志输出格式 -->
    <property name="log.pattern" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36}:%line - [%X{traceId}] - %msg%n" />
    <!-- 控制台日志输出格式（带颜色） -->
    <property name="console.log.pattern" value="%yellow(%d{yyyy-MM-dd HH:mm:ss.SSS}) %green([%thread]) %highlight(%-5level) %boldMagenta(%logger{36}:%line) - %blue([%X{traceId}]) - %cyan(%msg%n)" />
    <!-- 日志保留天数 -->
    <property name="log.max-history" value="3" />
    <!-- 日志文件大小上限 -->
    <property name="log.max-file-size" value="50MB" />
    <!-- 日志总文件大小上限 -->
    <property name="log.total-size-cap" value="1GB" />

    <!-- 从Spring环境中获取应用名称，如果获取不到则使用默认值-->
    <springProperty scope="context" name="SPRING_APPLICATION_NAME" source="spring.application.name" defaultValue="akey"/>

    <!-- 禁用Logback状态信息输出 -->
    <statusListener class="ch.qos.logback.core.status.NopStatusListener" />

    <!-- 控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${console.log.pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 系统所有日志输出 -->
    <appender name="file_all" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/${SPRING_APPLICATION_NAME}-all.log</file>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志文件名格式 -->
            <fileNamePattern>${log.path}/${SPRING_APPLICATION_NAME}-all.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 日志最大保留历史数量 -->
            <maxHistory>${log.max-history}</maxHistory>
            <!-- 单个日志文件最大大小 -->
            <maxFileSize>${log.max-file-size}</maxFileSize>
            <!-- 日志总文件大小上限 -->
            <totalSizeCap>${log.total-size-cap}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 系统错误日志输出 -->
    <appender name="file_error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/${SPRING_APPLICATION_NAME}-error.log</file>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志文件名格式 -->
            <fileNamePattern>${log.path}/${SPRING_APPLICATION_NAME}-error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 日志最大保留历史数量 -->
            <maxHistory>${log.max-history}</maxHistory>
            <!-- 单个日志文件最大大小 -->
            <maxFileSize>${log.max-file-size}</maxFileSize>
            <!-- 日志总文件大小上限 -->
            <totalSizeCap>${log.total-size-cap}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
    </appender>

    <!-- 开发环境 -->
    <springProfile name="dev">
        <root level="info">
            <appender-ref ref="console" />
            <appender-ref ref="file_all" />
            <appender-ref ref="file_error" />
        </root>

        <!-- 自定义包日志级别 -->
        <logger name="com.akey" level="debug" />
        
        <!-- SQL日志输出 -->
        <logger name="com.akey.core.mapper" level="debug" />
        <logger name="druid.sql.Statement" level="debug" />
    </springProfile>

    <!-- 测试环境 -->
    <springProfile name="test">
        <root level="info">
            <appender-ref ref="console" />
            <appender-ref ref="file_all" />
            <appender-ref ref="file_error" />
        </root>

        <!-- 自定义包日志级别 -->
        <logger name="com.akey" level="info" />
    </springProfile>

    <!-- 生产环境 -->
    <springProfile name="prod">
        <root level="warn">
            <appender-ref ref="file_all" />
            <appender-ref ref="file_error" />
        </root>

        <!-- 自定义包日志级别 -->
        <logger name="com.akey" level="info" />
    </springProfile>
</configuration>