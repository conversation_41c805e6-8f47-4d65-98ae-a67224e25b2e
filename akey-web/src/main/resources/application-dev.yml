# 开发环境配置
spring:
  # 数据源配置
  datasource:
    # 数据库连接类型 - Druid连接池
    type: com.alibaba.druid.pool.DruidDataSource
    # 数据库连接驱动 - MySQL连接驱动
    driver-class-name: com.mysql.cj.jdbc.Driver

    # Druid连接池配置
    druid:
      # 数据库连接URL
      url: ***************************************************************************************************************************************************
      # 数据库连接用户名
      username: root
      # 数据库连接密码
      password: 123456!@#
      # 初始化大小
      initial-size: 5
      # 最小连接池数量
      min-idle: 5
      # 最大连接池数量
      max-active: 20
      # 配置获取连接等待超时的时间
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      # 验证连接有效性的SQL
      validation-query: SELECT 1 FROM DUAL
      # 申请连接时执行validationQuery检测连接是否有效
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      
      # 打开PSCache，并且指定每个连接上PSCache的大小
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall,slf4j
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=500
      
      # 配置DruidStatFilter
      web-stat-filter:
        enabled: true
        url-pattern: "/*"
        exclusions: "*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*"
      
      # 配置DruidStatViewServlet
      stat-view-servlet:
        enabled: true
        url-pattern: "/druid/*"
        # IP白名单(没有配置或者为空，则允许所有访问)
        allow: 127.0.0.1,***********/24
        # IP黑名单 (存在共同时，deny优先于allow)
        deny: ************
        # 禁用HTML页面上的"Reset All"功能
        reset-enable: false
        # 登录名
        login-username: admin
        # 登录密码
        login-password: 123456

# Redis配置
  data:
    redis:
      # Redis服务器地址
      host: localhost
      # Redis服务器端口
      port: 6379
      # Redis数据库索引（默认为0）
      database: 0
      # Redis服务器连接密码（默认为空）
      password:
      # 连接超时时间
      timeout: 10000ms
      # Lettuce连接池配置
      lettuce:
        pool:
          # 连接池最大连接数
          max-active: 20
          # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms
          # 连接池中的最大空闲连接
          max-idle: 10
          # 连接池中的最小空闲连接
          min-idle: 5
        # 关闭超时时间
        shutdown-timeout: 100ms

# MyBatis-Plus配置
mybatis-plus:
  # 如果是放在src/main/java目录下 classpath:/com/yourpackage/*/mapper/*Mapper.xml
  # 如果是放在resource目录 classpath:/mapper/*Mapper.xml
  mapper-locations: classpath*:/mapper/**/*Mapper.xml
  # 实体扫描，多个package用逗号或者分号分隔
  type-aliases-package: com.akey.core.dao.entity
  global-config:
    # 数据库相关配置
    db-config:
      # 主键类型
      id-type: ASSIGN_UUID
      # 字段策略 IGNORED:"忽略判断",NOT_NULL:"非NULL判断"),NOT_EMPTY:"非空判断"
      field-strategy: NOT_NULL
      # 驼峰下划线转换
      column-underline: true
      # 逻辑删除配置
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
    # 是否开启LOGO
    banner: false
  # 原生配置
  configuration:
    # 是否开启自动驼峰命名规则映射:从数据库列名到Java属性的映射
    map-underscore-to-camel-case: true
    cache-enabled: false
    # 如果查询结果中包含空值的列，则 MyBatis 在映射的时候，不会映射这个字段
    call-setters-on-nulls: true
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# Sa-Token配置
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: authorization
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 2592000
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: false
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: random-128
  # 是否输出操作日志
  is-log: true
  # 是否从 cookie 中读取 token
  is-read-cookie: false
  # 是否从 header 中读取 token
  is-read-header: true
  # 是否从 请求体 中读取 token
  is-read-body: false
  # token前缀
  token-prefix: "Bearer"
  # jwt秘钥
  jwt-secret-key: abcdefghijklmnopqrstuvwxyz

# 自定义Redis配置
akey:
  redis:
    enabled: true
    cache:
      default-expire-time: 3600
      null-cache-time: 300
      key-prefix: "akey:dev:cache:"
      enable-null-cache: true
    lock:
      default-expire-time: 30
      key-prefix: "akey:dev:lock:"
      retry-count: 3
      retry-interval: 100

  # RSA加密配置
  security:
    rsa:
      # 开发环境默认禁用RSA加密
      enabled: false
      request:
        # 是否启用请求解密功能
        enabled: true
        # 请求解密私钥（开发环境可以为空）
        private-key: "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDHtADxKxdMh9kPC/tobxnh+ReYV8NJBljc28JQiFgZ1F5jjLUb34pvU+vjlYY0Db99g7vB4xc2v7xgfwgRyHo4+wYxDka668SHU00+bSljbx8yD49lgmA47Fvi3RzsfRoBhoEtL3fWyPIRppASozLo3RuDgWPQ+Oz931lRxnwuJgGKn4+gN+Td0sbtTyPvd175UYI2OPHaf5ZyPWp1+Lyen7vYsa6EYfzAOCJ7DpSAi5JEkqCVXQ8jH70L3aE96s135thUgTX7yYBPb/Ml8SqmSBu4/NJLsrhJnhXS2HkPokDJ8kzM9UW/ek0Wxsosca/96heFkEEcAoTjzFYHWlvfAgMBAAECggEAUsxK57LaAh0A3cA2CGKn9Iz2P2+DxC8hgNCMjB87h9oJGHUqUdgo6Wd+EenYrpRUpJGNsyGYalasn8WLbvQT2LQbKROmN7Gw1fqvuAZCdruHz5Wr+oZHptI4dqL+bv6fXaqKvFVF9oNx+ed5LIEFho7F7Ywj+mx6tfd4bPNCYU8cS2Nv7bR5e/hbsmGAsQcWfmNKVkyfCKa7qWZrEoPc/B7TkBPsHSid4+Qb5vk8NAox03hSJE9sw9JivNFEsuse66xnQie8qwT6tq0p7XiNwJUkEm49Akm9Y2KkzQEjRwe44TfXuQ8pHW3SOWqq7TQ/BWFUE9Axb3a/9wPtVlEfYQKBgQD5MULMHC9hfm4ltCQTSD2XnELzHvPYtuL+DAevweGOZsRuMQ5V8c8SQUdNGEgIE+nek6TGGpOkVv+ahvdnNAgYbqXY98Bhh7Aj9QZ9FWOzADvPXZJgGvy+2mGb6Z3h9jVyls3napbfdK06MCEQoks+nGAmSSWnQN+k0c5ziWKS6wKBgQDNKKMXi2DRmrF5cF3f04I3KdzYL7zjk1VnXmOp9lCLCeG2gdgREoKurwJSXFu3hmLcrXq+4zy5y0RKBXu/+O27V07DTf1m8zxRIEOLJNeHA4T2Uwcchn7hdKmmfymhAlVArjelBvQROstrPmjIk9fdoxviyqPoz6PqwaWCEgzV3QKBgQCdkWiLZ8cN/sPnU2D8UFGh7EzpLklCX2JpAXJa0F2vjYvnoV1POveUs3JCbXgeo3jeJL2sBmIVKG23aGBIAb6ctFpqNz6ObR4Tnnvb3AqOxTcMiD5mRqpKhAv0amRK3UQukOzhx6Re54ghow1FV1n83SRvERGeEIdZ0NCafeS2wQKBgQCemZO7V9x5LYWVw/BOYEyCKLK5ze3QYM3dI3MYqXqKghEj7i0CxbSRJmo9EXk134X9437IoxMG7sTUFHkeZ2ohMdWJ1njXZaJzdOXCY7DahzX61yTgQHlGAdJFDHk6J3uQjR4nTcDQsQsg1RZcOS7gAIBGSzF7gUnO6fTxINMbYQKBgAsKlNy50RCNSWtebR9C5/a1rTrzozrA8I1WD0AL6y/6RNOaYPxVMF6JfyAaWvVVuEbWfL02vAtcMsOwaaxFOL4jkgk3lCOJv/4v+QUQ3AxUTqdh2gPlV3jHUheCFOzPkaGFlP28J8hoHoGTWbjWnaX9Uk3dRxXv8LhTdia8w28z"
      response:
        # 是否启用响应加密功能
        enabled: true
        # 响应加密公钥（开发环境可以为空）
        public-key: "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAj0eJirPn26OEWIc+ReMBq3ngsCLrzx71swSv/0DB1UfJz8OnZyHf9uI3FpIhramI7uQ0pH8+Ziv83vFA4H+AdSmPWaPL2EU3l7R0E1oYg4vfYNBJhZP8cOk/BNuBIx6W/LVBURFeuJOUjEbgIMo6BuZkqQM0Fu3qbL5gW8qY7EXhbILYFiqn4wbvluM6h2g5kM4ZJvFeGrXGXjOjHmQvL4MKImuW4bHh7dQNaLlWih5QPOK/+ubHMU5ue6SgzzpJtJXI1G/6jXUUzJT8wVZ4/RtzS/Lh0GCwPepfv/GVxU9JDBY/inARfNqPmLxDYtTulQuupwO0c7eKifR9a4pi6wIDAQAB"

  # 验证码配置（开发环境）
  captcha:
    # 开发环境启用验证码
    enabled: true
    # 开发环境使用数字验证码，便于测试 RANDOM
    default-type: RANDOM
    # 开发环境使用较短的验证码
    default-length: 4

    # 图片配置
    image:
      # 开发环境使用标准尺寸
      width: 120
      height: 40
      # 字体稍小一些
      font-size: 22
      # 减少干扰，便于开发调试
      interference-line-count: 20
      # 噪点数量
      noise-point-count: 60
      # 是否启用字符旋转
      enable-rotation: true
      # 字符旋转角度范围（度）
      rotation-range: 10

    # 缓存配置
    cache:
      # 开发环境验证码过期时间较长，便于调试
      expire-minutes: 10
      key-prefix: "akey:dev:captcha:"
      # 是否启用缓存
      enabled: true
      # 开发环境允许更多验证次数
      max-verify-count: 5

    # 安全配置
    security:
      # 开发环境忽略大小写
      ignore-case: true
      # 开发环境关闭防暴力破解，便于测试
      enable-antibrute: true
      # 同一IP最大生成次数（每分钟）
      max-generate-per-minute: 100000
      # 同一IP最大验证次数（每分钟）
      max-verify-per-minute: 10000
      # 开发环境启用详细日志
      enable-logging: true
