package com.akey.web.vo;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * LoginResponseVO 单元测试
 * 
 * <p>测试登录响应VO的序列化和反序列化功能</p>
 * 
 * <AUTHOR>
 * @since 2025-07-05
 */
class LoginResponseVOTest {

    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
    }

    @Test
    void testCreateLoginResponseVO() {
        // 创建登录响应VO
        LoginResponseVO responseVO = new LoginResponseVO();
        responseVO.setToken("test-token-123");
        responseVO.setUserId("user-001");
        responseVO.setUsername("testuser");
        responseVO.setUserTypeId("type-001");
        responseVO.setUserTypeName("管理员");
        responseVO.setLoginTime(LocalDateTime.of(2025, 7, 5, 10, 30, 0));
        responseVO.setTokenExpireTime(LocalDateTime.of(2025, 7, 5, 18, 30, 0));
        responseVO.setFirstLogin(false);
        responseVO.setLastLoginTime(LocalDateTime.of(2025, 7, 4, 15, 20, 0));
        responseVO.setLastLoginIp("*************");

        // 断言：验证所有字段都设置正确
        assertEquals("test-token-123", responseVO.getToken());
        assertEquals("user-001", responseVO.getUserId());
        assertEquals("testuser", responseVO.getUsername());
        assertEquals("type-001", responseVO.getUserTypeId());
        assertEquals("管理员", responseVO.getUserTypeName());
        assertEquals(LocalDateTime.of(2025, 7, 5, 10, 30, 0), responseVO.getLoginTime());
        assertEquals(LocalDateTime.of(2025, 7, 5, 18, 30, 0), responseVO.getTokenExpireTime());
        assertFalse(responseVO.getFirstLogin());
        assertEquals(LocalDateTime.of(2025, 7, 4, 15, 20, 0), responseVO.getLastLoginTime());
        assertEquals("*************", responseVO.getLastLoginIp());
    }

    @Test
    void testDefaultFirstLogin() {
        // 创建登录响应VO，不设置firstLogin
        LoginResponseVO responseVO = new LoginResponseVO();

        // 断言：默认值应该是false
        assertFalse(responseVO.getFirstLogin(), "firstLogin的默认值应该是false");
    }

    @Test
    void testJsonSerialization() throws JsonProcessingException {
        // 创建登录响应VO
        LoginResponseVO responseVO = new LoginResponseVO();
        responseVO.setToken("test-token-123");
        responseVO.setUserId("user-001");
        responseVO.setUsername("testuser");
        responseVO.setUserTypeId("type-001");
        responseVO.setUserTypeName("管理员");
        responseVO.setLoginTime(LocalDateTime.of(2025, 7, 5, 10, 30, 0));
        responseVO.setTokenExpireTime(LocalDateTime.of(2025, 7, 5, 18, 30, 0));
        responseVO.setFirstLogin(true);
        responseVO.setLastLoginTime(LocalDateTime.of(2025, 7, 4, 15, 20, 0));
        responseVO.setLastLoginIp("*************");

        // 序列化为JSON
        String json = objectMapper.writeValueAsString(responseVO);

        // 断言：JSON应该包含所有字段
        assertNotNull(json);
        assertTrue(json.contains("test-token-123"));
        assertTrue(json.contains("user-001"));
        assertTrue(json.contains("testuser"));
        assertTrue(json.contains("type-001"));
        assertTrue(json.contains("管理员"));
        assertTrue(json.contains("2025-07-05 10:30:00")); // 时间格式化
        assertTrue(json.contains("2025-07-05 18:30:00"));
        assertTrue(json.contains("true")); // firstLogin
        assertTrue(json.contains("2025-07-04 15:20:00"));
        assertTrue(json.contains("*************"));
    }

    @Test
    void testJsonDeserialization() throws JsonProcessingException {
        // JSON字符串
        String json = """
            {
                "token": "test-token-123",
                "userId": "user-001",
                "username": "testuser",
                "userTypeId": "type-001",
                "userTypeName": "管理员",
                "loginTime": "2025-07-05 10:30:00",
                "tokenExpireTime": "2025-07-05 18:30:00",
                "firstLogin": true,
                "lastLoginTime": "2025-07-04 15:20:00",
                "lastLoginIp": "*************"
            }
            """;

        // 反序列化为对象
        LoginResponseVO responseVO = objectMapper.readValue(json, LoginResponseVO.class);

        // 断言：验证所有字段都反序列化正确
        assertEquals("test-token-123", responseVO.getToken());
        assertEquals("user-001", responseVO.getUserId());
        assertEquals("testuser", responseVO.getUsername());
        assertEquals("type-001", responseVO.getUserTypeId());
        assertEquals("管理员", responseVO.getUserTypeName());
        assertEquals(LocalDateTime.of(2025, 7, 5, 10, 30, 0), responseVO.getLoginTime());
        assertEquals(LocalDateTime.of(2025, 7, 5, 18, 30, 0), responseVO.getTokenExpireTime());
        assertTrue(responseVO.getFirstLogin());
        assertEquals(LocalDateTime.of(2025, 7, 4, 15, 20, 0), responseVO.getLastLoginTime());
        assertEquals("*************", responseVO.getLastLoginIp());
    }

    @Test
    void testNullValues() {
        // 创建登录响应VO，设置一些null值
        LoginResponseVO responseVO = new LoginResponseVO();
        responseVO.setToken("test-token-123");
        responseVO.setUserId("user-001");
        responseVO.setUsername("testuser");
        responseVO.setUserTypeId("type-001");
        responseVO.setUserTypeName("管理员");
        responseVO.setLoginTime(LocalDateTime.now());
        // tokenExpireTime, lastLoginTime, lastLoginIp 设置为null

        // 断言：null值应该被正确处理
        assertNotNull(responseVO.getToken());
        assertNotNull(responseVO.getUserId());
        assertNotNull(responseVO.getUsername());
        assertNull(responseVO.getTokenExpireTime());
        assertNull(responseVO.getLastLoginTime());
        assertNull(responseVO.getLastLoginIp());
    }

    @Test
    void testEqualsAndHashCode() {
        // 创建两个相同的登录响应VO
        LoginResponseVO responseVO1 = new LoginResponseVO();
        responseVO1.setToken("test-token-123");
        responseVO1.setUserId("user-001");
        responseVO1.setUsername("testuser");

        LoginResponseVO responseVO2 = new LoginResponseVO();
        responseVO2.setToken("test-token-123");
        responseVO2.setUserId("user-001");
        responseVO2.setUsername("testuser");

        // 断言：由于使用了Lombok的@Data注解，equals和hashCode应该正常工作
        assertEquals(responseVO1, responseVO2);
        assertEquals(responseVO1.hashCode(), responseVO2.hashCode());
    }

    @Test
    void testToString() {
        // 创建登录响应VO
        LoginResponseVO responseVO = new LoginResponseVO();
        responseVO.setToken("test-token-123");
        responseVO.setUserId("user-001");
        responseVO.setUsername("testuser");

        // 断言：toString应该包含字段信息
        String toString = responseVO.toString();
        assertNotNull(toString);
        assertTrue(toString.contains("test-token-123"));
        assertTrue(toString.contains("user-001"));
        assertTrue(toString.contains("testuser"));
    }
}
