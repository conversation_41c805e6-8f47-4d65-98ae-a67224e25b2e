package com.akey.web.dto;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * LoginRequestDTO 单元测试
 * 
 * <p>测试登录请求DTO的参数验证功能</p>
 * 
 * <AUTHOR>
 * @since 2025-07-05
 */
class LoginRequestDTOTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    void testValidLoginRequest() {
        // 创建有效的登录请求
        LoginRequestDTO loginRequest = new LoginRequestDTO();
        loginRequest.setUsername("testuser");
        loginRequest.setPassword("password123");
        loginRequest.setCaptchaId("captcha-123");
        loginRequest.setCaptcha("1234");

        // 验证
        Set<ConstraintViolation<LoginRequestDTO>> violations = validator.validate(loginRequest);

        // 断言：应该没有验证错误
        assertTrue(violations.isEmpty(), "有效的登录请求不应该有验证错误");
    }

    @Test
    void testBlankUsername() {
        // 创建用户名为空的登录请求
        LoginRequestDTO loginRequest = new LoginRequestDTO();
        loginRequest.setUsername("");
        loginRequest.setPassword("password123");

        // 验证
        Set<ConstraintViolation<LoginRequestDTO>> violations = validator.validate(loginRequest);

        // 断言：应该有验证错误（可能是@NotBlank和@Size同时触发）
        assertTrue(violations.size() >= 1, "空用户名应该有验证错误");

        // 检查是否包含预期的错误消息
        boolean hasExpectedMessage = violations.stream()
                .anyMatch(v -> "用户名不能为空".equals(v.getMessage()) ||
                              "用户名长度必须在2-50个字符之间".equals(v.getMessage()));
        assertTrue(hasExpectedMessage, "应该包含用户名相关的验证错误消息");
    }

    @Test
    void testNullUsername() {
        // 创建用户名为null的登录请求
        LoginRequestDTO loginRequest = new LoginRequestDTO();
        loginRequest.setUsername(null);
        loginRequest.setPassword("password123");

        // 验证
        Set<ConstraintViolation<LoginRequestDTO>> violations = validator.validate(loginRequest);

        // 断言：应该有一个验证错误
        assertEquals(1, violations.size());
        ConstraintViolation<LoginRequestDTO> violation = violations.iterator().next();
        assertEquals("用户名不能为空", violation.getMessage());
    }

    @Test
    void testUsernameTooShort() {
        // 创建用户名过短的登录请求
        LoginRequestDTO loginRequest = new LoginRequestDTO();
        loginRequest.setUsername("a");
        loginRequest.setPassword("password123");

        // 验证
        Set<ConstraintViolation<LoginRequestDTO>> violations = validator.validate(loginRequest);

        // 断言：应该有一个验证错误
        assertEquals(1, violations.size());
        ConstraintViolation<LoginRequestDTO> violation = violations.iterator().next();
        assertEquals("用户名长度必须在2-50个字符之间", violation.getMessage());
    }

    @Test
    void testUsernameTooLong() {
        // 创建用户名过长的登录请求
        LoginRequestDTO loginRequest = new LoginRequestDTO();
        loginRequest.setUsername("a".repeat(51)); // 51个字符
        loginRequest.setPassword("password123");

        // 验证
        Set<ConstraintViolation<LoginRequestDTO>> violations = validator.validate(loginRequest);

        // 断言：应该有一个验证错误
        assertEquals(1, violations.size());
        ConstraintViolation<LoginRequestDTO> violation = violations.iterator().next();
        assertEquals("用户名长度必须在2-50个字符之间", violation.getMessage());
    }

    @Test
    void testBlankPassword() {
        // 创建密码为空的登录请求
        LoginRequestDTO loginRequest = new LoginRequestDTO();
        loginRequest.setUsername("testuser");
        loginRequest.setPassword("");

        // 验证
        Set<ConstraintViolation<LoginRequestDTO>> violations = validator.validate(loginRequest);

        // 断言：应该有验证错误（可能是@NotBlank和@Size同时触发）
        assertTrue(violations.size() >= 1, "空密码应该有验证错误");

        // 检查是否包含预期的错误消息
        boolean hasExpectedMessage = violations.stream()
                .anyMatch(v -> "密码不能为空".equals(v.getMessage()) ||
                              "密码长度必须在6-100个字符之间".equals(v.getMessage()));
        assertTrue(hasExpectedMessage, "应该包含密码相关的验证错误消息");
    }

    @Test
    void testPasswordTooShort() {
        // 创建密码过短的登录请求
        LoginRequestDTO loginRequest = new LoginRequestDTO();
        loginRequest.setUsername("testuser");
        loginRequest.setPassword("12345"); // 5个字符

        // 验证
        Set<ConstraintViolation<LoginRequestDTO>> violations = validator.validate(loginRequest);

        // 断言：应该有一个验证错误
        assertEquals(1, violations.size());
        ConstraintViolation<LoginRequestDTO> violation = violations.iterator().next();
        assertEquals("密码长度必须在6-100个字符之间", violation.getMessage());
    }

    @Test
    void testCaptchaTooLong() {
        // 创建验证码过长的登录请求
        LoginRequestDTO loginRequest = new LoginRequestDTO();
        loginRequest.setUsername("testuser");
        loginRequest.setPassword("password123");
        loginRequest.setCaptcha("12345678901"); // 11个字符

        // 验证
        Set<ConstraintViolation<LoginRequestDTO>> violations = validator.validate(loginRequest);

        // 断言：应该有一个验证错误
        assertEquals(1, violations.size());
        ConstraintViolation<LoginRequestDTO> violation = violations.iterator().next();
        assertEquals("验证码长度不能超过10个字符", violation.getMessage());
    }

    @Test
    void testDefaultRememberMe() {
        // 创建登录请求，不设置rememberMe
        LoginRequestDTO loginRequest = new LoginRequestDTO();
        loginRequest.setUsername("testuser");
        loginRequest.setPassword("password123");

        // 断言：验证基本字段
        assertNull(loginRequest.getUsername());
        assertNull(loginRequest.getPassword());
    }

    @Test
    void testMultipleValidationErrors() {
        // 创建有多个验证错误的登录请求
        LoginRequestDTO loginRequest = new LoginRequestDTO();
        loginRequest.setUsername(""); // 用户名为空
        loginRequest.setPassword("123"); // 密码过短
        loginRequest.setCaptcha("12345678901"); // 验证码过长

        // 验证
        Set<ConstraintViolation<LoginRequestDTO>> violations = validator.validate(loginRequest);

        // 断言：应该有多个验证错误（@NotBlank和@Size可能同时触发）
        assertTrue(violations.size() >= 3, "应该有多个验证错误");

        // 验证包含的错误类型
        Set<String> errorMessages = violations.stream()
                .map(ConstraintViolation::getMessage)
                .collect(java.util.stream.Collectors.toSet());

        // 至少应该包含这些类型的错误
        assertTrue(errorMessages.stream().anyMatch(msg ->
                msg.contains("用户名") || msg.contains("密码") || msg.contains("验证码")),
                "应该包含用户名、密码或验证码相关的错误");
    }
}
