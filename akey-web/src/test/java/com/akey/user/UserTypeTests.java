package com.akey.user;

import com.akey.common.enums.BuiltinEnum;
import com.akey.core.dao.entity.UserType;
import com.akey.core.dao.service.UserTypeService;
import com.akey.web.AkeyApplication;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

/**
 * 用户类型测试类
 */
@Slf4j
@ActiveProfiles("dev")
@SpringBootTest(classes = AkeyApplication.class)
class UserTypeTests {

    @Autowired
    private UserTypeService userTypeService;

    /**
     * 获取所有用户类型
     */
    @Test
    void testUserType() {
        List<UserType> userTypes = userTypeService.getAllUserTypes();
        log.info("userTypes: {}", userTypes);
    }

    /**
     * 创建用户类型
     */
    @Test
    void testCreateUserType() {
        UserType userType = new UserType();
        userType.setTypeName("测试用户类型2");
        userType.setIsBuiltin(BuiltinEnum.BUILTIN);
        // 判断是否创建成功
        log.info("创建用户类型是否成功: {}", userTypeService.createUserType(userType));
    }

    /**
     * 更新用户类型
     */
    @Test
    void testUpdateUserType() {
        UserType userType = new UserType();
        userType.setId("666b5f25bd4c7401130bffc09413a78d");
        userType.setTypeName("测试用户类型2-修改");
        userType.setIsBuiltin(BuiltinEnum.BUILTIN);
        // 判断是否更新成功
        log.info("更新用户类型是否成功: {}", userTypeService.updateUserType(userType));
    }

    /**
     * 删除用户类型
     */
    @Test
    void testDeleteUserType() {
        log.info("删除用户类型是否成功: {}", userTypeService.deleteUserType("666b5f25bd4c7401130bffc09413a78d"));
    }

    /**
     * 分页查询用户类型
     */
    @Test
    void testUserTypePageList() {
        Page<UserType> page = new Page<>(1, 10);
        log.info("用户类型分页查询结果: {}", userTypeService.getUserTypePageList(page, null, null).getRecords());
    }

    /**
     * 根据用户ID获取可用类型
     */
    @Test
    void testGetAvailableUserTypesForUser() {
        String userId = "3cd5cb110a3f69ca9fcf67cf05cb5437";
        log.info("根据用户ID获取可用类型: {}", userTypeService.getAvailableUserTypesForUser(userId));
    }


}
