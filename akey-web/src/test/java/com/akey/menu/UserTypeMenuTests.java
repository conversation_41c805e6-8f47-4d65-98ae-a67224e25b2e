package com.akey.menu;

import com.akey.core.dao.entity.Menu;
import com.akey.core.dao.entity.UserType;
import com.akey.core.dao.entity.UserTypeMenu;
import com.akey.core.dao.service.MenuService;
import com.akey.core.dao.service.UserTypeMenuService;
import com.akey.core.dao.service.UserTypeService;
import com.akey.common.enums.*;
import com.akey.web.AkeyApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 用户类型菜单关联Service测试类
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */

@Slf4j
@ActiveProfiles("dev")
@SpringBootTest(classes = AkeyApplication.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class UserTypeMenuTests {

    @Autowired
    private UserTypeMenuService userTypeMenuService;
    
    @Autowired
    private UserTypeService userTypeService;
    
    @Autowired
    private MenuService menuService;

    // 测试数据ID，在测试方法间共享
    private static String testUserTypeId1;
    private static String testUserTypeId2;
    private static String testMenuId1;
    private static String testMenuId2;
    private static String testMenuId3;

    /**
     * 创建测试数据
     */
    @Test
    @Order(1)
    public void testCreateTestData() {
        log.info("=== 开始创建测试数据 ===");
        
        // 创建测试用户类型1
        UserType userType1 = new UserType();
        userType1.setTypeName("测试管理员1");
        userType1.setIsBuiltin(BuiltinEnum.NOT_BUILTIN);
        
        boolean userType1Created = userTypeService.createUserType(userType1);
        assertTrue(userType1Created, "测试用户类型1创建应该成功");
        testUserTypeId1 = userType1.getId();
        log.info("创建测试用户类型1成功, id: {}", testUserTypeId1);
        
        // 创建测试用户类型2
        UserType userType2 = new UserType();
        userType2.setTypeName("测试管理员2");
        userType2.setIsBuiltin(BuiltinEnum.NOT_BUILTIN);
        
        boolean userType2Created = userTypeService.createUserType(userType2);
        assertTrue(userType2Created, "测试用户类型2创建应该成功");
        testUserTypeId2 = userType2.getId();
        log.info("创建测试用户类型2成功, id: {}", testUserTypeId2);
        
        // 创建测试菜单1
        Menu menu1 = new Menu();
        menu1.setMenuName("测试菜单1");
        menu1.setMenuCode("test_menu_1");
        menu1.setMenuType(MenuTypeEnum.DIRECTORY);
        menu1.setSortOrder(1);
        menu1.setStatus(EnableStatusEnum.ENABLED);
        menu1.setVisible(ShowHideEnum.VISIBLE);
        
        boolean menu1Created = menuService.createMenu(menu1);
        assertTrue(menu1Created, "测试菜单1创建应该成功");
        testMenuId1 = menu1.getId();
        log.info("创建测试菜单1成功, id: {}", testMenuId1);
        
        // 创建测试菜单2
        Menu menu2 = new Menu();
        menu2.setMenuName("测试菜单2");
        menu2.setMenuCode("test_menu_2");
        menu2.setMenuType(MenuTypeEnum.MENU);
        menu2.setSortOrder(2);
        menu2.setStatus(EnableStatusEnum.ENABLED);
        menu2.setVisible(ShowHideEnum.VISIBLE);
        
        boolean menu2Created = menuService.createMenu(menu2);
        assertTrue(menu2Created, "测试菜单2创建应该成功");
        testMenuId2 = menu2.getId();
        log.info("创建测试菜单2成功, id: {}", testMenuId2);
        
        // 创建测试菜单3
        Menu menu3 = new Menu();
        menu3.setMenuName("测试菜单3");
        menu3.setMenuCode("test_menu_3");
        menu3.setMenuType(MenuTypeEnum.BUTTON);
        menu3.setSortOrder(3);
        menu3.setStatus(EnableStatusEnum.ENABLED);
        menu3.setVisible(ShowHideEnum.VISIBLE);
        
        boolean menu3Created = menuService.createMenu(menu3);
        assertTrue(menu3Created, "测试菜单3创建应该成功");
        testMenuId3 = menu3.getId();
        log.info("创建测试菜单3成功, id: {}", testMenuId3);
        
        log.info("=== 测试数据创建完成 ===");
    }

    /**
     * 测试单个菜单分配
     */
    @Test
    @Order(2)
    public void testAssignSingleMenu() {
        log.info("=== 开始测试单个菜单分配 ===");
        
        // 为用户类型1分配菜单1
        boolean result1 = userTypeMenuService.assignMenuToUserType(testUserTypeId1, testMenuId1);
        assertTrue(result1, "为用户类型1分配菜单1应该成功");
        
        // 验证分配结果
        boolean isAssigned = userTypeMenuService.isMenuAssignedToUserType(testUserTypeId1, testMenuId1);
        assertTrue(isAssigned, "菜单1应该已分配给用户类型1");
        
        // 重复分配同一菜单，应该失败
        boolean result2 = userTypeMenuService.assignMenuToUserType(testUserTypeId1, testMenuId1);
        assertFalse(result2, "重复分配同一菜单应该失败");
        
        log.info("=== 单个菜单分配测试完成 ===");
    }

    /**
     * 测试批量菜单分配
     */
    @Test
    @Order(3)
    public void testAssignMultipleMenus() {
        log.info("=== 开始测试批量菜单分配 ===");
        
        // 为用户类型1批量分配菜单
        List<String> menuIds = Arrays.asList(testMenuId1, testMenuId2, testMenuId3);
        boolean result = userTypeMenuService.assignMenusToUserType(testUserTypeId1, menuIds);
        assertTrue(result, "批量分配菜单应该成功");
        
        // 验证分配结果
        List<String> assignedMenuIds = userTypeMenuService.getMenuIdsByUserType(testUserTypeId1);
        assertEquals(3, assignedMenuIds.size(), "用户类型1应该有3个菜单");
        assertTrue(assignedMenuIds.contains(testMenuId1), "应该包含菜单1");
        assertTrue(assignedMenuIds.contains(testMenuId2), "应该包含菜单2");
        assertTrue(assignedMenuIds.contains(testMenuId3), "应该包含菜单3");
        
        // 统计菜单数量
        long menuCount = userTypeMenuService.countMenusByUserType(testUserTypeId1);
        assertEquals(3L, menuCount, "用户类型1的菜单数量应该是3");
        
        log.info("=== 批量菜单分配测试完成 ===");
    }

    /**
     * 测试移除菜单
     */
    @Test
    @Order(4)
    public void testRemoveMenu() {
        log.info("=== 开始测试移除菜单 ===");
        
        // 移除用户类型1的菜单1
        boolean result1 = userTypeMenuService.removeMenuFromUserType(testUserTypeId1, testMenuId1);
        assertTrue(result1, "移除菜单1应该成功");
        
        // 验证移除结果
        boolean isAssigned = userTypeMenuService.isMenuAssignedToUserType(testUserTypeId1, testMenuId1);
        assertFalse(isAssigned, "菜单1应该已从用户类型1移除");
        
        // 统计剩余菜单数量
        long menuCount = userTypeMenuService.countMenusByUserType(testUserTypeId1);
        assertEquals(2L, menuCount, "用户类型1的菜单数量应该是2");
        
        log.info("=== 移除菜单测试完成 ===");
    }

    /**
     * 测试查询功能
     */
    @Test
    @Order(5)
    public void testQueryFunctions() {
        log.info("=== 开始测试查询功能 ===");
        
        // 获取用户类型的菜单ID列表
        List<String> menuIds = userTypeMenuService.getMenuIdsByUserType(testUserTypeId1);
        assertEquals(2, menuIds.size(), "用户类型1应该有2个菜单");
        
        // 获取菜单的用户类型ID列表
        List<String> userTypeIds = userTypeMenuService.getUserTypeIdsByMenu(testMenuId2);
        assertEquals(1, userTypeIds.size(), "菜单2应该分配给1个用户类型");
        assertTrue(userTypeIds.contains(testUserTypeId1), "应该包含用户类型1");
        
        // 检查分配状态
        assertTrue(userTypeMenuService.isMenuAssignedToAnyUserType(testMenuId2), "菜单2应该被分配");
        assertTrue(userTypeMenuService.hasUserTypeAssignedAnyMenu(testUserTypeId1), "用户类型1应该有分配的菜单");
        assertFalse(userTypeMenuService.hasUserTypeAssignedAnyMenu(testUserTypeId2), "用户类型2应该没有分配的菜单");
        
        // 获取关联记录
        List<UserTypeMenu> userTypeMenus = userTypeMenuService.getUserTypeMenusByUserType(testUserTypeId1);
        assertEquals(2, userTypeMenus.size(), "用户类型1应该有2条关联记录");
        
        List<UserTypeMenu> menuUserTypes = userTypeMenuService.getUserTypeMenusByMenu(testMenuId2);
        assertEquals(1, menuUserTypes.size(), "菜单2应该有1条关联记录");
        
        log.info("=== 查询功能测试完成 ===");
    }

    /**
     * 测试复制菜单权限
     */
    @Test
    @Order(6)
    public void testCopyMenus() {
        log.info("=== 开始测试复制菜单权限 ===");
        
        // 复制用户类型1的菜单权限到用户类型2
        boolean result = userTypeMenuService.copyMenusFromUserType(testUserTypeId1, testUserTypeId2);
        assertTrue(result, "复制菜单权限应该成功");
        
        // 验证复制结果
        List<String> sourceMenuIds = userTypeMenuService.getMenuIdsByUserType(testUserTypeId1);
        List<String> targetMenuIds = userTypeMenuService.getMenuIdsByUserType(testUserTypeId2);
        assertEquals(sourceMenuIds.size(), targetMenuIds.size(), "目标用户类型的菜单数量应该与源用户类型相同");
        
        for (String menuId : sourceMenuIds) {
            assertTrue(targetMenuIds.contains(menuId), "目标用户类型应该包含源用户类型的所有菜单");
        }
        
        log.info("=== 复制菜单权限测试完成 ===");
    }

    /**
     * 测试批量删除
     */
    @Test
    @Order(7)
    public void testBatchDelete() {
        log.info("=== 开始测试批量删除 ===");
        
        // 获取所有关联记录
        List<UserTypeMenu> allUserTypeMenus = userTypeMenuService.getAllUserTypeMenus();
        assertFalse(allUserTypeMenus.isEmpty(), "应该有关联记录");
        
        // 提取部分ID进行批量删除
        List<String> idsToDelete = Arrays.asList(
                allUserTypeMenus.get(0).getId(),
                allUserTypeMenus.size() > 1 ? allUserTypeMenus.get(1).getId() : null
        );
        idsToDelete.removeIf(id -> id == null);
        
        int deletedCount = userTypeMenuService.batchDeleteUserTypeMenus(idsToDelete);
        assertTrue(deletedCount > 0, "应该成功删除至少1条记录");
        
        log.info("=== 批量删除测试完成 ===");
    }

    /**
     * 测试移除所有关联
     */
    @Test
    @Order(8)
    public void testRemoveAllAssociations() {
        log.info("=== 开始测试移除所有关联 ===");
        
        // 移除用户类型1的所有菜单
        int removedCount1 = userTypeMenuService.removeAllMenusFromUserType(testUserTypeId1);
        log.info("移除用户类型1的菜单数量: {}", removedCount1);
        
        // 验证移除结果
        assertFalse(userTypeMenuService.hasUserTypeAssignedAnyMenu(testUserTypeId1), 
                "用户类型1应该没有分配的菜单");
        
        // 移除菜单2的所有用户类型关联
        int removedCount2 = userTypeMenuService.removeAllUserTypesFromMenu(testMenuId2);
        log.info("移除菜单2的用户类型数量: {}", removedCount2);
        
        // 验证移除结果
        assertFalse(userTypeMenuService.isMenuAssignedToAnyUserType(testMenuId2), 
                "菜单2应该没有分配给任何用户类型");
        
        log.info("=== 移除所有关联测试完成 ===");
    }

    /**
     * 清理测试数据
     */
    @AfterAll
    public static void cleanupTestData(@Autowired UserTypeMenuService userTypeMenuService,
                                       @Autowired UserTypeService userTypeService,
                                       @Autowired MenuService menuService) {
        log.info("=== 开始清理测试数据 ===");
        
        // 清理所有用户类型菜单关联
        if (testUserTypeId1 != null) {
            userTypeMenuService.removeAllMenusFromUserType(testUserTypeId1);
        }
        if (testUserTypeId2 != null) {
            userTypeMenuService.removeAllMenusFromUserType(testUserTypeId2);
        }
        
        // 删除测试用户类型
        if (testUserTypeId1 != null) {
            userTypeService.deleteUserType(testUserTypeId1);
        }
        if (testUserTypeId2 != null) {
            userTypeService.deleteUserType(testUserTypeId2);
        }
        
        // 删除测试菜单
        if (testMenuId1 != null) {
            menuService.deleteMenu(testMenuId1);
        }
        if (testMenuId2 != null) {
            menuService.deleteMenu(testMenuId2);
        }
        if (testMenuId3 != null) {
            menuService.deleteMenu(testMenuId3);
        }
        
        log.info("=== 测试数据清理完成 ===");
    }
} 